<template>
  <div style="display: flex" class="offlineVideo">
    <div class="box1">
      <el-upload
        accept=".mp4"
        action="#"
        list-type="picture-card"
        :http-request="upload"
        :show-file-list="false"
      >
        <i class="el-icon-plus" v-show="form.videoUrl == ''"></i>
        <video
          :width="boxWidth"
          :height="boxHeight"
          v-show="form.videoUrl!= ''"
          :src="form.videoUrl"
          controls="controls"
        ></video>
        <span class="box1-delete">
        <span v-show="(form.videoUrl!= '')&&type==1" @click.stop="handleRemove">删除</span>
        <span v-show="(form.videoUrl!= '')&&type==2" @click="handleRemove">修改</span>
      </span>
      </el-upload>
      
    </div>
  </div>
</template>
  
  <script>
import ObsClient from "esdk-obs-browserjs/src/obs";
import { selectObsData } from "@/api/system/appupgrade";
export default {
  name: "uploadVideo",
  props: {
    type:{
        type:Number,
        default: 2 //1表示显示修改 2表示删除
    },
    boxWidth:{
        type:String,
        default:'200px'
    },
    boxHeight:{
        type:String,
        default:'148px'
    },
    value: [String, Object, Array],
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      videoLength: 1,
      form: {
        videoUrl: "",
      },
    };
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          this.form.videoUrl = val;
        } else {
          this.form.videoUrl = "";
          return "";
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    //删除
    handleRemove() {
      this.form.videoUrl = "";
      this.$emit("input", this.form.videoUrl);
    },
    //修改
    //上传视频
    upload(params) {
      console.log("erzi");
      this.$modal.loading("正在上传文件，请稍候...");
      selectObsData().then((response) => {
        if (response.code == 200) {
          this.obsInfo = response.result;
          this.obsClient = new ObsClient({
            access_key_id: this.obsInfo.ak, // 你的ak
            secret_access_key: this.obsInfo.sk, // 你的sk
            server: this.obsInfo.sslMode + "://" + this.obsInfo.endpoint, // 你的endPoint
          });
          this.params(params);
        }
      });
    },
    params(params) {
      console.log("jinru");
      let date = new Date();
      let that = this;
      this.obsClient.putObject(
        {
          Bucket: this.obsInfo.bucketName, // 桶名
          Key:
            this.obsInfo.active +
            `/${date.getFullYear()}/${date.getMonth() + 1}/` +
            `${date.getTime()}/` +
            params.file.name, // 路径 + 文件名
          SourceFile: params.file,
          ProgressCallback: this.callback,
        },
        function (err, result) {
          if (err) {
            that.$message.error("文件上传失败");
            that.$modal.closeLoading();
          } else {
            let url =
              that.obsInfo.sslMode +
              "://" +
              that.obsInfo.bucketName +
              "." +
              that.obsInfo.endpoint +
              "/" +
              that.obsInfo.active +
              `/${date.getFullYear()}/${date.getMonth() + 1}/` +
              `${date.getTime()}/` +
              params.file.name;
            that.form.videoUrl = url;
            that.$emit("input", that.form.videoUrl);
            that.$modal.closeLoading();
          }
        }
      );
    },
  },
};
</script>
  
  <style lang="scss" scoped>
  ::v-deep .el-upload--picture-card{
    width: v-bind(boxWidth) !important;
    height: v-bind(boxHeight)!important;
    display: flex;
    justify-content: center;
    align-items: center;
  }
.box1:hover .box1-delete {
  display: block;
}
.box1 {
  position: relative;
  &-delete {
    display: none;
    color: red;
    font-size: 18px !important;
    // top: -61px;
    // left: 162px;
    position: absolute;
    cursor: pointer;
  }
}
  
</style>