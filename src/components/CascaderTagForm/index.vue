<template>
    <div >
        <el-cascader ref="cascader" clearable class="region_cascader" :options="options" :props="defaultProps" @change="getValue"  v-model="list" collapse-tags :disabled='disabled'></el-cascader>
    </div>
</template>
<script>
import provincialLevel from '@/utils/provincialLevel.js'
import { getAreaTreeList} from "@/api/ffs/home.js";
export default {
    // 是否支持多选
    props:{
        value: [String, Object, Array],
        multiple:{
            type:Boolean,
            default:false
        },
        flag:{
            type:Boolean,
            default:true
        },
        disabled:{
          type:Boolean,
          default:false
        }
    }
    ,
    data() {
        return {
            defaultProps: { multiple: this.multiple, checkStrictly: false },
            provincialLevel: provincialLevel.result,
            options: [],
            list: [],
            listString: this.$store.state.user.user.areaDataRange
        }

    },
    watch:{
        value:{
            handler(){
                if(!this.value){
                    this.list=[]
                    this.setChildrenDisabled(this.options, false);
                }else{
                  this.list=[]

                  this.value.split(';').forEach(item=>{
                    let arr=[]
                    item.split(',').forEach(item1=>{
                      arr.push(item1)
                    })
                    this.list.push(arr)
                  })

                }
            }
        },
        list:{
          handler(n,o){

            console.log(n)
          },
        }
    },
    mounted() {
        // if(this.listString){
            this.loopArry()
        // }else{
            // this.getList()
        // }


    },
    methods: {
        // getList(){
        //     getAreaTreeList({}).then(res=>{
        //         if(res.code==200){
        //             this.options=res.result
        //         }
        //     })
        // },
        getValue(value) {
            if(this.multiple){
                this.selectValue(value)
                console.log(this.$refs.cascader)
                let nodes=this.$refs.cascader.getCheckedNodes()
                console.log('*******')
                console.log(nodes)
            }else{
                let temp=JSON.parse(JSON.stringify(value))
                if(this.flag){
                    if(value.length>0){
                        temp.unshift('0')
                    }
                }
                console.log(value)

                this.$emit("input",temp.toString())
            }

        },

        selectValue(value){
            let temp = JSON.parse(JSON.stringify(value));
            let result = []
            temp.map((item) => {
                // 长度为1  说明这个节点是顶级节点
                if (item.length == 1) {
                    result.push(item);
                } else {
                    if (result.length > 0) {
                        let flag = false;
                        for (let i = 0; i < result.length; i++) {
                            for (let j = 0; j < result[i].length; j++) {
                                if (item.indexOf(result[i][j]) == -1) {
                                    result.push(item);
                                    flag = true;
                                    break;
                                }
                            }
                            if (flag) {
                                break;
                            }
                        }
                    } else {
                        result.push(item);
                    }
                }
            });
            this.list = result
            let selectArr = [];
            this.list.map((item) => {
                selectArr.push(item[item.length - 1]);
            });
            if (selectArr.length > 0) {
                this.updateTreeDataStatus(selectArr);
            } else {
                this.setChildrenDisabled(this.options, false);
            }
            this.handelList()
        },

         // 处理数据
        handelList(){
            let listString=JSON.parse(JSON.stringify(this.list))
            if(this.flag){
                listString.map(item=>{
                    item.unshift('0')
                })
            }

            this.$emit("input",listString.join(';'))
        },

        // 更新树形数据的状态
        updateTreeDataStatus(data) {
            let treeData = this.options;
            // 直接把所有节点置为可选，避免父节点取消选择了，子节点还不可选的情况
            this.setChildrenDisabled(treeData, false);
            // 根据上面选择的节点，把不可选的子节点及所有后代节点置为不可选
            data.map((item) => {
                let sub = this.filter(treeData, (node) => {
                    return node.value == item;
                });
                if (sub?.children) {
                    const subChild = this.setChildrenDisabled(sub.children, true);
                    this.$set(sub, "children", subChild);
                }
            });
        },
        //递归设置子级是否可以选择
        setChildrenDisabled(tree, status) {
            tree.map((item) => {
                this.$set(item, "disabled", status);
                if (item.children) {
                    this.setChildrenDisabled(item.children, status);
                }
            });
            return tree;

        },

        filter(treeList, callback) {
            const queue = [...treeList];
            while (queue.length > 0) {
                const cur = queue.shift();
                if (callback(cur)) {
                    return cur;
                }
                if (cur.children && cur.children.length > 0) {
                    for (const item of cur.children) {
                        queue.push(item);
                    }
                }
            }
        },
        //处理数据
        handelString() {
            return this.listString.split(';')||[]

        },
        //循环数据
        loopArry() {
            let arry = this.handelString()
            arry.forEach(item => {
                let it = item.split(',')
                this.provincialLevel.map(item1 => {
                    // 选择省下所有数据
                    if (it.length == 2 && it[1] == item1.value) {
                        this.options.push(item1)
                    }

                    //选择市级所有数据
                    if (it.length == 3) {
                        let provincial = item1
                        let provinceIndex = this.provinceIndexFn(it[1])

                        if ((provinceIndex == -1) && (it[1] == item1.value)) {
                            let obj = JSON.parse(JSON.stringify(item1))
                            obj.children = []
                            provincial.children.map(pro => {
                               console.log(pro.value,"数据",it[2]);
                                if (pro.value == it[2]) {
                                    console.log(pro,"444444");
                                    obj.children.push(pro)
                                }
                            })


                            this.options.push(obj)
                        }

                        if ((provinceIndex != -1) && (it[1] == item1.value)) {
                            provincial.children.map(pro => {
                                if (pro.value == it[2]) {
                                    this.options[provinceIndex].children.push(pro)
                                }
                            })
                        }
                    }
                    //县级数据
                    if (it.length == 4) {
                        let provincial = item1//省级数据
                        let provinceIndex = this.provinceIndexFn(it[1])
                        if ((provinceIndex == -1) && (it[1] == item1.value)) {
                            let objP = JSON.parse(JSON.stringify(item1))
                            objP.children = []
                            provincial.children.some(pro => {
                                let city = pro//市级数据
                                // let cityIndex=this.cityIndex(objP.children,t[2])
                                if (pro.value == it[2]) {
                                    let objCity = JSON.parse(JSON.stringify(pro))
                                    objCity.children = []
                                    city.children.some(coun => {
                                        if (coun.value == it[3]) {
                                            objCity.children.push(coun)
                                        }
                                    })
                                    objP.children.push(objCity)
                                }
                            })
                            this.options.push(objP)
                        }
                        if (provinceIndex != -1 && (it[1] == item1.value)) {

                            provincial.children.map(pro => {
                                let cityIndex = this.cityIndexFn(provinceIndex, it[2])
                                let cityList = pro
                                if (cityIndex == -1 && it[2] == pro.value) {
                                    let objCity = JSON.parse(JSON.stringify(pro))
                                    objCity.children = []
                                    cityList.children.some(coun => {
                                        if (coun.value == it[3]) {
                                            objCity.children.push(coun)
                                        }
                                    })
                                    this.options[provinceIndex].children.push(objCity)
                                }
                                if (cityIndex != -1 && (it[2] == pro.value)) {
                                    cityList.children.some(coun => {
                                        if (coun.value == it[3]) {
                                            this.options[provinceIndex].children[cityIndex].children.push(coun)
                                        }
                                    })
                                }
                            })
                        }
                    }
                })
            });
            console.log('：', this.options);
        },

        //返回省级数据的索引
        provinceIndexFn(value) {
            return this.options.findIndex(item => {
                return item.value == value
            })
        },
        //市级数据索引
        cityIndexFn(index, value) {
            return this.options[index].children.findIndex(item => {
                return item.value == value
            })
        },
    },
};
</script>
<style lang="scss" >
    .in-checked-path .el-checkbox__input .el-checkbox__inner::before {
        content: "";
        position: absolute;
        display: block;
        background-color: #fff;
        ;
        height: 2px;
        transform: scale(0.5);
        left: 0;
        right: 0;
        top: 5px;
    }

    .in-checked-path .is-checked .el-checkbox__inner::before {
        background-color: #5672FA;
        ;
    }

    .in-checked-path .el-checkbox__input .el-checkbox__inner {
        background: #5672FA;
    }
</style>
<style scoped lang="scss">
::v-deep .el-cascader__tags{
    flex-wrap:nowrap;
}
::v-deep .el-cascader .el-input{
  min-width: 320px;
}
</style>
