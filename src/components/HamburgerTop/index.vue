<template>
  <div style="padding: 5px 20px;" @click="toggleClick">
    <div v-show="sidebar.opened" class="left">
        <i class="el-icon-d-arrow-left" ></i>
    </div>
    <i class="el-icon-d-arrow-right" v-show="!sidebar.opened"></i>
  </div>
</template>
  
  <script>
import { mapState } from "vuex";
export default {
  name: "Hamburger",
  props: {
    isActive: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    ...mapState({
      sidebar: (state) => state.app.sidebar,
    }),
  },
  methods: {
    toggleClick() {
      console.log(this.sidebar, this.isActive);
      this.$emit("toggleClick");
    },
  },
};
</script>
  <style scoped>
  .left{
   display: flex;
   justify-content: flex-end;
  }
</style>
  