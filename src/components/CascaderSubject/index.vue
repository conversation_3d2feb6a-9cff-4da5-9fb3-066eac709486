<template>
  <div >
    <el-cascader clearable class="region_cascader" ref="cascader" :options="options" :props="defaultProps" @change="getValue" v-model="list" collapse-tags></el-cascader>
  </div>
</template>
<script>
import { supervisingSubjectList } from "@/api/ffs/supervisionSheet/livingSupervisionApi.js";
export default {
  // 是否支持多选
  props:{
    value: [String, Object, Array],
    multiple:{
      type:Boolean,
      default:false
    },
    flag:{
      type:Boolean,
      default:true
    }
  }
  ,
  data() {
    return {
      defaultProps: { multiple: this.multiple, checkStrictly: true, },
      options: [],
      list: [],
    }

  },

  mounted() {
    this.getSuperList()
  },
  methods:{
    getSuperList(){
      supervisingSubjectList({}).then(res => {
        console.log('resssss++++++')
        console.log(res)
        let list = res.result || [];
        const treeData = list.map(item => this.generateTree(item));
        console.log(treeData)
        this.options = treeData
      })
    },
    generateTree(nodeList) {
      let leaf = false;
      const treeNode = {
        value: nodeList.tenantId,
        label: nodeList.companyName,
        companyType: nodeList.companyType,
      };

      if (
        nodeList.sysEnterpriseList &&
        nodeList.sysEnterpriseList.length > 0 &&
        nodeList.sysEnterpriseList.some(childNode => childNode.level >= 1)
      ) {
        treeNode.children = nodeList.sysEnterpriseList
          .filter(childNode => childNode.level >= 1)
          .map(childNode => {
            const childTree = this.generateTree(childNode);
            childTree.leaf = true;
            return childTree;
          });
      }

      return treeNode;
    },
    getValue(value){
      console.log(value)
      let nodes = this.$refs.cascader.getCheckedNodes()[0]
      console.log(nodes)
      const supervisSubjectTypt = nodes.data.companyType;
      const supervisSubjectTyptId = nodes.data.value;
      let superveData = {
        supervisSubjectTypt, supervisSubjectTyptId
      }
      this.$emit("getValue", JSON.stringify(superveData));
      console.log(JSON.stringify(superveData))
    },
    clearValue(){
      this.list = []
    }
  }
};
</script>
<style lang="scss" >

</style>
