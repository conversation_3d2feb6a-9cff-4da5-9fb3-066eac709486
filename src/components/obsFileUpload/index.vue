<template>
  <div @click.once="clickUp">
    <el-upload class="upload-demo" drag action="#" 
    :multiple="multiple"
    :http-request="upload" 
    :show-file-list="false"
    :before-upload="handleBeforeUpload"
    >
      <i class="el-icon-upload"></i>
      <div class="el-upload__text">
        <em>点击上传</em>
      </div>
    </el-upload>
     <!-- 文件列表 -->
     <transition-group  name="el-fade-in-linear" tag="div">
      <div
      class="filelist"
        v-if="showFileLsit"
        :key="index + 1"
        v-for="(file, index) in fileUrl"
      >
      <!-- !!!_ 在子元素上，使用索引作为key的值，则相当于没有使用key -->
        <el-link :href="`${file.url}`" :underline="false" target="_blank">
          <span class="el-icon-document">{{ (file.name) }}</span>
        </el-link>
        <div class="delete">
          <span  @click="handleDelete(index)">删除</span>
        </div>
      </div>
    </transition-group>
  </div>
</template>

<script>
import ObsClient from "esdk-obs-browserjs/src/obs";
import { selectObsData } from "@/api/system/appupgrade";
export default {
  name: "EastMindAdminUiIndex",
props:{
    showFileLsit:{
        type:Boolean,
        default:true
    },
    fileType: {
      type: Array,
      default: () => ["doc", "xls", "ppt", "txt", "pdf","json",'jpg'],
    },
    multiple:{
        type:Boolean,
        default:true
    }
},
  data() {
    return {
      obsInfo: {},
      obsClient:null,
      fileUrl:[],
    };
  },

  mounted() {},

  methods: {
    clickUp() {
      selectObsData().then((response) => {
        if(response.code==200){
            this.obsInfo = response.result;
            this.obsClient = new ObsClient({
              access_key_id: this.obsInfo.ak, // 你的ak
              secret_access_key: this.obsInfo.sk, // 你的sk
              server: this.obsInfo.sslMode + "://" + this.obsInfo.endpoint // 你的endPoint
            });
        }
      });
    },
    upload(params){
      
         this.params(params)
 
    },
      async  params(params) {
            let date=new Date()
            let that=this
            this.obsClient.putObject({
              Bucket: this.obsInfo.bucketName, // 桶名
              Key:  this.obsInfo.active +`/${date.getFullYear()}/${date.getMonth() +1 }/`+`${date.getTime()}/` + params.file.name, // 路径 + 文件名
              SourceFile: params.file,
              ProgressCallback: this.callback
            }, function (err, result) {
              if (err) {
                that.$message.error('文件上传失败')
                that.$modal.closeLoading();
              } else {
                let url= that.obsInfo.sslMode + "://" + that.obsInfo.bucketName + "." + that.obsInfo.endpoint + "/" + that.obsInfo.active +`/${date.getFullYear()}/${date.getMonth() +1 }/`+`${date.getTime()}/` + params.file.name;
                that.fileUrl.push({name:params.file.name,url:url})
            
                 that.$emit("input",  that.fileUrl);
                 that.$modal.closeLoading();
              }
            })
            
        },

    //文件上传钱校验
    handleBeforeUpload(file){
        // 校检文件类型
      if (this.fileType) {
        let fileExtension = "";
        if (file.name.lastIndexOf(".") > -1) {
          fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
        }
        const isTypeOk = this.fileType.some((type) => {
          if (file.type.indexOf(type) > -1) return true;
          if (fileExtension && fileExtension.indexOf(type) > -1) return true;
          return false;
        });
        if (!isTypeOk) {
          this.$modal.msgError(
            `文件格式不正确, 请上传${this.fileType.join("/")}格式文件!`
          );
          return false;
        }
      }
      this.$modal.loading("正在上传文件，请稍候...");
      this.number++;

      return true;
    },
     //删除   
     handleDelete(index){
        this.fileUrl.splice(index,1)
        this.$emit("input",  this.fileUrl);
     }
  },
};
</script>

<style lang="scss" scoped>
.filelist{
    display: flex;
    justify-content: space-between;
    .delete{
        cursor:pointer;
        color:red;
        padding: 2px 0;
    }
}
</style>