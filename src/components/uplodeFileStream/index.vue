
<template>
  <!-- 直接上传一个文件流给服务端-->
  <div class="main" @click="validateform">
    <el-upload
      action="hh"
      :http-request="uploadPost"
      :before-upload="handleBeforeUpload"
      :disabled="disabled"
      :data="formData"
      :limit='limit'
    >
      <div class="fc upload-box">
        <i class="el-icon-upload"></i>
        <span class="el-icon-txt">{{uploadName}}</span>
      </div>
    </el-upload>
  </div>
</template>

<script>
import { uploadFile } from "@/utils/request";
export default {
  name: "uplodeFileStream",
  props: {
    // 值
    value: [String, Object, Array],
     // 最大允许上传个数
    limit: {
      type: Number,
      default: 1,
    },
    // 大小限制(MB)
    fileSize: {
      type: Number,
      default: 5,
    },
    // 文件类型, 例如['png', 'jpg', 'jpeg']
    fileType: {
      type: Array,
      default: () => ["doc", "xls", "ppt", "txt", "pdf"],
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false,
    },
    // 表单校验是否通过
    adopt: {
      type: Boolean,
      default: true,
    },

    uploadName: {
      type: String,
      default: "上传文件",
    },
    // 上传时附带的额外参数
    formData: {
      type: Object,
      default: {},
    },

    //上传文件流接口
    uploadApi: {
      type: Function,
      default: "",
    },
  },
  data() {
    return {};
  },
  created() {},
  mounted() {},

  methods: {
    validateform() {
      this.$emit("validateform");
    },
    // 上传前校检格式和大小
    handleBeforeUpload(file) {
      console.log("adopt_adopt:", this.adopt);
      if (!this.adopt) return false;
      if (!this.uploadApi) {
        this.$message({
          message: "上传文件API:uploadApi属性必传",
          type: "error",
        });
        return false;
      }

      // 校检文件类型
      if (this.fileType) {
        let fileExtension = "";
        if (file.name.lastIndexOf(".") > -1) {
          fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
        }
        const isTypeOk = this.fileType.some((type) => {
          if (file.type.indexOf(type) > -1) return true;
          if (fileExtension && fileExtension.indexOf(type) > -1) return true;
          return false;
        });
        if (!isTypeOk) {
          this.$modal.msgError(
            `文件格式不正确, 请上传${this.fileType.join("/")}格式文件!`
          );
          return false;
        }
      }
      this.$modal.loading("正在上传文件，请稍候...");
      setTimeout(() => {
        this.$modal.closeLoading();
      }, 8000);
      return true;
    },
    async uploadPost(item) {
      let form = new FormData();
      form.append("file", item.file);
      for (const key in this.formData) {
        form.append(key, this.formData[key]);
      }

      console.log("form上传：", form);

      let loadUrl = this.uploadApi();
      // 文件上传必须设置http请求头信息
      const resUlt = await uploadFile(loadUrl, form);
      this.$modal.closeLoading();
      this.$emit("refresh");
    },
  },
};
</script>
<style  scoped>
.upload-box {
  border: 1px dashed #e4e7ed;
  border-radius: 10px;
  padding: 30px;
}
.upload-box:hover {
  border: 1px dashed #1890ff;
}
.el-icon-upload {
  font-size: 30px;
  margin-right: 10px;
  color: #1890ff;
}
.el-icon-txt {
  display: block;
  font-size: 16px;
}
</style>