<template>
  <el-breadcrumb class="app-breadcrumb " :class="{
    'mes-breadcrumb': levelList.length > 0
  }" separator="/">
    <transition-group name="breadcrumb">
      <el-breadcrumb-item v-for="(item,index) in levelList" :key="item.path" class="mt10">
        <span v-if="item.redirect === 'noRedirect' || index == levelList.length - 1" class="no-redirect">{{ item.meta.title }}</span>
        <a v-else @click.prevent="handleLink(item)">{{ item.meta.title }}</a>
      </el-breadcrumb-item>
    </transition-group>
  </el-breadcrumb>
</template>

<script>
export default {
  data() {
    return {
      levelList: null
    }
  },
  watch: {
    $route(route) {
      // if you go to the redirect page, do not update the breadcrumbs
      // if (route.path.startsWith('/redirect/')) {
      //   return
      // }
      this.getBreadcrumb()
    }
  },
  created() {
    this.getBreadcrumb()
  },
  methods: {
    getBreadcrumb() {
      // only show routes with meta.title
      let matched = this.$route.matched.filter(item => item.name != 'Mesapp' &&item.name != 'Index' && item.meta && item.meta.title)
      const first = matched[0]
      if (first && first.name != 'Index' && !this.isDashboard(first)) {
        matched = [{ path: '/index', meta: { title: '首页' }}].concat(matched)
      }
      this.levelList = matched
    },
    isDashboard(route) {
      const name = route && route.name
      if (!name) {
        return false
      }
      return name.trim() === 'Index'
    },
    handleLink(item) {
      const { redirect, path } = item
      if (redirect) {
        this.$router.push(redirect)
        return
      }
      this.$router.push(path)
    }
  }
}
</script>

<style lang="scss" scoped>
.app-breadcrumb.el-breadcrumb {
  font-size: 14px;
  padding-left: 20px;
  .no-redirect {
    color: #97a8be;
    cursor: text;
  }
}


</style>
<style lang="scss">
.mes-breadcrumb{
  background: #F5F6FA !important;
  .el-breadcrumb__item:last-child .el-breadcrumb__inner, 
  .el-breadcrumb__item:last-child .el-breadcrumb__inner a,
  .el-breadcrumb__inner a, .el-breadcrumb__inner.is-link,
   .el-breadcrumb__item:last-child .el-breadcrumb__inner a:hover,
    .el-breadcrumb__item:last-child .el-breadcrumb__inner:hover{
    color: #999999;
  }
  .el-breadcrumb__item .no-redirect{
    color: #1F2026 !important;
  }
  .el-breadcrumb__item{
    font-family: Source Han Sans CN-Medium, Source Han Sans CN;
    font-weight: 500;
    color: #999999;
    line-height: 18px;
  }
}
</style>