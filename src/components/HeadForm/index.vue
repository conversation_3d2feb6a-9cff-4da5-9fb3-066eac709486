<template>
  <div>
    <el-card class="mb10 form_box" shadow="never" ref="formBox">
      <el-form :model='queryParams'  ref="queryForm" size="small" :inline="true" label-width="140px"  >
        <el-row class="form_row">
          <el-col class="form_col">
            <slot></slot>
          </el-col>
        </el-row>
        <el-row style="margin-left: 140px;">
          <el-col>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              <template v-if="toggleSearchDom">
                <el-button type="text" @click="packUp">
                  {{ toggleSearchStatus ? '收起' : '展开' }}
                  <i :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"></i>
                </el-button>
              </template>

            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
  </div>

</template>

<script>
  import {
    tableUi
  } from "@/utils/mixin/tableUi.js";
  export default {
    mixins: [tableUi],
    props: {

      queryParams: {
        type: Object,
        default: () =>{},
      },

    },
    data() {
      return {

      };
    },
    watch: {

    },
    computed: {

    },
    methods: {
      handleQuery(){
        this.$parent.handleQuery()
      },
      resetQuery(){
        this.resetForm("queryForm");

        this.$parent.resetQuery()
      },


    }
  };

</script>

<style>
</style>
