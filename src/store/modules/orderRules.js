/**
 *
 * @type {{mutations: {SET_RULES: orderRules.mutations.SET_RULES}, state: {rules: {}}, actions: {}}}
 * const parsms = {
 *         name: 12,
 *         age: 23,
 *         haha: 'haha'
 *       }
 *       this.$store.commit('SET_RULES', params)
 *       console.log(this.$store.state)
 *
 *        @actions
 *        const params = {
 *         name: 12,
 *         age: 23,
 *         haha: 'actions'
 *       }
 *       this.$store.dispatch('setRules', params).then(() => {
 *         console.log('javaScript')
 *       })
 *       console.log(this.$store.state.orderRules)
 *       return;
 */

const orderRules = {
  state: {
    rules: {},
    templeteIdObj: {}
  },

  mutations: {
    SET_RULES: (state, rules) => {
      state.rules = rules;
    },
    SET_TEMPLATEOBJ: (state, templeteIdObj) => {
      state.templeteIdObj = templeteIdObj;
    }
  },

  actions: {
    async setRules(content, params) {
      content.commit("SET_RULES", await params);
    },
  },
};

export default orderRules;
