import defaultSettings from '@/settings'

const { sideTheme, showSettings, topNav, tagsView, fixedHeader, sidebarLogo, dynamicTitle } = defaultSettings

const storageSetting = JSON.parse(localStorage.getItem('layout-setting')) || ''
const state = {
  title: '',
  theme: '#5672FA',//storageSetting.theme || '#409EFF',
  themeRed: '#F85300',//storageSetting.theme || '#409EFF',
  sideTheme: 'light',//storageSetting.sideTheme || sideTheme,
  showSettings: showSettings,
  topNav: false,//storageSetting.topNav === undefined ? topNav : storageSetting.topNav,
  tagsView: true,//storageSetting.tagsView === undefined ? tagsView : storageSetting.tagsView,
  fixedHeader: true,//storageSetting.fixedHeader === undefined ? fixedHeader : storageSetting.fixedHeader,
  sidebarLogo: true,//storageSetting.sidebarLogo === undefined ? sidebarLogo : storageSetting.sidebarLogo,
  dynamicTitle: true //storageSetting.dynamicTitle === undefined ? dynamicTitle : storageSetting.dynamicTitle
}
const mutations = {
  CHANGE_SETTING: (state, { key, value }) => {
    if (state.hasOwnProperty(key)) {
      state[key] = value
    }
  },
  SET_THEME:(state,value)=>{
    state.theme=value
  }
}

const actions = {
  // 修改布局设置
  changeSetting({ commit }, data) {
    commit('CHANGE_SETTING', data)
  },
  // 设置网页标题
  setTitle({ commit }, title) {
    state.title = title
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

