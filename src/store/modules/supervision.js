import {
  superviseSave,
  superviseSaveDraft,
} from "@/api/ffs/supervisionSheet/livingSupervisionApi.js";

import { superviseSave as farmingSuperviseSave,
  superviseSaveDraft as farmingSuperviseSaveDraft }  from '@/api/ffs/farmingSupervisionSheet/livingSupervisionApi'

var dayjs = require("dayjs");
const supervision = {
  state: {
    addFrom: {
      superviseId: "", //监管单id
      applyPhone: "", //被监管人手机号
      creditInvestigationId: "", //调研信息id
      intentionListId: "", //意向单id
      provinceId: "", //省id
      provinceName: "", //省名称
      cityId: "", //市id
      cityName: "", //市名称
      countyId: "", //区县id
      countyName: "", //区县名称
      address: "", //详细地址
      addressLoglat: "", //被监管方经纬度
      fileIntentionList: "", //意向表附件
      fileCreditInvestigation: "", //调研表附件
      regulatoryScheme: "", //监管方案附件
      subjectType: "", //被监管方类型：1企业，2个人
      subjectPhone: "", //被监管方手机号
      applyName: "", //被监管方名称
      bankId: "", //委托方ID
      bankName: "", //委托方昵称名称

      contractNo: "", //合同编号
      superviseAmount: "", //授信金额（万分
      superviseAmountReceive: "", // 用信金额（万分）
      superviseLimit: "", //监管期限（月）
      superviseStart: "", //监管开始时间
      superviseEnd: "", //监管结束时间
      superviseServiceRate: "", //监管服务费率
      superviseServiceFee: "", //监管服务费（分）
      settlementWay: "", //结算方式：1年付，2半年付，3季付
      fileTripleAgreement: "", //三方协议文件
      fileSuperviseContract: "", //监管合同文件
      fileVaccineRecord: "", //防疫记录文件
      fileInsurance: "", //保险单文件
      fileGazhaTestify: "", //嘎查证明文件
      fileRemark:'',//附件备注
      serviceFeeType: 1, //服务费类型 0，包干；1，非包干
      dayServiceFee: "", //日监管服务费

      supervisorId: "", //监管员id
      supervisorName: "", //监管员名称
      supervisorPhone: "", //监管员联系方式
      loanOfficerId: "", //信贷员id
      loanOfficerName: "", //信贷员名称
      loanOfficerPhone: "", //信贷员手机号
      insuranceCompanyName: "", //保险公司名称
      insuranceCompanyId:'',//保险公司id
      livestockList: [], //监管单关联活畜列表
      satisticsList: [], //监管单关联活畜统计列表
      noEarTagSatisticsList:[],//无耳标

      superviseType: "", //监管类型：1活体，2仓单
      fileOther: "", //其他文件
      fileLoan: "", //贷款单文件
      fileLeaseContract: "", //租赁合同文件
      safetyValueRate: "", //安全货值比例
      storehouseList: [], //仓库信息
      commodityList: [], //商品信息
          earlyFlag: '',//0 不预警 1 要预警
          earlySendUser: [],//预警用户id
          earlySendUserStr: [],//预警用户
          offlineVideoUrlStr: '',
          superviseWay:'',
          haveEartag:1,//有无耳标//监管方式
          penName: '',
          penId: ''

    },
  },

  mutations: {
    SET_ADD_FROM: (state, data) => {
      state.addFrom = data;
    },

    SET_ADD_FROM_SUPERVIS_TYPE: (state, data) => {
      state.addFrom.superviseType = data;
    },

    RESET_ADD_FROM: (state) => {
      state.addFrom = {
        superviseId: "", //监管单id
        applyPhone: "", //被监管人手机号
        creditInvestigationId: "", //调研信息id
        intentionListId: "", //意向单id
        provinceId: "", //省id
        provinceName: "", //省名称
        cityId: "", //市id
        cityName: "", //市名称
        countyId: "", //区县id
        countyName: "", //区县名称
        address: "", //详细地址
        addressLoglat: "", //被监管方经纬度
        fileIntentionList: "", //意向表附件
        fileCreditInvestigation: "", //调研表附件
        regulatoryScheme: "", //监管方案附件
        fileRemark:'',//附件备注
        contractNo: "", //合同编号
        superviseAmount: "", // "授信金额（万）
        superviseAmountReceive: "", // 用信金额（万分）
        superviseLimit: "", //监管期限（月）
        superviseStart: "", //监管开始时间
        superviseEnd: "", //监管结束时间
        superviseServiceRate: "", //监管服务费率
        superviseServiceFee: "", //监管服务费（分）
        settlementWay: "", //结算方式：1年付，2半年付，3季付
        fileTripleAgreement: "", //三方协议文件
        fileSuperviseContract: "", //监管合同文件
        fileVaccineRecord: "", //防疫记录文件
        fileInsurance: "", //保险单文件
        fileGazhaTestify: "", //嘎查证明文件
        serviceFeeType: 1, //服务费类型 0，包干；1，非包干
        dayServiceFee: "", //日监管服务费

        supervisorId: "", //监管员id
        supervisorName: "", //监管员名称
        supervisorPhone: "", //监管员联系方式
        loanOfficerId: "", //信贷员id
        loanOfficerName: "", //信贷员名称
        loanOfficerPhone: "", //信贷员手机号
        insuranceCompanyName: "", //保险公司名称
        insuranceCompanyId:'',//保险公司id
        livestockList: [], //监管单关联活畜列表
        satisticsList: [], //监管单关联活畜统计列表
        noEarTagSatisticsList:[],//无耳标
        superviseType: "", //监管类型：1活体，2仓单
        fileOther: "", //其他文件
        fileLoan: "", //贷款单文件
        fileLeaseContract: "", //租赁合同文件
        storehouseList: [], //仓库信息---货区列表
        commodityList: [], //商品信息
        subjectType: "", //被监管方类型：1企业，2个人
        subjectPhone: "", //被监管方手机号
        subjectName: "", //被监管方名称
          earlyFlag: '',//0 不预警 1 要预警
          earlySendUser: [],//预警用户id
          earlySendUserStr: [],//预警用户
          offlineVideoUrlStr: '',
          superviseWay:'',//监管方式
          haveEartag:1,//有无耳标//监管方式
          penName: '',
          penId: ''
      };
    },
  },

  actions: {
    // 创建监管单--提交
    submitSuperviseSave({ commit, state }) {
      const mobielRex = /^1[3456789]\d{9}$/;
      if (!mobielRex.test(state.addFrom.loanOfficerPhone)) {
        state.addFrom.loanOfficerPhone = "";
      }
      if (!mobielRex.test(state.addFrom.supervisorPhone)) {
        state.addFrom.supervisorPhone = "";
      }
      if (!state.addFrom.intentionListId) {
        state.addFrom.flag = 1; //flag = 1  直接创建的时候加上，选择意向单不要传
      }

      if (
          state.addFrom.superviseType == 1 && (!state.addFrom.superviseAmountReceive || state.addFrom.superviseAmountReceive == 0)
      ) {
        state.addFrom.superviseAmountReceive = state.addFrom.superviseAmount;
      }
      state.addFrom.superviseStart=dayjs(state.addFrom.superviseStart).format('YYYY-MM-DD');

      return new Promise((resolve, reject) => {
        if (state.addFrom.creditInvestigationId) {
          delete state.addFrom.creditInvestigationId;
        }
          let obj = JSON.parse(JSON.stringify(state.addFrom))
          obj.earlySendUser = obj.earlySendUser.toString()
          obj.earlySendUserStr = JSON.stringify(obj.earlySendUserStr)
          superviseSave(obj)
          .then((res) => {
            resolve(res);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    // 创建监管单--保存草稿
    superviseSaveDraft({ commit, state }) {
      const mobielRex = /^1[3456789]\d{9}$/;
        if (state.addFrom.superviseStart) {
            state.addFrom.superviseStart = dayjs(state.addFrom.superviseStart).format('YYYY-MM-DD');
        }
      if (!mobielRex.test(state.addFrom.loanOfficerPhone)) {
        state.addFrom.loanOfficerPhone = "";
      }
      if (!mobielRex.test(state.addFrom.supervisorPhone)) {
        state.addFrom.supervisorPhone = "";
      }

      if (!state.addFrom.intentionListId) {
        state.addFrom.flag = 1;
      }

      if (
        state.addFrom.superviseType == 1 &&
        !state.addFrom.superviseAmountReceive
      ) {
        state.addFrom.superviseAmountReceive = 0;
      }

      return new Promise((resolve, reject) => {
        if (state.addFrom.creditInvestigationId) {
          delete state.addFrom.creditInvestigationId;
        }
        if (
          state.addFrom.superviseAmount == "" ||
          state.addFrom.superviseAmount < 0
        ) {
          state.addFrom.superviseAmount = 0;
        }
        if (
          state.addFrom.superviseServiceFee == "" ||
          state.addFrom.superviseServiceFee < 0
        ) {
          state.addFrom.superviseServiceFee = 0;
        }
          let obj = JSON.parse(JSON.stringify(state.addFrom))
          obj.earlySendUser = obj.earlySendUser.toString()
          obj.earlySendUserStr = JSON.stringify(obj.earlySendUserStr)
          console.log(state.addFrom,"shju")
          superviseSaveDraft(obj)
          .then((res) => {
            resolve(res);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },

    // 创建监管单--提交(农业)
    submitFarmingSuperviseSave({ commit, state }) {
      const mobielRex = /^1[3456789]\d{9}$/;
      if (!mobielRex.test(state.addFrom.loanOfficerPhone)) {
        state.addFrom.loanOfficerPhone = "";
      }
      if (!mobielRex.test(state.addFrom.supervisorPhone)) {
        state.addFrom.supervisorPhone = "";
      }
      if (!state.addFrom.intentionListId) {
        state.addFrom.flag = 1; //flag = 1  直接创建的时候加上，选择意向单不要传
      }

      if (
          state.addFrom.superviseType == 1 && (!state.addFrom.superviseAmountReceive || state.addFrom.superviseAmountReceive == 0)
      ) {
        state.addFrom.superviseAmountReceive = state.addFrom.superviseAmount;
      }
      state.addFrom.superviseStart=dayjs(state.addFrom.superviseStart).format('YYYY-MM-DD');

      return new Promise((resolve, reject) => {
        if (state.addFrom.creditInvestigationId) {
          delete state.addFrom.creditInvestigationId;
        }
          let obj = JSON.parse(JSON.stringify(state.addFrom))
          obj.earlySendUser = obj.earlySendUser.toString()
          obj.earlySendUserStr = JSON.stringify(obj.earlySendUserStr)
          farmingSuperviseSave(obj)
          .then((res) => {
            resolve(res);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },

     // 创建监管单--保存草稿（农业）
     superviseFarmingSaveDraft({ commit, state }) {
      const mobielRex = /^1[3456789]\d{9}$/;
        if (state.addFrom.superviseStart) {
            state.addFrom.superviseStart = dayjs(state.addFrom.superviseStart).format('YYYY-MM-DD');
        }
      if (!mobielRex.test(state.addFrom.loanOfficerPhone)) {
        state.addFrom.loanOfficerPhone = "";
      }
      if (!mobielRex.test(state.addFrom.supervisorPhone)) {
        state.addFrom.supervisorPhone = "";
      }

      if (!state.addFrom.intentionListId) {
        state.addFrom.flag = 1;
      }

      if (
        state.addFrom.superviseType == 1 &&
        !state.addFrom.superviseAmountReceive
      ) {
        state.addFrom.superviseAmountReceive = 0;
      }

      return new Promise((resolve, reject) => {
        if (state.addFrom.creditInvestigationId) {
          delete state.addFrom.creditInvestigationId;
        }
        if (
          state.addFrom.superviseAmount == "" ||
          state.addFrom.superviseAmount < 0
        ) {
          state.addFrom.superviseAmount = 0;
        }
        if (
          state.addFrom.superviseServiceFee == "" ||
          state.addFrom.superviseServiceFee < 0
        ) {
          state.addFrom.superviseServiceFee = 0;
        }
          let obj = JSON.parse(JSON.stringify(state.addFrom))
          obj.earlySendUser = obj.earlySendUser.toString()
          obj.earlySendUserStr = JSON.stringify(obj.earlySendUserStr)
          console.log(state.addFrom,"shju")
          farmingSuperviseSaveDraft(obj)
          .then((res) => {
            resolve(res);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },

  },
};

export default supervision;
