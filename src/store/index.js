import Vue from 'vue'
import Vuex from 'vuex'
import app from './modules/app'
import user from './modules/user'
import tagsView from './modules/tagsView'
import permission from './modules/permission'
import supervision from './modules/supervision'
import settings from './modules/settings'
import orderRules from "./modules/orderRules";
import getters from './getters'

Vue.use(Vuex)

const store = new Vuex.Store({
  modules: {
    app,
    user,
    tagsView,
    permission,
    supervision,
    settings,
    orderRules
  },
  getters
})

export default store
