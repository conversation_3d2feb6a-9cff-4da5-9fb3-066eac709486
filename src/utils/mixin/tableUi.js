export const tableUi = {
    data() {
        return {
            tableHeight: 0,//表格高度
            toggleSearchStatus:false, //展开收起
            toggleSearchDom:true,//是否出现展开收起
            regionDom:true,
        }
    },
    mounted() {
        // 判断银行和保险公司是否存在区域查询
        const userinfo = JSON.parse(window.localStorage.getItem("USERINFO"));
        if (userinfo?.enterpriseModel && (userinfo?.enterpriseModel?.companyType==2||userinfo?.enterpriseModel?.companyType==3)) {
           this.regionDom=false
        }else{
             this.regionDom=true
        }
        this.$nextTick(()=>{
            this.setTable(1)
         })
    },
    activated(){
        this.$nextTick(()=>{
            this.setTable(1)
        })
      },
    methods: {
        layout(){
            this.$nextTick(()=>{
                this.setTable(2)
            })
        },
        packUp(){
            const formRow = this.$el.querySelector('.form_row')
            if(this.toggleSearchStatus){
                formRow.style.height='44px'
            }else{
                formRow.style.height='auto'
            }
            this.toggleSearchStatus=!this.toggleSearchStatus
        
            this.setTable(2)
        },
        setTable(type) {
                this.$nextTick(() => {
                const formBox = this.$el.querySelector('.form_box')
                //操作按钮的高度
                const formBtn=this.$el.querySelector('.form_btn') 
                if(type==1){
                    this.setCollapse()
                }
                let windowHeight = window.innerHeight;
                this.tableHeight = windowHeight - formBox.offsetHeight - 200
                this.tableHeight= formBtn? this.tableHeight - formBtn.offsetHeight - 8: this.tableHeight
                if(this.boxHeight){
                    this.tableHeight= this.tableHeight- this.boxHeight
                }
                
                if(this.tableHeight < 0) { this.tableHeight = 0 }
                window.addEventListener('resize', () => {
                    windowHeight = window.innerHeight;
                    if(type==1){
                        this.setCollapse()
                    }
                    this.tableHeight = windowHeight - formBox.offsetHeight - 200
                    this.tableHeight= formBtn? this.tableHeight - formBtn.offsetHeight - 8: this.tableHeight
                    if(this.boxHeight){
                        this.tableHeight= this.tableHeight- this.boxHeight
                    }
                    
                    if(this.tableHeight < 0) { this.tableHeight = 0 }
                }); 
            })
            },
        setCollapse(){
            const formRow = this.$el.querySelector('.form_row')
            const formCol=this.$el.querySelector('.form_col')
            if(formCol?.offsetHeight>=formRow?.offsetHeight&&(formCol?.offsetHeight>51)){
                this.toggleSearchDom=true
            }else{
                this.toggleSearchDom=false
            }
        }

  }    

}
