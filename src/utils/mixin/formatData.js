
/**
 * 用于表格重置初始化数据
 * handleObj {object} 你要操作的具体对象名称
 * defaultValObj {object} 需要设置默认值的对象
 */
 export const formatData = {
    methods: {
        formatData(handleObj,defaultValObj='') {
            let sourceObj=this[handleObj];
            let resetData = {};
            for (const objKey in sourceObj) {
                const itemType=Object.prototype.toString.call(sourceObj[objKey]);
                if ( itemType== '[object Array]') {
                    resetData[objKey] = [];
                } else if (itemType == '[object Object]') {
                    resetData[objKey] = {};
                } else {
                    resetData[objKey] = "";
                }
            };
            if(defaultValObj){
                for(const key in defaultValObj){
                    resetData[key]=defaultValObj[key]
                };
            }
            console.log("resetData:",resetData)
            this[handleObj] = resetData;
        }
    }
}