export const searchUi = {
    data() {
        return {
            tableHeight: 0,//表格高度
            toggleSearchStatus:false, //展开收起
            toggleSearchDom:true,//是否出现展开收起
        }
    },
    mounted()  {
        this.$nextTick(()=>{
            this.setCollapse()
         })
    },
    methods: {
        packUp(){
            const formRow = this.$el.querySelector('.form_row')
            if(this.toggleSearchStatus){
                formRow.style.height='44px'
            }else{
                formRow.style.height='auto'
            }
            this.toggleSearchStatus=!this.toggleSearchStatus
        },
          
        setCollapse(){
            const formRow = this.$el.querySelector('.form_row')
            const formCol=this.$el.querySelector('.form_col')
            console.log(formCol?.offsetHeight,formCol?.offsetHeight)
            if(formCol?.offsetHeight>=formRow?.offsetHeight&&(formCol?.offsetHeight>51)){
                this.toggleSearchDom=true
            }else{
                this.toggleSearchDom=false
            }
        }

  }    

}
