 import {  treeListByParentId
} from "@/api/ffs/supervisionSheet/livingSupervisionApi.js";
export const ffsClient = {
    data() {
        return {
            props: {
                lazy: true,
                checkStrictly: true, //可选择任意节点
                lazyLoad (node, resolve) {
                    const nodes =[]
                    const userinfo = JSON.parse(window.localStorage.getItem("USERINFO"));
                    let tenantId=null
                    let platformId=userinfo.platformId||0
                    let parentFlag=null
                    if(node.level==0){
                       tenantId=userinfo.tenantId||0
                       parentFlag=1
                    }else{
                      tenantId=node.value
                      parentFlag=node.level+1
                    }
                    console.log(222)
                    treeListByParentId({parentFlag:parentFlag,tenantId:tenantId,platformId:platformId}).then(res=>{
                        let list=res.result||[]

                        list.map(item=>{
                            let leaf=false
                            if(item.childEnterpriseCount>0){
                                leaf=false
                            }else{
                                leaf=true
                            }
                            let obj = {
                                value: item.tenantId,
                                label: item.deptName,
                                ancestors:item.ancestors,
                                companyBankType:item.companyBankType,
                                leaf: leaf // 节点级别，如果没有子节点就停止查询
                             }
                             nodes.push(obj);
                        })
                            console.log(nodes)
                            resolve(nodes)

                    })
                }
              },
        }
    },
    mounted() {

    },

    methods: {
        selecascader(){
            let areaCodeData = this.$refs['myCascader'].getCheckedNodes()[0];
            this.$nextTick(() => {
                const panelRefs = this.$refs.myCascader.$refs.panel // 节点刷新，页面回显
                // panelRefs.lazyLoad(panelRefs.getCheckedNodes()[0])
                this.$refs.myCascader.dropDownVisible = false
            });
            console.log(11)
            if(this.queryParams.companyTenantIds.length>0){
                this.queryParams.ancestorsAllQuery=[]
                this.queryParams.ancestorsAllQuery =[areaCodeData.data.ancestors]
                this.queryParams.companyBankType =areaCodeData.data.companyBankType
                this.queryParams.companyTenantId=areaCodeData.data.value
            }else{
                this.queryParams.ancestorsAllQuery=[]
                this.queryParams.companyBankType =''
                this.queryParams.companyTenantId=''
            }
        },

  }

}
