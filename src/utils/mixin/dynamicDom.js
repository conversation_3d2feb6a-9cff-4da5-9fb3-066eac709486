
/**
 * 动态创建表单、DOM节点
 */
export const dynamicDom = {
    methods: {
        // 添加DOM节点
        /**
         * @param {string} obj  你要操作obj该对象名  本质是一个对象
         * @param {string} key  你要操作obj中具体对象的key值，该key对应的是一个数组
         * 
         * 示例：
         * data() {
            return {
                addForm:{
                     field: [
                        {
                            id: 1,
                            type: 1,
                        }
                    ]
                  }
                }
           }
           如果要动态操作addForm下面的animal则在组件中调用：@click="addDom('addForm','field')"
         * 
         * 
         */
        addDom(obj, key) {
            let handleObj = this[obj][key];
            let newData = {};
            for (const objKey in handleObj[0]) {
                const itemType = Object.prototype.toString.call(handleObj[0][objKey]);
                if (itemType == '[object Array]') {
                    newData[objKey] = [];
                } else if (itemType == '[object Object]') {
                    newData[objKey] = {};
                } else {
                    newData[objKey] = "";
                }
            }
            handleObj.push(newData);
            handleObj.forEach((item, index) => {
                item.id = (handleObj[0].id||1) + index + "";
            });
        },
        // 删除DOM节点
        /**
         * 
         * @param {string} obj  你要操作obj该对象名  本质是一个对象
         * @param {string} key 你要操作obj中具体对象的key值，该key对应的是一个数组
         * @param {number} index  要删除的数据下标
         */
        delDom(obj, key,index) {
            let handleObj = this[obj][key];
            handleObj.splice(index, 1);
        }
    }
}
