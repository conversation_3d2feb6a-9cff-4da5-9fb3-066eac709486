
// res是一个文件流
const fileStreamToFile=(name,res)=>{
  const blob = new Blob([res], {type: "application/xlsx"});
  const filename = name+".xlsx";
  const link = document.createElement("a");
  link.href = URL.createObjectURL(blob);
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  window.setTimeout(function () {
    URL.revokeObjectURL(blob);
    document.body.removeChild(link);
  }, 0);
}

export{
  fileStreamToFile
}