import provincialLevelData from "./provincialLevel.js";

let   listString= JSON.parse(localStorage.getItem("USERINFO") || null)?.platformModel.platformAreaId
console.log(listString,"xxx");
let provincialLevel=provincialLevelData.result
let options=[]
const handelString = () => {
    return listString.split(";");
  };

    //返回省级数据的索引
   function provinceIndexFn  (value)  {
        return options.findIndex((item) => {
          return item.value == value;
        });
      };
      //市级数据索引
    function cityIndexFn  (index, value) {
        return options[index].children.findIndex((item) => {
          return item.value == value;
        });
      };


      export const loopArry = () => {
        options=[]
        let arry = handelString();
        arry.forEach((item) => {
          let it = item.split(",");
          provincialLevel.map((item1) => {
            // 选择省下所有数据
            if (it.length == 2 && it[0] == item1.value) {
             options.push(item1);
            }
            //选择市级所有数据
            if (it.length == 3) {
              let provincial = item1;
              let provinceIndex = provinceIndexFn(it[0]);
              if (provinceIndex == -1 && it[0] == item1.value) {
                let obj = JSON.parse(JSON.stringify(item1));
                obj.children = [];
                provincial.children.map((pro) => {
                  if (pro.value == it[1]) {
                    obj.children.push(pro);
                  }
                });
                options.push(obj);
              }
              if (provinceIndex != -1 && it[0] == item1.value) {
                provincial.children.map((pro) => {
                  if (pro.value == it[1]) {
                  options[provinceIndex].children.push(pro);
                  }
                });
              }
            }
            //县级数据
            if (it.length == 4) {
              let provincial = item1; //省级数据
              let provinceIndex = provinceIndexFn(it[0]);
              if (provinceIndex == -1 && it[0] == item1.value) {
                let objP = JSON.parse(JSON.stringify(item1));
                objP.children = [];
                provincial.children.some((pro) => {
                  let city = pro; //市级数据
                  // let cityIndex=cityIndex(objP.children,t[2])
                  if (pro.value == it[1]) {
                    let objCity = JSON.parse(JSON.stringify(pro));
                    objCity.children = [];
                    city.children.some((coun) => {
                      if (coun.value == it[2]) {
                        objCity.children.push(coun);
                      }
                    });
                    objP.children.push(objCity);
                  }
                });
                options.push(objP);
              }
              if (provinceIndex != -1 && it[0] == item1.value) {
                provincial.children.map((pro) => {
                  let cityIndex = cityIndexFn(provinceIndex, it[1]);
                  let cityList = pro;
                  if (cityIndex == -1 && it[1] == pro.value) {
                    let objCity = JSON.parse(JSON.stringify(pro));
                    objCity.children = [];
                    cityList.children.some((coun) => {
                      if (coun.value == it[2]) {
                        objCity.children.push(coun);
                      }
                    });
                    options[provinceIndex].children.push(objCity);
                  }
                  if (cityIndex != -1 && it[1] == pro.value) {
                    cityList.children.some((coun) => {
                      if (coun.value == it[2]) {
                        options[provinceIndex].children[
                          cityIndex
                        ].children.push(coun);
                      }
                    });
                  }
                });
              }
            }
          });
        });
        return options
      };