<template>
  <div class="navbar">
    <div class="navbar-title">
        <img src="@/assets/images/headerTitle.png"  style="width: 364px; height: 23px ;vertical-align:middle">
    </div>
    <!-- <hamburger id="hamburger-container" :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" /> -->

    <!-- <breadcrumb id="breadcrumb-container" class="breadcrumb-container" v-if="!topNav"/>
    <top-nav id="topmenu-container" class="topmenu-container" v-if="topNav"/> -->

    <div class="right-menu">
      <template v-if="device!=='mobile'">
            <div style="display: flex; align-items: center; font-size: 14px;">
                <search id="header-search" class="right-menu-item" />
                <!-- <screenfull id="screenfull" class="right-menu-item hover-effect" /> -->

                <!-- <el-tooltip content="布局大小" effect="dark" placement="bottom">
                <size-select id="size-select" class="right-menu-item hover-effect" />
                </el-tooltip> -->
                <!-- 登录着岗位名称 -->
                <div class="deptName" v-if="!tradingList?.length">
                  欢迎您：<span style="padding-right: 10px;">{{ platformName }}</span>
                  <span style="padding-right: 10px;">{{deptName}}</span>

                  <span style="padding-right: 10px;">{{roleName}}</span>
                    <router-link to="/user/profile">
                        <span :style="{'color':'#FFFFFF'}">  {{ userName }}</span>
                    </router-link>
                </div>
                <div class="deptName" v-else>
                  <!-- 贸易公司下拉框 -->
                  欢迎您：
                  <span style="padding-right: 10px;">{{roleName}}</span>
                    <router-link to="/user/profile">
                        <span :style="{'color':'#FFFFFF'}" style="margin-right: 10px;">  {{ userName }}</span>
                    </router-link>
                  <el-select
                    v-model="selectedTradingId"
                    placeholder="请选择贸易公司"
                    size="small"
                    style="width: 180px; margin-right: 10px; color: #FFFFFF;"
                    popper-class="trading-select-dropdown"
                    @change="handleTradingChange"
                  >
                    <el-option
                      v-for="item in tradingList"
                      :key="item.tradingTenantId"
                      :label="item.companyName"
                      :value="item.tradingTenantId"
                    />
                  </el-select>
                 
                </div>
                <div style="display: flex; align-items: center; cursor: pointer;" @click="logout">
                    <img src="@/assets/images/logout.png" style="width: 15px; height: 16px;">
                    <span style="margin: 0 20px 0  7px;" :style="{'color':'#FFFFFF' }">退出</span>
                </div>
            </div>
      </template>
<!--
      <el-dropdown class="avatar-container right-menu-item hover-effect" trigger="click">
        <div class="avatar-wrapper">
          <img :src="avatar" class="user-avatar">
          <i class="el-icon-caret-bottom" />
        </div>
        <el-dropdown-menu slot="dropdown">
            <router-link to="/user/profile">
            <el-dropdown-item>个人中心</el-dropdown-item>
          </router-link>
          <el-dropdown-item @click.native="setting = true">
            <span>布局设置</span>
          </el-dropdown-item>
          <el-dropdown-item divided @click.native="logout">
            <span>退出登录</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown> -->
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Breadcrumb from '@/components/Breadcrumb'
import TopNav from '@/components/TopNav'
import Hamburger from '@/components/Hamburger'
import Screenfull from '@/components/Screenfull'
import SizeSelect from '@/components/SizeSelect'
import Search from '@/components/HeaderSearch'
import { myTradingList } from '@/api/nmb/userCustomer'

export default {
  data: () => {
    return {
      tradingList: [],
      selectedTradingId: null 
    }
  },
  components: {
    Breadcrumb,
    TopNav,
    Hamburger,
    Screenfull,
    SizeSelect,
    Search
  },
  computed: {
    ...mapGetters([
      'sidebar',
      'avatar',
      'device',
      'user'
    ]),
    setting: {
      get() {
        return this.$store.state.settings.showSettings
      },
      set(val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'showSettings',
          value: val
        })
      }
    },
    topNav: {
      get() {
        return this.$store.state.settings.topNav
      }
    },
    deptName(){
        let deptName=this.user?.dept?.deptName||''
        let name=""
        if(this.user.enterpriseModel){
            name=this.user.enterpriseModel.companyName
        }else{
            name=this.user.platformModel?.platformName
        }
        if(deptName==name){
            return ''
        }else{
            return deptName
        }

    },
    roleName(){
        let roleNameF=this.user?.roles[0]?.roleName||''
        let roleNameT=this.user?.roles[1]?.roleName||''
        let roleNameR=this.user?.roles[2]?.roleName||''
        return (roleNameF+(roleNameT?'，'+roleNameT:''))+(roleNameR?'，'+roleNameR:'')

    },
    userName(){
        let name=this.user.corprateName||this.user.nickName||this.user.userName||''
        return name
    },
    theme() {
        return this.$store.state.settings.theme;
      },
      platformName(){
        let name=""
        if(this.user.enterpriseModel){
            name=this.user.enterpriseModel.companyName
        }else{
            name=this.user.platformModel?.platformName
        }
        return name
      }
  },
  mounted(){
    this.getMyTradingList()
  },
  methods: {
    getMyTradingList(){
      myTradingList({}).then(res=>{
        if (res.result && res.result.length > 0) {
          this.tradingList = res.result
          const savedTradingId = localStorage.getItem('selectedTradingId')
          if (savedTradingId) {
            const exists = this.tradingList.some(item => item.tradingTenantId == savedTradingId)
            if (exists) {
              this.selectedTradingId = savedTradingId
            } else {
              this.selectedTradingId = this.tradingList[0].tradingTenantId
              localStorage.setItem('selectedTradingId', this.selectedTradingId)
            }
          } else {
            this.selectedTradingId = this.tradingList[0].tradingTenantId
            localStorage.setItem('selectedTradingId', this.selectedTradingId)
          }
        }
      })
    },

    // 处理贸易公司选择变化
    handleTradingChange(value) {
      console.log('选择的贸易公司ID:', value)
      // 保存到localStorage
      localStorage.setItem('selectedTradingId', value)
      this.$router.go(0) 
    },
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    async logout() {
        console.log(this.user);
      this.$confirm('确定退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$store.dispatch('LogOut').then(() => {
          if(window.localStorage.getItem('platformType') == 1) {
            location.href = '/login1';
          } else if(window.localStorage.getItem('platformType') == 2){
            location.href = '/login2';
          } else if(window.localStorage.getItem('platformType') == 3){
              location.href = '/login3';
          } else if(window.localStorage.getItem('platformType') == 4){
              location.href = '/login4';
          } else if(window.localStorage.getItem('platformType') == 5){
              location.href = '/login_f';
          } else if(window.localStorage.getItem('platformType') == 6){
              location.href = '/login6';
          }else {
             location.href = '/login';
          }

        })
      }).catch(() => {});
    }
  }
}
</script>

<style lang="scss" scoped>
.navbar{
    width:100% !important;
}
.navbar {
  height: 50px;
  box-sizing: border-box;
  overflow: hidden;
  position: relative;
    background: linear-gradient(270deg, #6882FF 0%, #415EEB 55%);
    box-shadow: 0 1px 4px rgba(0,21,41,.08);
    &-title{
        height: 50px;
        float: left;
        color: #FFFFFF;
        line-height: 50px;
        font-size: 20px;
        padding-left: 20px;
    }
  .breadcrumb-container {
    float: left;
  }

  .topmenu-container {
    position: absolute;
    left: 50px;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;
    color: #FFFFFF;
    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #FFFFFF;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background .3s;

        &:hover {
          background: rgba(0, 0, 0, .025)
        }
      }
    }

    .avatar-container {
      margin-right: 30px;

      .avatar-wrapper {
        margin-top: 5px;
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 25px;
          height: 25px;
          border-radius: 10px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -17px;
          top: 12px;
          font-size: 14px;
        }
      }
    }
  }
}
.deptName{
     margin: 0 20px;
}

/* 贸易公司下拉框样式 */
:deep(.el-select .el-input) {
  .el-input__inner {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: #FFFFFF;

    &::placeholder {
      color: rgba(255, 255, 255, 0.7);
    }
  }

  .el-select__caret {
    color: #FFFFFF;
  }
}

/* 下拉菜单样式 */
.trading-select-dropdown {
  border-radius: 4px;

  .el-select-dropdown__item {
    &.selected {
      color: #4c6bed;
      font-weight: bold;
    }

    &:hover {
      background-color: #f5f7fa;
    }
  }
}
</style>
