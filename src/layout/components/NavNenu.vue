<template>
  <div class="navbar">
    <div class="navbar-title breadcrumb-container">
      <img src="../../assets/images/mes/logo.png" alt="">
    </div>
    <!-- <hamburger id="hamburger-container" :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" /> -->

    <!-- <breadcrumb id="breadcrumb-container" class="breadcrumb-container" v-if="!topNav"/>
    <top-nav id="topmenu-container" class="topmenu-container" v-if="topNav"/> -->

    <div class="right-menu">
      <template v-if="device!=='mobile'">
        <div  style="display: flex; align-items: center; font-size: 14px;">
        <search id="header-search" class="right-menu-item" />
        <!-- <screenfull id="screenfull" class="right-menu-item hover-effect" /> -->

        <!-- <el-tooltip content="布局大小" effect="dark" placement="bottom">
          <size-select id="size-select" class="right-menu-item hover-effect" />
        </el-tooltip> -->

        <div class="deptName">
            欢迎您:{{deptName}}
            <span>{{roleName}}</span>
              <router-link to="/user/profile">
                  <span :style="{'color':theme}">  {{ userName }}</span>
              </router-link>
          </div>
          <div style="display: flex; align-items: center; cursor: pointer;" @click="logout">
              <img src="@/assets/images/logout.png" style="width: 15px; height: 16px;">
              <span style="margin: 0 20px 0  7px;" :style="{'color':theme }">退出</span>
          </div>
        </div>
      </template>
<!-- 
      <el-dropdown class="avatar-container right-menu-item hover-effect" trigger="click">
        <div class="avatar-wrapper">
          <img :src="avatar" class="user-avatar">
          <i class="el-icon-caret-bottom" />
        </div>
        <el-dropdown-menu slot="dropdown">
          <router-link to="/user/profile">
            <el-dropdown-item>个人中心</el-dropdown-item>
          </router-link>
          <el-dropdown-item divided @click.native="logout">
            <span>退出登录</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown> -->
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Breadcrumb from '@/components/Breadcrumb'
import TopNav from '@/components/TopNav'
import Hamburger from '@/components/Hamburger'
import Screenfull from '@/components/Screenfull'
import SizeSelect from '@/components/SizeSelect'
import Search from '@/components/HeaderSearch'

export default {
  components: {
    Breadcrumb,
    TopNav,
    Hamburger,
    Screenfull,
    SizeSelect,
    Search
  },
  computed: {
    ...mapGetters([
      'sidebar',
      'avatar',
      'device',
      'user'
    ]),
    setting: {
      get() {
        return this.$store.state.settings.showSettings
      },
      set(val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'showSettings',
          value: val
        })
      }
    },
    topNav: {
      get() {
        return this.$store.state.settings.topNav
      }
    },
    deptName(){
        let deptName=this.user?.dept?.deptName||''
        return deptName
    },
    roleName(){
        let roleName=this.user?.roles[0]?.roleName
        return roleName

    },
    userName(){
        let name=this.user.corprateName||this.user.nickName||this.user.userName||''
        return name
    },
    theme() {
        return this.$store.state.settings.theme;
      },
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    async logout() {
      this.$confirm('确定退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$store.dispatch('LogOut').then(() => {
          if(window.localStorage.getItem('platformType') == 1) {
            location.href = '/login1';
          } else if(window.localStorage.getItem('platformType') == 2){
            location.href = '/login2';
          } else if(window.localStorage.getItem('platformType') == 3){
              location.href = '/login3';
          } else if(window.localStorage.getItem('platformType') == 4){
              location.href = '/login4';
          } else {
             location.href = '/login';
          }
          
        })
      }).catch(() => {});
    }
  }
}
</script>

<style lang="scss" scoped>
.navbar{
    width:100% !important;
}
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);
    &-title{
      float: left;
      height: 50px;
      line-height: 50px;
      font-size: 16px;
    }
  .breadcrumb-container {
    float: left;
  }

  .topmenu-container {
    position: absolute;
    left: 50px;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background .3s;

        &:hover {
          background: rgba(0, 0, 0, .025)
        }
      }
    }

    .avatar-container {
      margin-right: 30px;

      .avatar-wrapper {
        margin-top: 5px;
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 25px;
          height: 25px;
          border-radius: 10px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -17px;
          top: 12px;
          font-size: 14px;
        }
      }
    }
  }
}

.breadcrumb-container {
  float: left;
  height: 50px;
  width: 220px;
  line-height: 50px;
  text-align: center;
  cursor: pointer;
  img{
    width: 160px;
  }
}
.deptName{
     margin: 0 20px;
}
</style>
