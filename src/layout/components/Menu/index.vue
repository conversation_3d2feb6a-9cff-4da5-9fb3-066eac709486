<template>
    <div :class="{'has-logo':showLogo,'sidebar':true}" :style="{ backgroundColor: settings.sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground }">
        <!-- <logo v-if="showLogo" :collapse="isCollapse" /> -->
        <el-scrollbar :class="settings.sideTheme" class="mes-el-scrollbar" wrap-class="scrollbar-wrapper">
            <el-menu
                :default-active="activeMenu"
                :collapse="isCollapse"
                :background-color="settings.sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground"
                :text-color="settings.sideTheme === 'theme-dark' ? variables.menuColor : variables.menuLightColor"
                :unique-opened="true"
                :active-text-color="settings.theme"
                :collapse-transition="false"
                mode="vertical"
                :popper-append-to-body="true"
                popper-class="mes-el-scrollbar"
            >
                <sidebar-item
                    v-for="(route, index) in menuList"
                    :key="route.path  + index"
                    :item="route"
                    :base-path="route.path"
                />
            </el-menu>
        </el-scrollbar>
        <div class="hamburger-box">
            <hamburger id="hamburger-container" :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" />
        </div>
    </div>
</template>

<script>
import { mapGetters, mapState } from "vuex";
import Hamburger from '@/components/Hamburger';
import Logo from "./Logo";
import SidebarItem from "./SidebarItem";
import variables from "@/assets/styles/variables.scss";
export default {
    components: { SidebarItem, Logo, Hamburger },
    computed: {
        ...mapState(["settings"]),
        ...mapGetters(["sidebarRouters", "sidebar"]),
        activeMenu() {
            const route = this.$route;
            const { meta, path } = route;
            // if set path, the sidebar will highlight the path you set
            if (meta.activeMenu) {
                return meta.activeMenu;
            }
            return path;
        },
        showLogo() {
            return this.$store.state.settings.sidebarLogo;
        },
        variables() {
            return variables;
        },
        isCollapse() {
            return !this.sidebar.opened;
        }
    }, 
    data() {
        return {
            menuList: []
        }
    },
    watch: {
        sidebarRouters() {
        this.changeMenuList()
        if(this.currentSidebarItem && this.sidebarRouters) {
            if(this.sidebarRouters[0]) {
            if (this.currentSidebarItem.parentPath  != this.sidebarRouters[0].parentPath) {
                this.$store.dispatch('app/setMesCurrentMenuPath', this.sidebarRouters[0].path);
                this.getChildrenList(this.sidebarRouters[0])
                this.$emit('setWidth')
            }
            }
        }
        }
    },
    created() {
        this.changeMenuList()
    },
    methods: {
        toggleSideBar() {
            this.$store.dispatch('app/toggleSideBar')
        },
        changeMenuList() {
            this.menuList = []
            this.sidebarRouters.forEach(item => {
                if (item.name == 'Mesapp') {
                    item.children.forEach(i => {
                        this.menuList.push(i)
                    })
                } else {
                    this.menuList.push(item)
                }
            });
        },
    },
};
</script>
<style lang="scss" scoped>
.sidebar{
    top: 55px !important;
    // padding: 0 !important;
    // padding-top: 15px !important;
}
.hamburger-box{
    font-size: 18px;
    text-align: right;
    padding-right: 12px;
    box-shadow: 0px 2px 3px 0px rgba(246,246,246,0.95) inset;
    line-height: 48px;
}
</style>