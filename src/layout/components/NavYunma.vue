<template>
  <div class="navbar">
    <div class="navbar-title" style="color: #5672FA">
        智慧云码溯源防伪管理平台
    </div>
    <div class="right-menu">
      <template v-if="device!=='mobile'">
            <div style="display: flex; align-items: center; font-size: 14px;">
                <search id="header-search" class="right-menu-item" />
                <!-- 登录着岗位名称 -->
                <div class="deptName">
                  欢迎您:{{deptName}}
                  <span>{{roleName}}</span>
                    <router-link to="/user/profile">
                        <span :style="{'color':theme}">  {{ userName }}</span>
                    </router-link>
                </div>
                <div style="display: flex; align-items: center; cursor: pointer;" @click="logout">
                    <img src="@/assets/images/logout.png" style="width: 15px; height: 16px;">
                    <span style="margin: 0 20px 0  7px;" :style="{'color':theme }">退出</span>
                </div>
            </div>
      </template>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Breadcrumb from '@/components/Breadcrumb'
import TopNav from '@/components/TopNav'
import Hamburger from '@/components/Hamburger'
import Screenfull from '@/components/Screenfull'
import SizeSelect from '@/components/SizeSelect'
import Search from '@/components/HeaderSearch'

export default {
  name: 'NavTrace',
  components: {
    Breadcrumb,
    TopNav,
    Hamburger,
    Screenfull,
    SizeSelect,
    Search
  },
  computed: {
    ...mapGetters([
      'sidebar',
      'avatar',
      'device',
      'user'
    ]),
    setting: {
      get() {
        return this.$store.state.settings.showSettings
      },
      set(val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'showSettings',
          value: val
        })
      }
    },
    topNav: {
      get() {
        return this.$store.state.settings.topNav
      }
    },
    deptName(){
        let deptName=this.user?.dept?.deptName||''
        return deptName
    },
    roleName(){
        let roleName=this.user?.roles[0]?.roleName
        return roleName

    },
    userName(){
        let name=this.user.corprateName||this.user.nickName||this.user.userName||''
        return name
    },
    theme() {
        return this.$store.state.settings.theme;
      },
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    async logout() {
        console.log(this.user);
      this.$confirm('确定退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$store.dispatch('LogOut').then(() => {
          if(window.localStorage.getItem('platformType') == 1) {
            location.href = '/login1';
          } else if(window.localStorage.getItem('platformType') == 2){
            location.href = '/login2';
          } else if(window.localStorage.getItem('platformType') == 3){
              location.href = '/login3';
          } else if(window.localStorage.getItem('platformType') == 4){
              location.href = '/login4';
          } else if(window.localStorage.getItem('platformType') == 6){
            alert(1)
              location.href = '/login6';
            } else {
             location.href = '/login';
          }

        })
      }).catch(() => {});
    }
  }
}
</script>

<style lang="scss" scoped>
.navbar{
    width:100% !important;
}
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0px 2px 6px 0px rgba(78,89,105,0.059);
    &-title{
        float: left;
        height: 50px;
        line-height: 50px;
      font-size: 16px;
      padding-left: 20px;
    }
  .breadcrumb-container {
    float: left;
  }

  .topmenu-container {
    position: absolute;
    left: 50px;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background .3s;

        &:hover {
          background: rgba(0, 0, 0, .025)
        }
      }
    }

    .avatar-container {
      margin-right: 30px;

      .avatar-wrapper {
        margin-top: 5px;
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 25px;
          height: 25px;
          border-radius: 10px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -17px;
          top: 12px;
          font-size: 14px;
        }
      }
    }
  }
}
.deptName{
     margin: 0 20px;
}
</style>
