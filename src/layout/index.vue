<template>
  <div>
    <!-- 溯源系统 -->
    <div :class="classObj" class="app-wrapper" :style="{'--current-color': theme}" v-if="lanqiTraceDom">
      <div v-if="device==='mobile'&&sidebar.opened" class="drawer-bg" @click="handleClickOutside" />
      <sidebar v-if="!sidebar.hide" class="sidebar-container" />
      <div :class="{hasTagsView:needTagsView,sidebarHide:sidebar.hide}" class="main-container">
        <div :class="{'fixed-header':fixedHeader}">
          <NavTrace></NavTrace>
        </div>
        <div>
            <breadcrumb id="breadcrumb-container" class="breadcrumb-container" v-if="!topNav&&!needTagsView"/>
            <top-nav id="topmenu-container" class="topmenu-container" v-if="topNav&&!needTagsView"/>
            <tags-view v-if="needTagsView" />
        </div>
        <app-main class="app_main"/>
        <right-panel>
          <settings />
        </right-panel>
      </div>
    </div>
    <!-- 智慧云码溯源系统 -->
    <div :class="classObj" class="app-wrapper" :style="{'--current-color': theme}" v-else-if="yunmaDom">
      <div v-if="device==='mobile'&&sidebar.opened" class="drawer-bg" @click="handleClickOutside" />
      <sidebar v-if="!sidebar.hide" class="sidebar-container" />
      <div :class="{hasTagsView:needTagsView,sidebarHide:sidebar.hide}" class="main-container">
        <div :class="{'fixed-header':fixedHeader}">
          <NavYunma></NavYunma>
        </div>
        <div>
            <breadcrumb id="breadcrumb-container" class="breadcrumb-container" v-if="!topNav&&!needTagsView"/>
            <top-nav id="topmenu-container" class="topmenu-container" v-if="topNav&&!needTagsView"/>
            <tags-view v-if="needTagsView" />
        </div>
        <app-main class="app_main"/>
        <right-panel>
          <settings />
        </right-panel>
      </div>
    </div>
    <!-- mes系统 -->
    <div v-else-if="showDom">
      <div :class="classMesObj" class="app-wrapper" :style="{'--current-color': theme}">
        <div v-if="device==='mobile'&&sidebar.opened" class="drawer-bg" @click="handleClickOutside" />
        <Menu v-if="!sidebar.hide" class="sidebar-container" />
        <!-- <Menu v-if="!sidebar.hide" @setWidth='listenWidth' id="sideMenu" class="sidebar-container sidebar-container1 side-menu" /> -->
        <div class="mesaapp_main">
          <div :class="{'fixed-headerMes':fixedHeader}">
            <NavNenu></NavNenu>
            <!-- <navbar /> -->
          </div>
          <div :class="{hasTagsView:needTagsView,sidebarHide:sidebar.hide}" class="main-container mes-container-main" style="box-sizing: border-box;background: #eef1f9">
            <MesBreadcrumb id="breadcrumb-container" class="breadcrumb-container"/>
            <app-main class="mesapp" />
            <right-panel> 
              <settings />
            </right-panel>
          </div>
        </div>
      </div>
    </div>
    <div :class="classObj" class="app-wrapper" :style="{'--current-color': theme}" v-else>
      <div v-if="device==='mobile'&&sidebar.opened" class="drawer-bg" @click="handleClickOutside" />
      <sidebar v-if="!sidebar.hide" class="sidebar-container" />
      <div :class="{hasTagsView:needTagsView,sidebarHide:sidebar.hide}" class="main-container">
        <div :class="{'fixed-header':fixedHeader}">
          <navbar />
        </div>
        <div style="margin:0 10px">
            <breadcrumb id="breadcrumb-container" class="breadcrumb-container" v-if="!topNav&&!needTagsView"/>
            <top-nav id="topmenu-container" class="topmenu-container" v-if="topNav&&!needTagsView"/>
            <tags-view v-if="needTagsView" />
        </div>
        <app-main class="app_main"/>
        <right-panel>
          <settings />
        </right-panel>
      </div>
    </div>
  </div>
</template>
  
  <script>
  import Breadcrumb from '@/components/Breadcrumb'
  import TopNav from '@/components/TopNav'
import RightPanel from "@/components/RightPanel";
import MesBreadcrumb from "@/components/Breadcrumb/mesBreadcrumb.vue";
import {
  AppMain,
  Navbar,
  Settings,
  Sidebar,
  TagsView,
  NavNenu,
  
} from "./components";

import ResizeMixin from "./mixin/ResizeHandler";
import Menu from "./components/Menu/index.vue";
import { mapGetters, mapState } from "vuex";
import variables from "@/assets/styles/variables.scss";
let elementResizeDetectorMaker = require("element-resize-detector");
import { checkPermi } from "@/utils/permission.js";

export default {
  name: "Layout",
  components: {
    AppMain,
    Navbar,
    RightPanel,
    Settings,
    Sidebar,
    TagsView,
    NavNenu,
    Menu,
    MesBreadcrumb,
    Breadcrumb,
    TopNav,
    NavTrace: () => import('./components/NavTrace'),
    NavYunma: () => import('./components/NavYunma')

  },
  data() {
      return {
        sideClientWidth: 0,
        lanqiTraceDom: false,
        yunmaDom: false,
      }
  },
  mixins: [ResizeMixin],
  computed: {
    ...mapState({
      theme: (state) => state.settings.theme,
      sideTheme: (state) => state.settings.sideTheme,
      sidebar: (state) => state.app.sidebar,
      device: (state) => state.app.device,
      needTagsView: (state) => state.settings.tagsView,
      fixedHeader: (state) => state.settings.fixedHeader,
      user:(state)=>state.user.user
    }),
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === "mobile",
      };
     
    },
    showDom(){
        if(this.user.hasOwnProperty('enterpriseModel') && this.user?.enterpriseModel?.companyType == 2){
            // this.$store.commit('settings/SET_THEME','#5672FA')
            return true
        }else{
            // this.$store.commit('settings/SET_THEME','#11A983')
            return false
        }
    },
    classMesObj(){
        return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
      };
    },
    variables() {
      return variables;
    },
    topNav: {
      get() {
        return this.$store.state.settings.topNav
      }
    }
  },
  watch: {
    'sidebar.hide': {
      handler(newVal, oldVal) {
        window.removeEventListener('resize', this.listenWidth)
        this.$nextTick(() => {
          this.listenWidth()
        })
      }
    }
  },
  created() {
    if (checkPermi(["home:view:lanqiTrace"]) || (checkPermi(["home:view:lanqiTrace"]) && checkPermi(["home:view:yunma"]))) {
        this.lanqiTraceDom=true
    }else{
        this.lanqiTraceDom=false
    }
    if (checkPermi(["home:view:yunma"]) || (!checkPermi(["home:view:lanqiTrace"]) && checkPermi(["home:view:yunma"]))) {
        this.yunmaDom=true
    }else{
        this.yunmaDom=false
    }
    
  },
  mounted() {
    this.listenWidth()
    window.addEventListener('resize', this.listenWidth)
  }, 
  methods: {
    listenWidth() {
      let erd = elementResizeDetectorMaker();
      let that = this;
      that.$nextTick(function () {
        if (document.getElementById("sideMenu")) {
          erd.listenTo(document.getElementById("sideMenu"), function (element) {
            that.sideClientWidth = element.clientWidth
          })
        }
      })
    },
    handleClickOutside() {
      this.$store.dispatch("app/closeSideBar", { withoutAnimation: false });
    },
  },
};
</script>
  
  <style lang="scss" scoped>
@import "~@/assets/styles/mixin.scss";
@import "~@/assets/styles/variables.scss";

.app-wrapper {
  @include clearfix;
  position: relative;
  height: 100%;
  width: 100%;

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}
.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
//   width: calc(100% - #{$base-sidebar-width});
    width: 100%;
  transition: width 0.28s;
}
.fixed-headerMes{
    position: fixed;
  top: 0;
  z-index: 9;
  width:100%;
  transition: width 0.28s;
}
.hideSidebar .fixed-header {
//   width: calc(100% - 54px);
}

.sidebarHide .fixed-header {
  width: 100%;
}

.mobile .fixed-header {
  width: 100%;
}
.side-menu {
  top: 50px !important;
  background: #fff !important;
  -webkit-box-shadow: -2px 0 5px rgba(0, 21, 41, 0.35);
  box-shadow: -2px 0 5px rgba(0, 21, 41, 0.35);
}
.mes-app {
  margin-left: 54px !important;
}
.mesapp{
    min-height: calc(100vh - 50px - 28px) !important;
    background: #F5F6FA;
}
.app_main{
    min-width: 1020px;
}
.mes-container-main{
  top: 50px !important;
}
</style>
  