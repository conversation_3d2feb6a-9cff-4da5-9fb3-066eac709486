import request from "@/utils/request"
import {basicPath10} from '@/api/base.js'
/* 
    牛贸易公司
*/

// 牛贸易公司列表
export const tradingPage = (data) => {
    return request({
        url: `${basicPath10}nmb/company/trading/page`,
        method: 'post',
        data: data
    })
}
// 牛贸易公司详情
export const tradingInfo = (data) => {
    return request({
        url: `${basicPath10}nmb/company/trading/info`,
        method: 'post',
        data: data
    })
}
// 牛贸易公司新增
export const tradingAdd = (data) => {
    return request({
        url: `${basicPath10}nmb/company/trading/add`,
        method: 'post',
        data: data
    })
}
// 牛贸易公司编辑
export const tradingUpdate = (data) => {
    return request({
        url: `${basicPath10}nmb/company/trading/update`,
        method: 'post',
        data: data
    })
}
// 牛贸易公司改密码
export const updatePassword = (data) => {
    return request({
        url: `${basicPath10}nmb/company/trading/updatePassword`,
        method: 'post',
        data: data
    })
}

// 牛贸易公司员工列表
export const tradingManagerPage = (data) => {
    return request({
        url: `${basicPath10}nmb/user/trading/manager/page`,
        method: 'post',
        data: data
    })
}

// 详情 
export const managerInfo = (data) => {
    return request({
        url: `${basicPath10}nmb/user/trading/manager/info`,
        method: 'post',
        data: data
    })
}

// 新增
export const tradingManagerAdd = (data) => {
    return request({
        url: `${basicPath10}nmb/user/trading/manager/add`,
        method: 'post',
        data: data
    })
}

// 修改  
export const tradingManagerUpdate = (data) => {
    return request({
        url: `${basicPath10}nmb/user/trading/manager/update`,
        method: 'post',
        data: data
    })
}

// 停用
export const tradingManagerUpdateStatus = (data) => {
    return request({
        url: `${basicPath10}nmb/user/trading/manager/updateStatus`,
        method: 'post',
        data: data
    })
}

// 移除
export const tradingManagerRemove = (data) => {
    return request({
        url: `${basicPath10}nmb/user/trading/manager/remove`,
        method: 'post',
        data: data
    })
}
