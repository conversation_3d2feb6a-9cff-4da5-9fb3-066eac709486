import request from "@/utils/request"
import {basicPath10} from '@/api/base.js'
/* 
    员工
*/

// 员工列表
export const customerPage = (data) => {
    return request({
        url: `${basicPath10}nmb/user/customer/page`,
        method: 'post',
        data: data
    })
}
// 员工详情
export const customerInfo = (data) => {
    return request({
        url: `${basicPath10}nmb/user/customer/info`,
        method: 'post',
        data: data
    })
}
// 员工新增
export const customerAdd = (data) => {
    return request({
        url: `${basicPath10}nmb/user/customer/add`,
        method: 'post',
        data: data
    })
}
// 员工编辑
export const customerUpdate = (data) => {
    return request({
        url: `${basicPath10}nmb/user/customer/update`,
        method: 'post',
        data: data
    })
}
// 该状态
export const updateStatus = (data) => {
    return request({
        url: `${basicPath10}nmb/user/customer/updateStatus`,
        method: 'post',
        data: data
    })
}
//员工移除
export const remove = (data) => {
    return request({
        url: `${basicPath10}nmb/user/customer/remove`,
        method: 'post',
        data: data
    })
}

export const companyList = (data) => {
    return request({
        url: `${basicPath10}nmb/company/list`,
        method: 'post',
        data: data
    })
}
export const selectTradingRoleList = (data) => {
    return request({
        url: `${basicPath10}nmb/user/selectTradingRoleList`,
        method: 'post',
        data: data
    })
}

export const myTradingList = (data) => {
    return request({
        url: `${basicPath10}nmb/company/myTradingList`,
        method: 'post',
        data: data
    })
}

// 小组新增
export const userTeamAdd = (data) => {
    return request({
        url: `${basicPath10}userTeam/add`,
        method: 'post',
        data: data
    })
}
