import request from "@/utils/request"
import {basicPath10} from '@/api/base.js'
/* 
    需求方
*/

// 需求方列表
export const customerPage = (data) => {
    return request({
        url: `${basicPath10}nmb/company/customer/page`,
        method: 'post',
        data: data
    })
}
// 需求方详情
export const customerInfo = (data) => {
    return request({
        url: `${basicPath10}nmb/company/customer/info`,
        method: 'post',
        data: data
    })
}
// 需求方新增
export const customerAdd = (data) => {
    return request({
        url: `${basicPath10}nmb/company/customer/add`,
        method: 'post',
        data: data
    })
}
// 需求方编辑
export const customerUpdate = (data) => {
    return request({
        url: `${basicPath10}nmb/company/customer/update`,
        method: 'post',
        data: data
    })
}
// 改状态
export const updateStatus = (data) => {
    return request({
        url: `${basicPath10}nmb/company/customer/updateStatus`,
        method: 'post',
        data: data
    })
}
export const userList = (data) => {
    return request({
        url: `${basicPath10}nmb/user/list`,
        method: 'post',
        data: data
    })
}