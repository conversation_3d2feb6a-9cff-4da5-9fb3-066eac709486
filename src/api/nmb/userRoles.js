import request from '@/utils/request'
import { basicPath10 } from '@/api/base.js'
// 查询角色列表
export function listRole(data) {
    return request({
      url: `${basicPath10}nmb/role/page`,
      method: 'post',
      data: data
    })
  }

// 查询角色详细
  export function getRole(data) {
    return request({
      url: `${basicPath10}nmb/role/info`,
      method: 'post',
      data: data
    })
  }

  // 角色新增
  export function addRole(data) {
    return request({
      url: `${basicPath10}nmb/role/add`,
      method: 'post',
      data: data
    })
  }

// 角色修改
export function updateRole(data) {
  return request({
    url: `${basicPath10}nmb/role/update`,
    method: 'post',
    data: data
  }) 
}

// 角色删除 nmb/role/remove
export function delRole(data) {
  return request({
    url: `${basicPath10}nmb/role/remove`,
    method: 'post',
    data: data
  })
}
// 菜单树
export function getMenuTree(data) {
  return request({
    url: `${basicPath10}nmb/role/menuTreeList`,
    method: 'post',
    data: data
  })
}