import request from "@/utils/request"
import {basicPath10} from '@/api/base.js'
/* 
    牛贸易公司
*/

// 牛贸易公司列表
export const tradingPage = (data) => {
    return request({
        url: `${basicPath10}nmb/user/trading/page`,
        method: 'post',
        data: data
    })
}
// 牛贸易公司详情
export const tradingInfo = (data) => {
    return request({
        url: `${basicPath10}nmb/user/trading/info`,
        method: 'post',
        data: data
    })
}
// 牛贸易公司新增
export const tradingAdd = (data) => {
    return request({
        url: `${basicPath10}nmb/user/trading/add`,
        method: 'post',
        data: data
    })
}
// 牛贸易公司编辑
export const tradingUpdate = (data) => {
    return request({
        url: `${basicPath10}nmb/user/trading/update`,
        method: 'post',
        data: data
    })
}
// 该状态
export const updateStatus = (data) => {
    return request({
        url: `${basicPath10}nmb/user/trading/updateStatus`,
        method: 'post',
        data: data
    })
}
//员工移除
export const remove = (data) => {
    return request({
        url: `${basicPath10}nmb/user/trading/remove`,
        method: 'post',
        data: data
    })
}

// 小组查询
export const userTeamList = (data) => {
    return request({
        url: `${basicPath10}userTeam/list`,
        method: 'post',
        data: data
    })
}

// 新增小组
export const userTeamAdd = (data) => {
    return request({
        url: `${basicPath10}userTeam/add`,
        method: 'post',
        data: data
    }) 
}