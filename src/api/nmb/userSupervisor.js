import request from "@/utils/request"
import {basicPath10} from '@/api/base.js'
/* 
    员工
*/

// 员工列表
export const supervisorPage = (data) => {
    return request({
        url: `${basicPath10}nmb/user/supervisor/page`,
        method: 'post',
        data: data
    })
}
// 员工详情
export const supervisorInfo = (data) => {
    return request({
        url: `${basicPath10}nmb/user/supervisor/info`,
        method: 'post',
        data: data
    })
}
// 员工新增
export const supervisorAdd = (data) => {
    return request({
        url: `${basicPath10}nmb/user/supervisor/add`,
        method: 'post',
        data: data
    })
}
// 员工编辑
export const supervisorUpdate = (data) => {
    return request({
        url: `${basicPath10}nmb/user/supervisor/update`,
        method: 'post',
        data: data
    })
}
// 该状态
export const updateStatus = (data) => {
    return request({
        url: `${basicPath10}nmb/user/supervisor/updateStatus`,
        method: 'post',
        data: data
    })
}
//员工移除
export const remove = (data) => {
    return request({
        url: `${basicPath10}nmb/user/supervisor/remove`,
        method: 'post',
        data: data
    })
}