import request from "@/utils/request"
import {basicPath10} from '@/api/base.js'
/* 
    监管方
*/

// 监管方列表
export const supervisorPage = (data) => {
    return request({
        url: `${basicPath10}nmb/company/supervisor/page`,
        method: 'post',
        data: data
    })
}
// 监管方详情
export const supervisorInfo = (data) => {
    return request({
        url: `${basicPath10}nmb/company/supervisor/info`,
        method: 'post',
        data: data
    })
}
// 监管方新增
export const supervisorAdd = (data) => {
    return request({
        url: `${basicPath10}nmb/company/supervisor/add`,
        method: 'post',
        data: data
    })
}
// 监管方编辑
export const supervisorUpdate = (data) => {
    return request({
        url: `${basicPath10}nmb/company/supervisor/update`,
        method: 'post',
        data: data
    })
}
// 改状态
export const updateStatus = (data) => {
    return request({
        url: `${basicPath10}nmb/company/supervisor/updateStatus`,
        method: 'post',
        data: data
    })
}