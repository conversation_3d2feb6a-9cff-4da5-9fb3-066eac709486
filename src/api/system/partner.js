import request from '@/utils/request'
import {basicPath2} from '@/api/base.js'
// 合伙人列表
export function partnerList(data) {
  return request({
    url: `${basicPath2}partner/list`,
    method: 'post',
    data: data
  })
}
//添加
export function partnerAdd(data) {
    return request({
      url: `${basicPath2}partner/add`,
      method: 'post',
      data: data
    })
  }
  //修改
  export function partnerEdit(data) {
    return request({
      url: `${basicPath2}partner/edit`,
      method: 'post',
      data: data
    })
  }
   //查询合伙人详情
   export function selectById(data) {
    return request({
      url: `${basicPath2}partner/selectById`,
      method: 'post',
      data: data
    })
  }
     //查询合伙人实名认证的列表
     export function selectByUsername(data) {
        return request({
          url: `${basicPath2}partner/selectByUsername`,
          method: 'post',
          data: data
        })
      }