import request from '@/utils/request'
import { basicPath2 } from '@/api/base.js'
// 合伙人列表
export function pastorList(data) {
    return request({
        url: `${basicPath2}pastor/list`,
        method: 'post',
        data: data
    })
}
//添加
export function pastorAdd(data) {
    return request({
        url: `${basicPath2}pastor/add`,
        method: 'post',
        data: data
    })
}
//修改
export function pastorEdit(data) {
    return request({
        url: `${basicPath2}pastor/edit`,
        method: 'post',
        data: data
    })
}
//审核
export function pastorApprove(data) {
    return request({
        url: `${basicPath2}pastor/approve`,
        method: 'post',
        data: data
    })
}
//禁用和启用
export function pastorDisable(data) {
    return request({
        url: `${basicPath2}pastor/delete`,
        method: 'post',
        data: data
    })
}
//详情
export function pastorselectById(data) {
    return request({
        url: `${basicPath2}pastor/selectById`,
        method: 'post',
        data: data
    })
}

export function pastorselectByIds(data) {
    return request({
        url: `${basicPath2}pastor/info`,
        method: 'post',
        data: data
    })
}
//牧民列表
export function userPastorList(data) {
    return request({
        url: `${basicPath2}userPastor/list`,
        method: 'post',
        data: data
    })
}

//解绑牧民
export function userPastorDel(data) {
    return request({
        url: `${basicPath2}userPastor/delete`,
        method: 'post',
        data: data
    })
}

//新增单个用户
export function userPastorIN(data) {
    return request({
        url: `${basicPath2}userPastor/insert`,
        method: 'post',
        data: data
    })
}
//查询用户信息
export function userList(data) {
    return request({
        url: `${basicPath2}userPastor/userList`,
        method: 'post',
        data: data
    })
}
//生成二维码
export function qrNo(data) {
    return request({
        url: `${basicPath2}pastor/catNo`,
        method: 'post',
        data: data
    })
}
