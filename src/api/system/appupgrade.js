import request from '@/utils/request'
import {basicPath1, basicPath2} from '@/api/base';

// 查询app版本升级列表
export function listAppupgrade(query) {
  return request({
    url: basicPath1 + '/system/appupgrade/list',
    method: 'get',
    params: query
  })
}

// 查询app版本升级详细
export function getAppupgrade(appUpgradeId) {
  return request({
    url: basicPath1 + '/system/appupgrade/' + appUpgradeId,
    method: 'get'
  })
}

// 新增app版本升级
export function addAppupgrade(data) {
  return request({
    url: basicPath1 + '/system/appupgrade',
    method: 'post',
    data: data
  })
}

// 获取华为云obs配置
export function selectObsData() {
  return request({
    url: basicPath2 + '/files/obs/fileToken',
    method: 'post'
  })
}

// 修改app版本升级
export function updateAppupgrade(data) {
  return request({
    url: basicPath1 + '/system/appupgrade',
    method: 'put',
    data: data
  })
}

// 删除app版本升级
export function delAppupgrade(appUpgradeId) {
  return request({
    url: basicPath1 + '/system/appupgrade/' + appUpgradeId,
    method: 'delete'
  })
}
