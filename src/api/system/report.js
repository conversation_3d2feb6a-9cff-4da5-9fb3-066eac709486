import request from '@/utils/request'
// 获取全部报表
export function getReport(data) {
    return request({
        // url: `${basicPath5}baseScreenEnterpriseInfo/list`,
        url: `screenapp-service/screenReportDataManage/list`,
        method: 'post',
        data,
        headers: {
            "version": "yaoyuan"
        }
    })
}
export const getAreaList = (data) => {
    return request({
        // url: `${basicPath5}baseScreenEnterpriseInfo/list`,
        url: `screenapp-service/screenReportDataManage/select/areaName`,
        method: 'post',
        data,
        headers: {
            "version": "yaoyuan"
        }
    })
}
// 活畜指标
export const getdataList = (data) => {
    return request({
        // url: `${basicPath5}baseScreenEnterpriseInfo/list`,
        url: `screenapp-service/ecommerceDataManage/livestock/trade/norm`,
        method: 'post',
        data,
        headers: {
            "version": "yaoyuan"
        }
    })
}
// 趋势图
export const gettrendList = (data) => {
    return request({
        // url: `${basicPath5}baseScreenEnterpriseInfo/list`,
        url: `screenapp-service/ecommerceDataManage/list/trade/trend`,
        method: 'post',
        data,
        headers: {
            "version": "yaoyuan"
        }
    })
}
// 环形图
export const getRoundList = (data) => {
    return request({
        // url: `${basicPath5}baseScreenEnterpriseInfo/list`,
        url: `screenapp-service/screenLivestockKindDis/list`,
        method: 'post',
        data,
        headers: {
            "version": "yaoyuan"
        }
    })
}
// 数据指标弹出层修改
export const dataEdit = (data) => {
    return request({
        // url: `${basicPath5}baseScreenEnterpriseInfo/list`,
        url: `screenapp-service/ecommerceDataManage/update/tradeNorm`,
        method: 'post',
        data,
        headers: {
            "version": "yaoyuan"
        }
    })
}
// 活畜交易品类弹出层修改
export const roundEdit = (data) => {
    return request({
        url: `screenapp-service/screenLivestockKindDis/update`,
        method: 'post',
        data,
        headers: {
            "version": "yaoyuan"
        }
    })
}
// 月份
export const getDataList = (data) => {
    return request({
        url: `screenapp-service/screenDate/generate`,
        method: 'post',
        data,
        headers: {
            "version": "yaoyuan"
        }
    })
}
// 活畜交易趋势弹出层修改
export const trendEdit = (data) => {
    return request({
        url: `screenapp-service/ecommerceDataManage/update/tradeTrend`,
        method: 'post',
        data,
        headers: {
            "version": "yaoyuan"
        }
    })
}