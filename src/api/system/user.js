import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/east";
import { basicPath1, basicPath2, basicPath4 } from '@/api/base.js'

// 查询用户列表
export function listUser(query) {
  return request({
    url: `${basicPath1}system/user/list`,
    method: 'get',
    params: query
  })
}
//简单查询用户列表
export function listSimpleUser(query) {
  return request({
    url: `${basicPath1}system/user/simpleList`,
    method: 'post',
    data: query
  })
}

export function searchUserList(query) {
  return request({
    url: `${basicPath1}system/user/searchUserList`,
    method: 'post',
    data: query
  })
}

export function selectGlobalManagerUserList(query) {
  return request({
    url: `${basicPath1}system/user/selectGlobalManagerUserList`,
    method: 'post',
    data: query
  })
}

export function selectUserByPhonenumber(data) {
    return request({
      url: `${basicPath2}xmbuser/byPhone`,
      method: 'post',
      data: data
    })
  }
// 查询用户详细
export function getUser(userId) {
  return request({
    url: `${basicPath1}system/user/` + parseStrEmpty(userId),
    method: 'get'
  })
}

// 新增用户
export function addUser(data) {
  return request({
    url: `${basicPath1}system/user`,
    method: 'post',
    data: data
  })
}

// 修改用户
export function updateUser(data) {
  return request({
    url: `${basicPath1}system/user`,
    method: 'put',
    data: data
  })
}

// 删除用户
export function delUser(userId) {
  return request({
    url: `${basicPath1}system/user/` + userId,
    method: 'delete'
  })
}

// 用户密码重置
export function resetUserPwd(userId, password) {
  const data = {
    userId,
    password
  }
  return request({
    url: `${basicPath1}system/user/resetPwd`,
    method: 'put',
    data: data
  })
}

// 用户状态修改
export function changeUserStatus(userId, status) {
  const data = {
    userId,
    status
  }
  return request({
    url: `${basicPath1}system/user/changeStatus`,
    method: 'put',
    data: data
  })
}

// 查询用户个人信息
export function getUserProfile() {
  return request({
    url: `${basicPath1}system/user/profile`,
    method: 'get'
  })
}

// 修改用户个人信息
export function updateUserProfile(data) {
  return request({
    url: `${basicPath1}system/user/profile`,
    method: 'put',
    data: data
  })
}

// 用户密码重置
export function updateUserPwd(oldPassword, newPassword) {
  const data = {
    oldPassword,
    newPassword
  }
  return request({
    url: `${basicPath1}system/user/profile/updatePwd`,
    method: 'put',
    params: data
  })
}

// 用户头像上传
export function uploadAvatar(data) {
  return request({
    // url: `${basicPath1}system/user/profile/avatar`,
    url: `${basicPath2}files/obs/fileUpload`,
    method: 'post',
    data: data
  })
}

// 查询授权角色
export function getAuthRole(userId) {
  return request({
    url: `${basicPath1}system/user/authRole/` + userId,
    method: 'get'
  })
}

// 保存授权角色
export function updateAuthRole(data) {
  return request({
    url: `${basicPath1}system/user/authRole`,
    method: 'put',
    params: data
  })
}


// 查询搜索用户  可以手机号、昵称等等信息搜索
export function searchUser(data) {
    return request({
      url: `${basicPath2}user/searchUser`,
      method: 'post',
      data
    })
  }
// 查询银行名称
export function bankinfo(data) {
    return request({
        url: `${basicPath4}bankinfo/page`,
        method: 'post',
        data
    })
}
