import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/east";
import { basicPath1, basicPath2, basicPath4 } from '@/api/base.js'

// 查询用户列表
export function listUser(query) {
  return request({
    url: `${basicPath1}system/member/list`,
    method: 'get',
    params: query
  })
}

// 阿尔善列表
export function listUserAlxa(data) {
  return request({
    url: `${basicPath4}pasture/queryAesList`,
    method: 'post',
    data: data
  })
}

//简单查询用户列表
export function listSimpleUser(query) {
  return request({
    url: `${basicPath1}system/member/simpleList`,
    method: 'post',
    params: query
  })
}

export function searchUserList(query) {
  return request({
    url: `${basicPath1}system/member/searchUserList`,
    method: 'post',
    params: query
  })
}

// 查询用户详细
export function getUser(userId) {
  return request({
    url: `${basicPath1}system/member/` + parseStrEmpty(userId),
    method: 'get'
  })
}

// 新增用户
export function addUser(data) {
  return request({
    url: `${basicPath1}system/member`,
    method: 'post',
    data: data
  })
}

// 修改用户
export function updateUser(data) {
  return request({
    url: `${basicPath1}system/member`,
    method: 'put',
    data: data
  })
}

// 删除用户
export function delUser(userId) {
  return request({
    url: `${basicPath1}system/member/` + userId,
    method: 'delete'
  })
}

// 用户密码重置
export function resetUserPwd(userId, password) {
  const data = {
    userId,
    password
  }
  return request({
    url: `${basicPath1}system/member/resetPwd`,
    method: 'put',
    data: data
  })
}

// 用户状态修改
export function changeUserStatus(userId, status) {
  const data = {
    userId,
    status
  }
  return request({
    url: `${basicPath1}system/member/changeStatus`,
    method: 'put',
    data: data
  })
}

// 查询用户个人信息
export function getUserProfile() {
  return request({
    url: `${basicPath1}system/member/profile`,
    method: 'get'
  })
}

// 修改用户个人信息
export function updateUserProfile(data) {
  return request({
    url: `${basicPath1}system/member/profile`,
    method: 'put',
    data: data
  })
}

// 用户密码重置
export function updateUserPwd(oldPassword, newPassword) {
  const data = {
    oldPassword,
    newPassword
  }
  return request({
    url: `${basicPath1}system/member/profile/updatePwd`,
    method: 'put',
    params: data
  })
}

// 用户头像上传
export function uploadAvatar(data) {
  return request({
    // url: `${basicPath1}system/user/profile/avatar`,
    url: `${basicPath2}files/obs/fileUpload`,
    method: 'post',
    data: data
  })
}


// 查询搜索用户  可以手机号、昵称等等信息搜索
export function searchUser(data) {
  return request({
    url: `${basicPath2}user/searchUser`,
    method: 'post',
    data
  })
}

