import request from "@/utils/request";
import { basicPath2, basicPath3 } from "@/api/base.js";


/**
 *
 * @param {*} 运营中心
 * @returns
 */
// 创建收款账户
export function createAccountForPlatform(data) {
  return request({
    url: `${basicPath2}account/createAccountForPlatform`,
    method: "post",
    data: data,
  });
}

// 账户列表
export function listPlatformAccount(data) {
  return request({
    url: `${basicPath2}account/listPlatformAccount`,
    method: "post",
    data: data,
  });
}

// 账户详情
export function platformAccount(data) {
  return request({
    url: `${basicPath2}account/platformAccount`,
    method: "post",
    data: data,
  });
}



/**
 *
 * @param {*} 工作人员
 * @returns
 */
// 列表
export function listAccountForWorker(data) {
  return request({
    url: `${basicPath2}account/listAccountForWorker`,
    method: "post",
    data: data,
  });
}



/**
 *
 * @param {*} 其他 服务站
 * @returns
 */

// 列表
export function listAboutAccount(data) {
  return request({
    url: `${basicPath3}goodsPlace/listAboutAccount`,
    method: "post",
    data: data,
  });
}

// 创建账户
export function settingAccount(data) {
  return request({
    url: `${basicPath3}goodsPlace/settingAccount`,
    method: "post",
    data: data,
  });
}
