import request from '@/utils/request'
import {basicPath1} from '@/api/base'

// 查询租户管理列表
export function listSysTenant(query) {
  return request({
    url: basicPath1 + '/system/sysTenant/list',
    method: 'get',
    params: query
  })
}

// 查询租户管理详细
export function getSysTenant(tenantId) {
  return request({
    url: basicPath1 + '/system/sysTenant/' + tenantId,
    method: 'get'
  })
}

// 新增租户管理
export function addSysTenant(data) {
  return request({
    url: basicPath1 + '/system/sysTenant',
    method: 'post',
    data: data
  })
}

// 修改租户管理
export function updateSysTenant(data) {
  return request({
    url: basicPath1 + '/system/sysTenant',
    method: 'put',
    data: data
  })
}

// 删除租户管理
export function delSysTenant(tenantId) {
  return request({
    url: basicPath1 + '/system/sysTenant/' + tenantId,
    method: 'delete'
  })
}
