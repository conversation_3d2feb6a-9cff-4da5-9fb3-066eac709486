import request from '@/utils/request'
import {basicPath1} from '@/api/base'

// 查询企业认证列表
export function listEnterprise(query) {
  return request({
    url: basicPath1 + '/system/enterprise/list',
    method: 'get',
    params: query
  })
}

export function listNoPageEnterprise(query){
  return request({
    url: basicPath1 + '/system/enterprise/getNoPageList',
    method: 'get',
    params: query
  })
}

// 查询企业认证详细
export function getEnterprise(id) {
  return request({
    url: basicPath1 + 'system/enterprise/' + id,
    method: 'get'
  })
}

// 新增企业认证
export function addEnterprise(data) {
  return request({
    url: basicPath1 + '/system/enterprise',
    method: 'post',
    data: data
  })
}

// 企业审核
export function approveEnterprise(data) {
    return request({
      url: basicPath1 + 'system/enterprise/approve',
      method: 'post',
      data: data
    })
  }

// 修改企业认证
export function updateEnterprise(data) {
  return request({
    url: basicPath1 + '/system/enterprise',
    method: 'put',
    data: data
  })
}

// 删除企业认证
export function delEnterprise(id) {
  return request({
    url: basicPath1 + '/system/enterprise/' + id,
    method: 'delete'
  })
}



// 新增企业认证
export function addOperating(data) {
  return request({
    url: basicPath1 + '/system/enterprise/addOperating',
    method: 'post',
    data: data
  })
}

// 修改企业认证
export function editOperating(data) {
  return request({
    url: basicPath1 + '/system/enterprise/editOperating',
    method: 'post',
    data: data
  })
}
//添加监管公司
export function ADDsuperviseEnterprise(data) {
    return request({
      url: basicPath1 + 'system/superviseEnterprise',
      method: 'post',
      data: data
    })
  }
  //保存企业收款账户信息
  export function saveReceiveAccount(data) {
      return request({
        url: basicPath1 + 'system/enterprise/saveReceiveAccount',
        method: 'post',
        data: data
      })
    }
    //通过企业ID查询收款账户信息
    export function selectReceiveAccountById(data) {
        return request({
          url: basicPath1 + 'system/enterprise/selectReceiveAccountById',
          method: 'post',
          data: data
        })
      }