import request from '@/utils/request'
import { basicPath3 } from '@/api/base.js'

export function userIntentionList(data) {
    return request({
        url: `${basicPath3}userIntention/selectUserIntentionList`,
        method: 'post',
        data: data
    })
}

//修改
export function updateUserIntention(data) {
    return request({
        url: `${basicPath3}userIntention/updateUserIntention`,
        method: 'post',
        data: data
    })
}
//答复详情
export function feedBackreply(data) {
    return request({
        url: `${basicPath3}feedBack/reply`,
        method: 'post',
        data: data
    })
}
