import request from '@/utils/request'
import {basicPath1} from '@/api/base'

// 查询审批流程执行日志列表
export function listLog(query) {
  return request({
    url: basicPath1 + 'flow/execute/log/list',
    method: 'get',
    params: query
  })
}

// 查询审批流程执行日志详细
export function getLog(logId) {
  return request({
    url: basicPath1 + 'flow/log/' + logId,
    method: 'get'
  })
}

// 新增审批流程执行日志
export function addLog(data) {
  return request({
    url: basicPath1 + '/flow/log',
    method: 'post',
    data: data
  })
}

// 修改审批流程执行日志
export function updateLog(data) {
  return request({
    url: basicPath1 + '/flow/log',
    method: 'put',
    data: data
  })
}

// 删除审批流程执行日志
export function delLog(logId) {
  return request({
    url: basicPath1 + '/flow/log/' + logId,
    method: 'delete'
  })
}
