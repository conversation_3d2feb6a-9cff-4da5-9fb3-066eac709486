import request from '@/utils/request'
import {basicPath1} from '@/api/base'

// 查询流程节点列表
export function listNode(query) {
  return request({
    url: basicPath1+'/flow/node/list',
    method: 'get',
    params: query
  })
}



// 新增流程节点
export function addNode(data) {
  return request({
    url: basicPath1+'/flow/node',
    method: 'post',
    data: data
  })
}

// 修改流程节点
export function updateNode(data) {
  return request({
    url:basicPath1+ '/flow/node',
    method: 'put',
    data: data
  })
}

// 删除流程节点
export function delNode(mainId,nodeId) {
  return request({
    url: basicPath1+'/flow/node/' +mainId+'/'+ nodeId,
    method: 'delete'
  })
}