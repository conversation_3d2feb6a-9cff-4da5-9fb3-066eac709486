import request from '@/utils/request'
import {basicPath1} from '@/api/base'

/**
 * 查询审批整体节点列表
 * @param query
 * @returns {*}
 */
export function executeList(query) {
  return request({
    url: basicPath1+'/flow/execute/list',
    method: 'get',
    params: query
  })
}

/**
 * 查询待我审批的待办审批
 * @param query
 * @returns {*}
 */
export function myExamineApproveList(query) {
  return request({
    url: basicPath1+'/flow/execute/myExamineApproveList',
    method: 'get',
    params: query
  })
}

/**
 * 查询我已经审批的待办历史
 * @param query
 * @returns {*}
 */
export function myAlreadyApprovedList(query) {
  return request({
    url: basicPath1+'/flow/execute/log/list',
    method: 'get',
    params: query
  })
}

/**
 * 审批通过接口
 * @param data
 * @returns {*}
 */
export function approve(data){
  return request({
    url: basicPath1+'/flow/execute/approve',
    method: 'post',
    data: data
  })
}

/**
 * 获取已经审核过的节点，用来做驳回操作
 * @param query
 * @returns {*}
 */
export function getApproveNodes(query) {
  return request({
    url: basicPath1+'/flow/execute/getApproveNodes',
    method: 'get',
    params: query
  })
}

/**
 * 驳回
 * @param data
 * @returns {*}
 */
export function reject(data){
  return request({
    url: basicPath1+'/flow/execute/reject',
    method: 'post',
    data: data
  })
}

export function myMakeCopy(query){
  return request({
    url: basicPath1+'/flow/execute/myMakeCopy',
    method: 'get',
    params: query
  })
}


