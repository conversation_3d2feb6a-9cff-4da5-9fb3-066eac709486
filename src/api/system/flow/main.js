import request from '@/utils/request'
import {basicPath1} from '@/api/base'

// 查询流程配置列表
export function listMain(query) {
  return request({
    url: basicPath1+'/flow/main/list',
    method: 'get',
    params: query
  })
}

// 查询流程配置详细
export function getMain(mianId) {
  return request({
    url: basicPath1+'/flow/main/' + mianId,
    method: 'get'
  })
}

// 新增流程配置
export function addMain(data) {
  return request({
    url: basicPath1+'/flow/main',
    method: 'post',
    data: data
  })
}

// 修改流程配置
export function updateMain(data) {
  return request({
    url: basicPath1+'/flow/main',
    method: 'put',
    data: data
  })
}

// 删除流程配置
export function delMain(mianId) {
  return request({
    url: basicPath1+'/flow/main/' + mianId,
    method: 'delete'
  })
}

// 查询流程配置列表
export function getOperateCenter(query) {
  return request({
    url: basicPath1+'/flow/main/getOperateCenter',
    method: 'get',
    params: query
  })
}

// 查询流程配置列表
export function getSystemApp(query) {
  return request({
    url: basicPath1+'/flow/main/getSystemApp',
    method: 'get',
    params: query
  })
}
