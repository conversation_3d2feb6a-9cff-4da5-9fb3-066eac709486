import request from '@/utils/request'
import {basicPath1} from '@/api/base.js'
// 查询部门列表
export function listDept(query) {
  return request({
    url: `${basicPath1}system/dept/list`,
    method: 'get',
    params: query
  })
}

// 查询部门列表（排除节点）
export function listDeptExcludeChild(deptId) {
  return request({
    url: `${basicPath1}system/dept/list/exclude/` + deptId,
    method: 'get'
  })
}

// 查询部门详细
export function getDept(deptId) {
  return request({
    url: `${basicPath1}system/dept/` + deptId,
    method: 'get'
  })
}

// 查询部门下拉树结构
export function treeselect(query) {
  return request({
    url: `${basicPath1}system/dept/treeselect`,
    method: 'get',
    params: query
  })
}

// 查询部门下拉树结构,企业下的部门信息
export function treeTenantSelect(query) {
  return request({
    url: `${basicPath1}system/dept/treeTenantSelect/`,
    method: 'get',
    params: query
  })
}

// 查询部门下拉树结构，提供给岗位数据权限使用
export function treeselectPost(query) {
  return request({
    url: `${basicPath1}system/dept/treeselectPost`,
    method: 'get',
    params: query
  })
}

// 根据角色ID查询部门树结构
export function roleDeptTreeselect(roleId) {
  return request({
    url: `${basicPath1}system/dept/roleDeptTreeselect/` + roleId,
    method: 'get'
  })
}

// 新增部门
export function addDept(data) {
  return request({
    url: `${basicPath1}system/dept`,
    method: 'post',
    data: data
  })
}

// 修改部门
export function updateDept(data) {
  return request({
    url: `${basicPath1}system/dept`,
    method: 'put',
    data: data
  })
}

// 删除部门
export function delDept(deptId) {
  return request({
    url: `${basicPath1}system/dept/` + deptId,
    method: 'delete'
  })
}
