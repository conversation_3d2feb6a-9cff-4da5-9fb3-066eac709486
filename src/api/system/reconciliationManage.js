import request from '@/utils/request'
import { basicPath4 } from '@/api/base';

// 对账单列表
export function accountList(data) {
    return request({
        url: basicPath4 + 'acc/accountChecking/page',
        method: 'post',
        data: data
    })
}
//对账详情
export function accountInfo(data) {
    return request({
        url: basicPath4 + 'acc/accountChecking/info',
        method: 'post',
        data: data
    })
}
//发起对账
export function accounCheck(data) {
    return request({
        url: basicPath4 + 'acc/accountChecking/confirm',
        method: 'post',
        data: data
    })
}
//导出接口
export function accounExport(data) {
    return request({
        url: basicPath4 + 'acc/accountChecking/exportList',
        method: 'post',
        data: data,
        responseType: 'arraybuffer'
    })
}
//确认或驳回对账
export function accounReject(data) {
    return request({
        url: basicPath4 + 'acc/accountChecking/reject',
        method: 'post',
        data: data,
    })
}
//导出表格
export function exportCheckList(data) {
    return request({
        url: basicPath4 + 'acc/accountChecking/exportCheckList',
        method: 'post',
        data: data,
        responseType: 'arraybuffer'
    })
}