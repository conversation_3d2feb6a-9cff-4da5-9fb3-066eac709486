import request from '@/utils/request'
import { basicPath1, basicPath4 } from '@/api/base.js'


/**
 * @description 基础信息
 * @param {*} userId 
 * @returns 
 */
// 查询基础信息
export function getBaseInfo(userId) {
  return request({
    url: basicPath1+'system/member/' + userId,
    method: 'get',
  })
}
// 修改
export function addInfo(data) {
  return request({
    url: basicPath1+'system/member',
    method: 'put',
    data: data
  })
}



// 查询用户下家庭成员列表
export function listFamily(query) {
  return request({
    url: `${basicPath1}system/family/list`,
    method: 'get',
    params: query
  })
}

// 查询用户下家庭成员详细
export function getFamily(memId) {
  return request({
    url: basicPath1+'system/family/' + memId,
    method: 'get'
  })
}

// 新增用户下家庭成员
export function addFamily(data) {
  return request({
    url: basicPath1+'system/family',
    method: 'post',
    data: data
  })
}

// 修改用户下家庭成员
export function updateFamily(data) {
  return request({
    url: basicPath1+'system/family',
    method: 'put',
    data: data
  })
}

// 删除用户下家庭成员
export function delFamily(memId) {
  return request({
    url: basicPath1+'system/family/' + memId,
    method: 'delete'
  })
}

// 居住环境

// 查询牧民信息列表
export function listMessage(query) {
  return request({
    url: '/herdsman/message/list',
    method: 'get',
    params: query
  })
}


// 查询牧民信息详细
export function getMessage(data) {
  return request({
      url: basicPath4+'herdsman/selectBaseHerdsmanMessageByUserId',
      method: 'post',
      data: data
  })
}
// 查询牧民信息详细
export function getMessageTwo(data) {
    return request({
        url: basicPath4+'herdsman/selectBaseHerdsmanMessageByUserIdTwo',
        method: 'post',
        data: data
    })
  }
  export function getMessageThree(data) {
    return request({
        url: basicPath4+'herdsman/selectBaseHerdsmanMessageByUserIdThree',
        method: 'post',
        data: data
    })
  }
  export function getMessageFour(data) {
    return request({
        url: basicPath4+'herdsman/selectBaseHerdsmanMessageByUserIdFour',
        method: 'post',
        data: data
    })
  }
  export function getMessageFive(data) {
    return request({
        url: basicPath4+'herdsman/selectBaseHerdsmanMessageByUserIdFive',
        method: 'post',
        data: data
    })
  }
  export function getMessageSix(data) {
    return request({
        url: basicPath4+'selectBaseHerdsmanMessageByUserIdSix',
        method: 'post',
        data: data
    })
  }

// 编辑牧民信息
export function updateBaseHerdsmanMessage(data) {
  return request({
    url: basicPath4+'herdsman/updateBaseHerdsmanMessage',
    method: 'post',
    data: data
  })
}

// 新增牧民信息
export function addMessage(data) {
  return request({
    url: basicPath4+'herdsman/message',
    method: 'post',
    data: data
  })
}

// 修改牧民信息
export function updateMessage(data) {
  return request({
    url: basicPath4+'herdsman/message',
    method: 'put',
    data: data
  })
}

// 删除牧民信息
export function delMessage(herdsmanMessageId) {
  return request({
    url: basicPath4+'herdsman/message/' + herdsmanMessageId,
    method: 'delete'
  })
}
