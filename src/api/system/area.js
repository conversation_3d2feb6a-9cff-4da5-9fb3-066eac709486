import request from '@/utils/request'
import {basicPath1} from '@/api/base';

export function areaList(query) {
  return request({
    url: basicPath1 + 'tools/area/areaList',
    method: 'get',
    params: query
  })
}

export function areaIdsList(query){
  return request({
    url: basicPath1 + 'tools/area/areaIdsList',
    method: 'get',
    params: query
  })
}

export function getPostAreaList(query){
  return request({
    url: basicPath1 + 'tools/area/getPostAreaList',
    method: 'get',
    params: query
  })
}

