import request from '@/utils/request'
import {basicPath1} from '@/api/base'
import {basicPath2} from '@/api/base'

// 查询系统应用列表
export function listApp(query) {
  return request({
    url: basicPath1 + 'system/app/list',
    method: 'get',
    params: query
  })
}

// 查询系统应用详细
export function getApp(appId) {
  return request({
    url: basicPath1 + '/system/app/' + appId,
    method: 'get'
  })
}

// 新增系统应用
export function addApp(data) {
  return request({
    url: basicPath1 + '/system/app',
    method: 'post',
    data: data
  })
}

// 修改系统应用
export function updateApp(data) {
  return request({
    url: basicPath1 + '/system/app',
    method: 'put',
    data: data
  })
}

// 删除系统应用
export function delApp(appId) {
  return request({
    url: basicPath1 + '/system/app/' + appId,
    method: 'delete'
  })
}
//获取区域接口
export function areaList(data) {
    return request({
      url: basicPath2 + 'baseArea/list',
      method: 'post',
      data:data
    })
  }