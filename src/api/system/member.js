import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/east";
import { basicPath1, basicPath2, basicPath3 } from '@/api/base.js'

// 查询用户列表
// export function listUser(query) {
//   return request({
//     url: `${basicPath1}system/member/list`,
//     method: 'get',
//     params: query
//   })
// }
export function listUser(query) {
  return request({
    url: `${basicPath2}wx/platformUser`,
    method: 'POST',
    data: query
  })
}
// 查询用户列表--会员接口改造
export function listUserXmb(data) {
  return request({
    url: `${basicPath2}wx/xmbUser`,
    method: 'POST',
    data: data
  })
}

// 满足对于没有平台属性的用户，平台运营能够进行分配需求; 改造会员列表
export function systemListUser(query) {
  return request({
    url: `${basicPath2}wx/systemPlatformUser`,
    method: 'POST',
    data: query
  })
}

// 获取平台列表

export function platformList(query) {
  return request({
    url: `${basicPath2}sysPlatform/list`,
    method: 'POST',
    data: query
  })
}

// 设置平台
export function setPlatform(query) {
  return request({
    url: `${basicPath2}wx/setPlatform`,
    method: 'POST',
    data: query
  })
}

//简单查询用户列表
export function listSimpleUser(query) {
  return request({
    url: `${basicPath1}system/member/simpleList`,
    method: 'post',
    params: query
  })
}

// 查询用户详细
export function getUser(userId) {
  return request({
    url: `${basicPath1}system/member/` + parseStrEmpty(userId),
    method: 'get'
  })
}

export function getUserDetaill(data) {
  return request({
    url: `${basicPath2}wx/XmbUserByUserId`,
    method: 'post',
    data: data
  })
}

// 新增用户
export function addUser(data) {
  return request({
    url: `${basicPath1}system/member`,
    method: 'post',
    data: data
  })
}

// 修改用户
export function updateUser(data) {
  return request({
    url: `${basicPath1}system/member`,
    method: 'put',
    data: data
  })
}

// 删除用户
export function delUser(userId) {
  return request({
    url: `${basicPath1}system/member/` + userId,
    method: 'delete'
  })
}

// 用户密码重置
export function resetUserPwd(userId, password) {
  const data = {
    userId,
    password
  }
  return request({
    url: `${basicPath1}system/member/resetPwd`,
    method: 'put',
    data: data
  })
}

// 用户状态修改
export function changeUserStatus(userId, status) {
  const data = {
    userId,
    status
  }
  return request({
    url: `${basicPath1}system/member/changeStatus`,
    method: 'put',
    data: data
  })
}

// 查询用户个人信息
export function getUserProfile() {
  return request({
    url: `${basicPath1}system/member/profile`,
    method: 'get'
  })
}

// 修改用户个人信息
export function updateUserProfile(data) {
  return request({
    url: `${basicPath1}system/member/profile`,
    method: 'put',
    data: data
  })
}

// 用户密码重置
export function updateUserPwd(oldPassword, newPassword) {
  const data = {
    oldPassword,
    newPassword
  }
  return request({
    url: `${basicPath1}system/member/profile/updatePwd`,
    method: 'put',
    params: data
  })
}

// 用户头像上传
export function uploadAvatar(data) {
  return request({
    // url: `${basicPath1}system/user/profile/avatar`,
    url: `${basicPath2}files/obs/fileUpload`,
    method: 'post',
    data: data
  })
}

// 查询授权角色
export function getAuthRole(userId) {
  return request({
    url: `${basicPath1}system/member/authRole/` + userId,
    method: 'get'
  })
}

// 保存授权角色
export function updateAuthRole(data) {
  return request({
    url: `${basicPath1}system/member/authRole`,
    method: 'put',
    params: data
  })
}


// 查询搜索用户  可以手机号、昵称等等信息搜索
export function searchUser(data) {
    return request({
      url: `${basicPath2}member/searchUser`,
      method: 'post',
      data
    })
  }

  // 导出会员列表
export function orderExportMember(data){
  return request({
    url: `${basicPath2}wx/exportXmbUser`,
    method: 'post',
    responseType: 'blob',
    data: data
  })
}
