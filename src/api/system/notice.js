import request from '@/utils/request'
import {basicPath1} from '@/api/base.js'
// 查询公告列表
export function listNotice(query) {
  return request({
    url: `${basicPath1}system/notice/list`,
    method: 'get',
    params: query
  })
}

// 查询公告详细
export function getNotice(noticeId) {
  return request({
    url: `${basicPath1}system/notice/` + noticeId,
    method: 'get'
  })
}

// 新增公告
export function addNotice(data) {
  return request({
    url: `${basicPath1}system/notice`,
    method: 'post',
    data: data
  })
}

// 修改公告
export function updateNotice(data) {
  return request({
    url: `${basicPath1}system/notice`,
    method: 'put',
    data: data
  })
}

// 删除公告
export function delNotice(noticeId) {
  return request({
    url: `${basicPath1}system/notice/` + noticeId,
    method: 'delete'
  })
}
// 公告列表
export function announcementPage(data) {
    return request({
        url: 'traceapp-service/announcement/page',
        method: 'post',
        data: data
    })
}
// 公告添加
export function announcementAdd(data) {
    return request({
        url: 'traceapp-service/announcement/add',
        method: 'post',
        data: data
    })
}
// 公告详情
export function announcementInfo(data) {
    return request({
        url: 'traceapp-service/announcement/info',
        method: 'post',
        data: data
    })
}
// 公告编辑
export function announcementUpdate(data) {
    return request({
        url: 'traceapp-service/announcement/update',
        method: 'post',
        data: data
    })
}
// 公告删除
export function announcementDelete(data) {
    return request({
        url: 'traceapp-service/announcement/delete',
        method: 'post',
        data: data
    })
}

// 类型列表
export function announcementTypePage(data) {
    return request({
        url: 'traceapp-service/announcementType/page',
        method: 'post',
        data: data
    })
}
// 类型添加
export function announcementTypeAdd(data) {
    return request({
        url: 'traceapp-service/announcementType/add',
        method: 'post',
        data: data
    })
}
// 类型详情
export function announcementTypeInfo(data) {
    return request({
        url: 'traceapp-service/announcementType/info',
        method: 'post',
        data: data
    })
}
// 类型编辑
export function announcementTypeUpdate(data) {
    return request({
        url: 'traceapp-service/announcementType/update',
        method: 'post',
        data: data
    })
}
// 类型删除
export function announcementTypeDelete(data) {
    return request({
        url: 'traceapp-service/announcementType/delete',
        method: 'post',
        data: data
    })
}