import request from '@/utils/request'
// import { parseStrEmpty } from "@/utils/east";
import { basicPath5 } from '@/api/base.js'
// const basicPath2 = '/screenapp-service/'
// 获取部门
export function getDepartment(data) {
    return request({
        // url: `${basicPath5}baseScreenEnterpriseInfo/list`,
        url: `screenapp-service/baseScreenEnterpriseInfo/list`,
        method: 'post',
        data,
        headers: {
            "version":"yaoyuan"
        }
    })
}
// 新增部门
export function departmentAdd(data) {
    return request({
        url: `screenapp-service/baseScreenEnterpriseInfo/save`,
        method: 'post',
        data,
        headers: {
            "version": "yaoyuan"
        }
    })
}
// 编辑部门
export function departmentEdit(data) {
    return request({
        url: `screenapp-service/baseScreenEnterpriseInfo/update`,
        method: 'post',
        data,
        headers: {
            "version": "yaoyuan"
        }
    })
}



