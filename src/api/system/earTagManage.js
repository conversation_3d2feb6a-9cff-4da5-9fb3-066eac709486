// 耳标管理
import request from '@/utils/request'
import { uploadFile } from '@/utils/request'
import { basicPath2, basicPath4 } from '@/api/base.js'
import { xmbBaseInfo } from '@/api/xmb/xmbCommon'
import { rabBaseInfo } from '@/api/ffs/xmbCommon'

// 获取耳标列表
export function getEarTagList(data) {
        data.appId = rabBaseInfo.appId
        return request({
                url: `${basicPath4}hardwareEarTag/list`,
                method: 'post',
                data: data
        })
}

// 生成耳标
export function createEarTag(data) {
        data.appId = rabBaseInfo.appId
        return request({
                url: `${basicPath4}hardwareEarTag/batchAdd`,
                method: 'post',
                data: data,
                responseType: 'arraybuffer'
        })
}

// 耳标信息上传
export function batchEdit() {
        return process.env.VUE_APP_BASE_API + `${basicPath4}hardwareEarTag/batchEdit`
}

// 根据批次号导出耳标
export function exportModel(data) {
        data.appId = rabBaseInfo.appId
        return request({
                url: `${basicPath4}hardwareEarTag/exportModel`,
                method: 'post',
                data: data,
                responseType: 'arraybuffer'
        })
}

// 导入芯片模板（批量修改）
export function importChipModel(data) {
        return process.env.VUE_APP_BASE_API + `${basicPath4}hardwareEarTag/importChipModel`
}

// 导入三方模板（批量新增）
export function importThirdPartyModel(data) {
        return process.env.VUE_APP_BASE_API + `${basicPath4}hardwareEarTag/importThirdPartyModel`
}

// 获取分组去重好了的所有批次号
export function getBatchNumberList(data) {
        data.appId = rabBaseInfo.appId
        return request({
                url: `${basicPath4}hardwareEarTag/queryBatchList`,
                method: 'post',
                data: data
        })
}
//耳标分配分页查询
export function earTaglist(data) {
    data.appId = rabBaseInfo.appId
    return request({
        url: `${basicPath4}hardwareEarTag/list`,
        method: 'post',
        data: data
    })
}
//重新分配
export function reallocation(data) {
    data.appId = rabBaseInfo.appId
    return request({
        url: `${basicPath4}hardwareEarTag/reallocation`,
        method: 'post',
        data: data
    })
}
//耳标分配接口
export function earTagAllocation(data) {
    data.appId = rabBaseInfo.appId
    return request({
        url: `${basicPath4}hardwareEarTag/earTagAllocation`,
        method: 'post',
        data: data
    })
}
//耳标统计接口
export function earTagNum(data) {
    data.appId = rabBaseInfo.appId
    return request({
        url: `${basicPath4}hardwareEarTag/groupEarTagByStatus`,
        method: 'post',
        data: data
    })
}

export function queryById(data) {
    data.appId = rabBaseInfo.appId
    return request({
        url: `${basicPath4}hardwareEarTag/queryById`,
        method: 'post',
        data: data
    })
}

// 牧场圈舍列表
export function pasturePenList(data) {
  data.appId = rabBaseInfo.appId
  return request({
    url: `${basicPath4}pastureLivestock/selectPasturePenList`,
    method: 'post',
    data: data
  })
}

// 圈舍栏位列表
export function pastureFenceCodeList(data) {
  data.appId = rabBaseInfo.appId
  return request({
    url: `${basicPath4}pastureLivestock/selectPastureFenceCodeList`,
    method: 'post',
    data: data
  })
}

