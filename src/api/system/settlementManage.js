import request from '@/utils/request'
import { basicPath4 } from '@/api/base';

// 结算管理列表
export function settlementList(data) {
    return request({
        url: basicPath4 + 'acc/settlement/page',
        method: 'post',
        data: data
    })
}
//上传发票，确认收款接口
export function settlementUpdate(data) {
    return request({
        url: basicPath4 + 'acc/settlement/update',
        method: 'post',
        data: data
    })
}
//详情接口
export function settlementInfo(data) {
    return request({
        url: basicPath4 + 'acc/settlement/info',
        method: 'post',
        data: data
    })
}
//统计接口
export function settlementSum(data) {
    return request({
        url: basicPath4 + 'acc/settlement/sumAmount',
        method: 'post',
        data: data
    })
}
//生成结算单/
export function saveSettlement(data) {
    return request({
        url: basicPath4 + 'acc/accountChecking/saveSettlement',
        method: 'post',
        data: data,
        responseType: 'arraybuffer'
    })
}
//excel表格导出
export function exportSettlement(data) {
    return request({
        url: basicPath4 + 'acc/settlement/exportSettlementList',
        method: 'post',
        data: data, responseType: 'arraybuffer'
    })
}