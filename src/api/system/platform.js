import request from '@/utils/request'
import {basicPath1,basicPath2, basicPath3} from '@/api/base'

// 查询平台管理列表
export function listPlatform(query) {
  return request({
    url: basicPath1 + '/system/platform/list',
    method: 'get',
    params: query
  })
}

// 查询平台管理详细
export function getPlatform(platformId) {
  return request({
    url: basicPath1 + '/system/platform/' + platformId,
    method: 'get'
  })
}

// 新增平台管理
export function addPlatform(data) {
  return request({
    url: basicPath1 + '/system/platform',
    method: 'post',
    data: data
  })
}

// 修改平台管理
export function updatePlatform(data) {
  return request({
    url: basicPath1 + '/system/platform',
    method: 'put',
    data: data
  })
}

// 删除平台管理
export function delPlatform(platformId) {
  return request({
    url: basicPath1 + '/system/platform/' + platformId,
    method: 'delete'
  })
}



// 运营中心-获取已经存在的服务区域集合
export function selectServiceAreaList() {
  return request({
    url: basicPath2 + 'platform/selectServiceAreaList',
    method: 'post',

  })
}


// 查询部门下拉树结构，提供给岗位数据权限使用
export function areaTreeselect(data) {
  return request({
    url: `${basicPath1}system/platform/subAreaTree`,
    method: 'post',
    data: data
  })
}

// 查询运营区域
export function platformOperateArea(query) {
  return request({
    url: basicPath1 + '/system/platform/platformOperateArea',
    method: 'get',
    params: query
  })
}

// 收款账户查询
export function selectByDataId(data) {
  return request({
    url: basicPath3 + 'acc/receiveAccount/selectByDataId',
    method: 'post',
    data: data
  })
}

// 收款账户保存
export function receiveAccountSave(data) {
  return request({
    url: basicPath3 + 'acc/receiveAccount/save',
    method: 'post',
    data: data
  })
}
