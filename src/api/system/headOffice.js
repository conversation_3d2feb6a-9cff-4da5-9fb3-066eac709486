import request from '@/utils/request'
import {basicPath1} from '@/api/base'

// 查询银行总行列表
export function listHeadOffice(query) {
  return request({
    url: basicPath1 + '/system/HeadOffice/list',
    method: 'get',
    params: query
  })
}

// 查询分行
export function branchList(query) {
  return request({
    url: basicPath1 + '/system/HeadOffice/branchList',
    method: 'get',
    params: query
  })
}

// 查询支行
export function subBranchList(query) {
  return request({
    url: basicPath1 + '/system/HeadOffice/subBranchList',
    method: 'get',
    params: query
  })
}

// 查询银行的运营区域
export function getParentBankAreaData(query) {
  return request({
    url: basicPath1 + '/system/HeadOffice/getParentBankAreaData',
    method: 'get',
    params: query
  })
}

// 新增总行
export function addHeadOffice(data) {
  return request({
    url: basicPath1 + '/system/HeadOffice',
    method: 'post',
    data: data
  })
}

// 查询企业认证详细
export function getHeadOffice(id) {
  return request({
    url: basicPath1 + 'system/HeadOffice/' + id,
    method: 'get'
  })
}

// 修改总行
export function updateHeadOffice(data) {
  return request({
    url: basicPath1 + '/system/HeadOffice',
    method: 'put',
    data: data
  })
}

// 查询一条总行数据
export function getByIdHeadOffice(query) {
  return request({
    url: basicPath1 + '/system/HeadOffice/getById',
    method: 'get',
    params: query
  })
}
// 查询所有银行
export function HeadOfficeBanks(query) {
    return request({
      url: basicPath1 + 'system/HeadOffice/banks',
      method: 'get',
      params: query
    })
  }
  //查询上级银行
  export function getSuperiorsBanks(query) {
    return request({
      url: basicPath1 + 'system/HeadOffice/getSuperiorsBanks',
      method: 'get',
      params: query
    })
  }
  //跟新上级银行
  export function updateBankRelationship(query) {
    return request({
      url: basicPath1 + 'system/HeadOffice/updateBankRelationship',
      method: 'post',
      data: query
    })
  }

