import request from '@/utils/request'
import {basicPath7} from '@/api/base.js'
// 单项统计
export function singleCount(data) {
    return request({
        url: `${basicPath7}screen/singleCount`,
        method: 'post',
        data: data
    })
}
export function announcementPage(data) {
    return request({
        url: `${basicPath7}announcement/page`,
        method: 'post',
        data: data
    })
}
export function announcementInfo(data) {
    return request({
        url: `${basicPath7}announcement/info`,
        method: 'post',
        data: data
    })
}
export function purchaseRemind(data) {
    return request({
        url: `${basicPath7}company/purchaseRemind`,
        method: 'post',
        data: data
    })
}