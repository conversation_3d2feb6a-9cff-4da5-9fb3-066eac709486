import request from '@/utils/request'
import { basicPath8 } from '@/api/base.js'
//列比
export function selectPageList(data) {
    return request({
        url: `${basicPath8}keepingProject/admin/selectPageList`,
        method: 'post',
        data: data
    })
}
//查询认养项目详情
export function selectDetailById(data) {
    return request({
        url: `${basicPath8}keepingProject/selectDetailById`,
        method: 'post',
        data: data
    })
}
//管理端删除项目
export function remove(data) {
    return request({
        url: `${basicPath8}keepingProject/admin/remove`,
        method: 'post',
        data: data
    })
}
//市县级修改项目上下架状态
export function updateShowStatus(data) {
    return request({
        url: `${basicPath8}keepingProject/admin/updateShowStatus`,
        method: 'post',
        data: data
    })
}
//市县级修改认养项目
export function update(data) {
    return request({
        url: `${basicPath8}keepingProject/admin/update`,
        method: 'post',
        data: data
    })
}
//市县级重新发布项目
export function reset(data) {
    return request({
        url: `${basicPath8}keepingProject/admin/reset`,
        method: 'post',
        data: data
    })
}
//市县级创建认养项目
export function create(data) {
    return request({
        url: `${basicPath8}keepingProject/admin/create`,
        method: 'post',
        data: data
    })
}
export function selectAgreementById(data) {
    return request({
        url: `${basicPath8}keepingProject/selectAgreementById`,
        method: 'post',
        data: data
    })
}
export function saveAgreement(data) {
    return request({
        url: `${basicPath8}keepingProject/admin/saveAgreement`,
        method: 'post',
        data: data
    })
}