import request from '@/utils/request'
import { basicPath8 } from '@/api/base.js'

// 管理端查询订单列表
export function selectPageList(data) {
  return request({
    url: `${basicPath8}order/admin/selectPageList`,
    method: 'post',
    data: data
  })
}

// 订单详情
export function selectOrderDetailById(data) {
  return request({
    url: `${basicPath8}order/admin/selectOrderDetailById`,
    method: 'post',
    data: data
  })
}

// 分配客户经理
export function setCustomer(data) {
  return request({
    url: `${basicPath8}order/admin/setCustomer`,
    method: 'post',
    data: data
  })
}

// 客户经理查询养牛资质列表
export function selectQualListById(data) {
  return request({
    url: `${basicPath8}order/admin/selectQualListById`,
    method: 'post',
    data: data
  })
}

// 管理端查询最新活畜报价
export function selectNewQuotationById(data) {
  return request({
    url: `${basicPath8}keepingProject/admin/selectNewQuotationById`,
    method: 'post',
    data: data
  })
}

// 发起结算
export function createBalance(data) {
  return request({
    url: `${basicPath8}orderCheckout/create`,
    method: 'post',
    data: data
  })
}

// 解绑活畜
export function unbindLivestock(data) {
  return request({
    url: `${basicPath8}order/admin/unbindLivestock`,
    method: 'post',
    data: data
  })
}

// 客户经理上传资质
export function uploadQual(data) {
  return request({
    url: `${basicPath8}order/admin/uploadQual`,
    method: 'post',
    data: data
  })
}

//管理端查询养牛资质列表
export function selectBindLivestockList(data) {
  return request({
    url: `${basicPath8}order/admin/selectBindLivestockList`,
    method: 'post',
    data: data
  })
}

// 运维绑定活畜
export function bindLivestock(data) {
  return request({
    url: `${basicPath8}order/admin/bindLivestock`,
    method: 'post',
    data: data
  })
}

export function orderCheckoutList(data) {
  return request({
    url: `${basicPath8}orderCheckout/admin/selectPageList`,
    method: 'post',
    data: data
  })
}
//服务公司配置保存
export function save(data) {
  return request({
    url: `${basicPath8}serviceCompany/admin/save`,
    method: 'post',
    data: data
  })
}
// 获取平台所有服务公司
export function selectList(data) {
  return request({
    url: `${basicPath8}serviceCompany/admin/selectList`,
    method: 'post',
    data: data
  })
}
// 通过名称搜索平台下的公司
export function selectSysEnterpriseList(data) {
  return request({
    url: `${basicPath8}serviceCompany/admin/selectSysEnterpriseList`,
    method: 'post',
    data: data
  })
}

// 管理端查询提现列表
export function selectWithdrawPageList(data) {
  return request({
    url: `${basicPath8}cashback/admin/selectWithdrawPageList`,
    method: 'post',
    data: data
  })
}

// 管理端查询提现详情
export function selectWithdrawDetailById(data) {
  return request({
    url: `${basicPath8}cashback/admin/selectWithdrawDetailById`,
    method: 'post',
    data: data
  })
}

// 管理端提现审核
export function auditWithdraw(data) {
  return request({
    url: `${basicPath8}cashback/admin/auditWithdraw`,
    method: 'post',
    data: data
  })
}

// 平台取消订单
export function adminOrderCancel(data) {
  return request({
    url: `${basicPath8}order/admin/cancel`,
    method: 'post',
    data: data
  })
}

// 平台对公转账确认付款
export function offlineTxSuccess(data) {
  return request({
    url: `${basicPath8}orderTx/admin/offlineTxSuccess`,
    method: 'post',
    data: data
  })
}

// 财务查询企业分账列表
export function checkoutSelectPageList(data) {
  return request({
    url: `${basicPath8}companyCheckout/admin/selectPageList`,
    method: 'post',
    data: data
  })
}

// 财务查询企业分账详情
export function selectDetailById(data) {
  return request({
    url: `${basicPath8}companyCheckout/admin/selectDetailById`,
    method: 'post',
    data: data
  })
}

// 财务查询企业分账明细列表
export function selectCheckoutAmountPageList(data) {
  return request({
    url: `${basicPath8}companyCheckout/admin/selectCheckoutAmountPageList`,
    method: 'post',
    data: data
  })
}

// 财务修改企业分账状态
export function updateCheckoutStatus(data) {
  return request({
    url: `${basicPath8}companyCheckout/admin/updateCheckoutStatus`,
    method: 'post',
    data: data
  })
}
