import request from '@/utils/request'
import { basicPath2 } from '@/api/base.js'

import {xmbBaseInfo} from "@/api/xmb/xmbCommon";
export function marketIndexPageList(data) {
    return request({
        url: `${basicPath2}quotations/list`,
        method: 'post',
        data: data
    })
}

//查看详情
export function getMarketById(data) {
    return request({
        url: `${basicPath2}quotations/info`,
        method: 'post',
        data: data
    })
}


//删除
export function deleteMarketIndex(data) {
    return request({
        url: `${basicPath2}quotations/remove`,
        method: 'post',
        data: data
    })
}

//市场行情添加
export function insertMarketIndex(data) {
    // data.appId=xmbBaseInfo.appId
    return request({
        url: `${basicPath2}quotations/add`,
        method: 'post',
        data: data
    })
}

// 编辑

export function updateMarketIndex(data) {
    return request({
        url: `${basicPath2}quotations/edit`,
        method: 'post',
        data: data
    })
}
//已选省份
export function getMarketProvinceList(data) {
    return request({
        url: `${basicPath2}marketIndex/selectMarketProvinceList`,
        method: 'post',
        data: data
    })
}

// 活畜分类
export function livestockCategory(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath2}livestock/livestockCategory/list`,
        method: 'post',
        data: data
    })
}


// 活畜品种
export function livestockVarieties(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath2}livestock/livestockVarieties/list`,
        method: 'post',
        data: data
    })
}


// 种类
export function livestockCategoryTable(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath2}livestock/livestock/list`,
        method: 'post',
        data: data
    })
}



// 行情类别列表
export function quotationTypeList(data) {
    // data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath2}quotationType/list`,
        method: 'post',
        data: data
    })
}
// 行情类别新增
export function quotationTypeAdd(data) {
    // data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath2}quotationType/add`,
        method: 'post',
        data: data
    })
}
// 行情类别编辑
export function quotationTypeEdit(data) {
    // data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath2}quotationType/edit`,
        method: 'post',
        data: data
    })
}

// 行情历史记录
export function listByTypeId(data) {
    // data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath2}quotations/listByTypeIdForPC`,
        method: 'post',
        data: data
    })
}
