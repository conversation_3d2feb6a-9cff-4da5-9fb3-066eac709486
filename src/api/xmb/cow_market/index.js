import request from "@/utils/request";
import { basicPath2 } from "@/api/base.js";
import { xmbBaseInfo } from "@/api/xmb/xmbCommon";

// 列表
export const getRegisterList = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/shop/ncsStore/list`,
    method: "post",
    data: data,
  });
};

// 审核牛超市
export const registerapprove = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/shop/ncsStore/approve`,
    method: "post",
    data: data,
  });
};

// 牛超市详情
export const selectNcsById = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/shop/ncsStore/selectNcsById`,
    method: "post",
    data: data,
  });
};

// 禁用启用牛超市
export const refreshStatus = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/shop/ncsStore/refreshStatus`,
    method: "post",
    data: data,
  });
};

// 编辑牛超市
export const ncsStoreEdit = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/shop/ncsStore/editPlatFormFee`,
    method: "post",
    data: data,
  });
};
// 列表
export const shopLivestockList = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/shopLivestock/page`,
    method: "post",
    data: data,
  });
};
// 列表导出
export const shopLivestockListExport = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/excel/ncs/export/shopLivestockList`,
    method: "post",
    data: data,
    responseType: 'blob'
  });
};
// 列表
export const shopLivestockPage = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/shopLivestock/page`,
    method: "post",
    data: data,
  });
};
// 绑定超市管理员
export const administratort = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/shopLivestock/administrator`,
    method: "post",
    data: data,
  });
};
// 店铺详情
export const shopLivestockInfo = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/shopLivestock/info`,
    method: "post",
    data: data,
  });
};
// 完善信息
export const shopLivestockRefine = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/shopLivestock/refine`,
    method: "post",
    data: data,
  });
};
export const livestockVarieties = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/livestock/livestockVarieties/list`,
    method: "post",
    data: data,
  });
};
// 绑定牧场
export const bindPasture = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/shopLivestock/bindPasture`,
    method: "post",
    data: data,
  });
};
// 绑定牧场
export const updateContact = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/shopLivestock/updateContact`,
    method: "post",
    data: data,
  });
};
// 圈舍列表
export const penList = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/pasture/pen/list`,
    method: "post",
    data: data,
  });
};
// 牛管家确认接单
export const brokerReceive = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/ncsOrder/broker/receive`,
    method: "post",
    data: data,
  });
};
// 订单列表
export const selectOrderList = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/ncsOrder/admin/selectOrderList`,
    method: "post",
    data: data,
  });
};
// 订单详情
export const selectOrderInfo = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/ncsOrder/admin/selectOrderInfo`,
    method: "post",
    data: data,
  });
};
// 订单取消
export const brokerCancel = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/ncsOrder/broker/cancel`,
    method: "post",
    data: data,
  });
};
// 查看锁定订单列表
export const selectOrderLockLivestockList = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/ncsOrder/selectOrderLockLivestockList`,
    method: "post",
    data: data,
  });
};
// 查看订单发货单
export const selectAdminPickListByOrderId = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/ncsOrderPickLivestock/selectAdminPickListByOrderId`,
    method: "post",
    data: data,
  });
};
// 牛管家确认发货
export const confirmFreight = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/ncsOrder/broker/confirmFreight`,
    method: "post",
    data: data,
  });
};
// 根据订单ID查询货品列表
export const listByOrderId = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/goodsLivestock/listByOrderId`,
    method: "post",
    data: data,
  });
};
// 根据订单ID查询货品列表
export const lockLivestock = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/ncsOrder/broker/lockLivestock`,
    method: "post",
    data: data,
  });
};
// 查询畜牧帮活畜列表
export const selectXmbLivestockByEarTag = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/ncsOrderPickLivestock/selectXmbLivestockByEarTag`,
    method: "post",
    data: data,
  });
};
// 批量耳标添加活畜
export const batchAddLivestock = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/ncsOrderPickLivestock/batchAdd`,
    method: "post",
    data: data,
  });
};
// 删除发货单活畜
export const livestockDelete = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/ncsOrderPickLivestock/delete`,
    method: "post",
    data: data,
  });
};
// 列表 - 已添加活畜
export const liveLististByOrderId = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/ncsOrderPickLivestock/listByOrderId`,
    method: "post",
    data: data,
  });
};
// 列表 - 管理端查看批量意向订单
export const selectBatchDemandList = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/ncsDemand/admin/selectBatchDemandList`,
    method: "post",
    data: data,
  });
};
// 列表 - 管理端查看长期意向订单
export const selectCycleDemandList = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/ncsDemand/admin/selectCycleDemandList`,
    method: "post",
    data: data,
  });
};
// 列表 - 管理端查看意向订单详情
export const selectDemandInfoById = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/ncsDemand/admin/selectDemandInfoById`,
    method: "post",
    data: data,
  });
};
// 登录人是否牛管家
export const adminShopList = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/shopLivestock/adminShopList`,
    method: "post",
    data: data,
  });
};
// 牧场列表
export const selectUserPastureList = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/pasture/selectUserPastureList`,
    method: "post",
    data: data,
  });
};
// 牛管家确认发货单
export const confirmLivestock = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/ncsOrder/broker/confirmLivestock`,
    method: "post",
    data: data,
  });
};
// 牛管家确认发货单
export const confirmBankTransfer = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/ncsOrder/broker/confirmBankTransfer`,
    method: "post",
    data: data,
  });
};
// 列表 - 分页 - 订购意向单可选超市列表
export const pageBySupplyDemandId = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/shopLivestock/pageBySupplyDemandId`,
    method: "post",
    data: data,
  });
};
// 列表 - 分页 - 已分配的店铺
export const ncsDemandSupplyPage = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/ncsDemandSupply/page`,
    method: "post",
    data: data,
  });
};
// 绑定超市
export const chooseShop = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/ncsDemandSupply/chooseShop`,
    method: "post",
    data: data,
  });
};
// 解绑超市
export const ncsDemandSupplyDelete = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/ncsDemandSupply/delete`,
    method: "post",
    data: data,
  });
};
// 管理端批量订购生成订单
export const createShopOrder = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/ncsDemand/admin/createShopOrder`,
    method: "post",
    data: data,
  });
};
// 牛管家称重定价
export const saveWeight = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/ncsOrder/broker/saveWeight`,
    method: "post",
    data: data,
  });
};
// 管理端长期订单日期保存
export const updatePlanByCycleDemand = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/ncsDemand/admin/updatePlanByCycleDemand`,
    method: "post",
    data: data,
  });
};
// 管理端取消订单
export const adminCancel = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/ncsOrder/admin/cancel`,
    method: "post",
    data: data,
  });
};
export const cyclePrice = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/ncsOrder/broker/cyclePrice`,
    method: "post",
    data: data,
  });
};
// 设置平台自营收款账户
export const platformSelfOperated = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/shopLivestock/platformSelfOperated`,
    method: "post",
    data: data,
  });
};
// 查询省级运营下的企业
export const shopEnterpriseList = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/shopLivestock/enterpriseList`,
    method: "post",
    data: data,
  });
};
// 查询省级运营下的企业
export const saveLivestockWeight = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/ncsOrderPickLivestock/saveLivestockWeight`,
    method: "post",
    data: data,
  });
};

// 供应商列表
export const shopMiddlemenPage = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/shopMiddlemen/page`,
    method: "post",
    data: data,
  });
};
// 供应商添加
export const shopMiddlemenAdd = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/shopMiddlemen/add`,
    method: "post",
    data: data,
  });
};
// 供应商详情
export const shopMiddlemenInfo= (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/shopMiddlemen/info`,
    method: "post",
    data: data,
  });
};
// 供应商编辑
export const shopMiddlemenUpdate = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/shopMiddlemen/update`,
    method: "post",
    data: data,
  });
};
// 供应商续约
export const shopMiddlemenRenewContract = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/shopMiddlemen/renewContract`,
    method: "post",
    data: data,
  });
};
// 供应商入驻记录
export const shopMiddlemenHistoryRecord = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/shopMiddlemen/historyRecord`,
    method: "post",
    data: data,
  });
};
export const capitalSidePage = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/capitalSide/page`,
    method: "post",
    data: data,
  });
};
export const capitalSideAdd = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/capitalSide/add`,
    method: "post",
    data: data,
  });
};
export const capitalSideUpdate = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/capitalSide/update`,
    method: "post",
    data: data,
  });
};
export const capitalSideInfo = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/capitalSide/nfo`,
    method: "post",
    data: data,
  });
};
export const ncsUserRole = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/ncs/user/ncsUserRole`,
    method: "post",
    data: data,
  });
};