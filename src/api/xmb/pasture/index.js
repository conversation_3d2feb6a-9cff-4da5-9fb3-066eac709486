import request from '@/utils/request'
import { basicPath2 } from '@/api/base.js'
let appId='2078'
// 牧场列表分页查询
export function pasturelist(data) {
    data.appId=appId
    return request({
        // url: `${basicPath2}pasture/pcPageList`,
		url: `${basicPath2}pasture/manager/page`,
        method: 'post',
        data: data
    })
}
// 收益明细
export function detailsList(data) {
    return request({
        url: `${basicPath2}accRole/income/details`,
        method: 'post',
        data: data
    })
}
//提现结算明细
export function drawalList(data) {
    return request({
        url: `${basicPath2}accRole/drawCash/details`,
        method: 'post',
        data: data
    })
}


export function pastureLivList(params) {
    data.appId = appId
    return request({
        url: `${basicPath2}pastureLivestock/list`,
        method: 'post',
        data: params
    })
}

// 动态
export function listAllDetail(params) {
    data.appId = appId
    return request({
        url: `${basicPath2}livestock/manage/listAllDetail`,
        method: 'post',
        data: params
    })
}



export function livestockArchives(params) {
  data.appId = appId
  return request({
    url: `${basicPath2}pastureLivestock/livestockArchivesList`,
    method: 'post',
    data: params
  })
}

// 谱系图
export function parentInfoByEarTagNo(params) {
    data.appId = appId
    return request({
      url: `${basicPath2}pastureLivestock/selectLivestockGenealogy`,
      method: 'post',
      data: params
    })
}
//
// 通过活畜ID查询详情
export function selectPastureLivestockById(params) {
  data.appId = appId
  return request({
    url: `${basicPath2}livestock/livestock/selectPastureLivestockById `,
    method: 'post',
    data: params
  })
}


// 货品管理列表
export function goodsLivestockList(params) {
  data.appId = appId
  return request({
    url: `${basicPath2}goodsLivestock/page`,
    method: 'post',
    data: params
  })
}
// 货品管理列表
export function goodsLivestockListExport(params) {
    data.appId = appId
    return request({
      url: `${basicPath2}excel/ncs/export/goodsLivestockList`,
      method: 'post',
      data: params,
      responseType: 'blob'
    })
}
// 货品新增
export function goodsLivestockAdd(params) {
    data.appId = appId
    return request({
      url: `${basicPath2}goodsLivestock/add`,
      method: 'post',
      data: params
    })
}
// 货品详情
export function goodsLivestockInfo(params) {
    data.appId = appId
    return request({
      url: `${basicPath2}goodsLivestock/info`,
      method: 'post',
      data: params
    })
}
// 货品更新
export function goodsLivestockUpdate(params) {
    data.appId = appId
    return request({
      url: `${basicPath2}goodsLivestock/update`,
      method: 'post',
      data: params
    })
}
// 货品下架
export function offTheShelves(params) {
    data.appId = appId
    return request({
      url: `${basicPath2}goodsLivestock/offTheShelves`,
      method: 'post',
      data: params
    })
}
// 货品审核
export function goodsLivestockAudit(params) {
    data.appId = appId
    return request({
      url: `${basicPath2}goodsLivestock/audit`,
      method: 'post',
      data: params
    })
}

/**
 * 新增活畜
 * @param params
 */
export function addLivestock(data){
  data.appId = appId
  return request({
    url: `${basicPath2}pastureLivestock/addLivestock`,
    method: 'post',
    data: data
  })
}

export function bindEarTag(data) {
  data.appId = appId
  return request({
    url: `${basicPath2}pastureLivestock/bindEarTag`,
    method: 'post',
    data: data
  })
}

export function getLivestockList(data) {
  data.appId = appId
  return request({
    url: `${basicPath2}pastureLivestock/livestockList`,
    method: 'post',
    data: data
  })
}
