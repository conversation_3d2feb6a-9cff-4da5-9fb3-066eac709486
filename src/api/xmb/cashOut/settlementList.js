import request from '@/utils/request'
import { basicPath2 ,basicPath1} from '@/api/base.js'
import { xmbBaseInfo } from '@/api/xmb/xmbCommon'

// 提现记录
export function pageList(query) {
    return request({
        url: `${basicPath1}account/drawCash/page`,
        method: 'get',
        params: query
    })
}
// 提现详情
export function drawCashInfo(query) {
    return request({
        url: `${basicPath1}account/drawCash/info`,
        method: 'get',
        params: query
    })
}
// 提现审核
export function drawCashAudit(data) {
    return request({
        url: `${basicPath1}account/drawCash/audit`,
        method: 'post',
        data: data
    })
}
// 打款
export function toPayment(data) {
    return request({
        url: `${basicPath1}account/drawCash/confirm`,
        method: 'post',
        data: data
    })
}
