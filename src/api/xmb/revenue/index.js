import request from "@/utils/request";
import { basicPath2 } from "@/api/base.js";

/**
 * @desc 区域合伙人
 * @param {*} data
 * @returns

 */

// 列表
export const partnerIncomePage = (data) => {
  return request({
    url: `${basicPath2}partner/income/page`,
    method: "post",
    data: data,
  });
};

/**
 * @desc 畜牧科技师
 * @param {*} data
 * @returns
 */

// 列表
export const pastorIncomePage = (data) => {
  return request({
    url: `${basicPath2}pastor/income/page`,
    method: "post",
    data: data,
  });
};

/**
 * @desc 畜牧师收益记录
 * @param {*} data
 * @roleId 117：畜牧师；  118：合伙人；
 * @comsiType 分账类型（分类）（1：商品交易；  2：活畜交易；  3：活体监管；  4：仓单监管   5：会员拉新)
 * @returns
 */

export const pastorIncomeInfoList = (data) => {
  return request({
    url: `${basicPath2}pastor/income/infoList`,
    method: "post",
    data: data,
  });
};

/**
 * @desc 合伙人收益记录
 * 117：畜牧师；118：合伙人；
 * 分账类型（分类）（1：商品交易；2：活畜交易；3：活体监管；4：仓单监管；5：会员拉新
 */

export const partnerIncomeInfoList = (data) => {
  return request({
    url: `${basicPath2}partner/income/infoList`,
    method: "post",
    data: data,
  });
};


/**
 * 提现记录
 */

export const accountDrawCashPage = (data) => {
  return request({
    url: `${basicPath2}account/drawCash/page`,
    method: "post",
    data: data,
  });
};

