import request from '@/utils/request'
import { basicPath2 } from '@/api/base.js'
import { xmbBaseInfo } from '@/api/xmb/xmbCommon'

// 全部打卡记录
export function pageList(data) {
    return request({
        url: `${basicPath2}pasture/clock/listAll`,
		// url: `${basicPath2}pasture/clock/selectByUserId`,
        method: 'post',
        data: data
    })
}

// 单个打卡记录
export function pageListUserId(data) {
    return request({

		url: `${basicPath2}pasture/clock/selectByUserId`,
        method: 'post',
        data: data
    })
}
// 打卡详情
export function byClockId(data) {
    return request({
        url: `${basicPath2}pasture/clock/selectByClockId`,
        method: 'post',
        data: data
    })
}
// 打卡审核
export function approveClock(data) {
    return request({
        url: `${basicPath2}pasture/clock/approveClockAboutAdmin`,
        method: 'post',
        data: data
    })
}


//  积分明细
export function integralRecordPage(data) {
    return request({
        url: `${basicPath2}integral/record/webpage`,
        method: 'post',
        data: data
    })
}
