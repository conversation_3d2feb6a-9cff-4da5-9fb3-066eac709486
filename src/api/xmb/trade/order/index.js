import request from '@/utils/request'
import { basicPath2, basicPath3 } from '@/api/base.js'
import { xmbBaseInfo } from '@/api/xmb/xmbCommon'

// 订单列表
export function orderList(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath2}order/page`,
        method: 'post',
        data: data
    })
}


// 订单详情
export function orderDetails(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath2}order/info`,
        method: 'post',
        data: data
    })
}

export function orderDetailsAnimal(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath2}order/web/info`,
        method: 'post',
        data: data
    })
}


