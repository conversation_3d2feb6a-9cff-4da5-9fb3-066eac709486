import request from '@/utils/request'
import {basicPath2} from '@/api/base.js'
import { xmbBaseInfo } from '@/api/xmb/xmbCommon'

// 活畜列表
export function varietiesList(data) {
    data.appId=xmbBaseInfo.appId
    return request({
        url: `${basicPath2}livestock/livestockVarieties/list`,
        method: 'post',
        data: data
    })
}
// 查询
export function varietiesById(data) {
    data.appId=xmbBaseInfo.appId
    return request({
        url: `${basicPath2}livestock/livestockVarieties/queryById`,
        method: 'post',
        data: data
    })
}

// 删除
export function varietiesDel(data) {
    data.appId=xmbBaseInfo.appId
    return request({
        url: `${basicPath2}livestock/livestockVarieties/delete`,
        method: 'post',
        data: data
    })
}
//修改
export function varietiesEdit(data) {
    data.appId=xmbBaseInfo.appId
    return request({
        url: `${basicPath2}livestock/livestockVarieties/edit`,
        method: 'post',
        data: data
    })
}

//新增animalTypeList
export function varietiesAdd(data) {
    data.appId=xmbBaseInfo.appId
    return request({
        url: `${basicPath2}livestock/livestockVarieties/add`,
        method: 'post',
        data: data
    })
}