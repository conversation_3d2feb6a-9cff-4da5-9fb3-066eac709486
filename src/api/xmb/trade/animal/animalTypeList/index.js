import request from '@/utils/request'
import {basicPath2} from '@/api/base.js'
import { xmbBaseInfo } from '@/api/xmb/xmbCommon'

// 活畜列表
export function animalTypeList(data) {
    data.appId=xmbBaseInfo.appId
    return request({
        url: `${basicPath2}livestock/livestockCategory/list`,
        method: 'post',
        data: data
    })
}
// 查询
export function animalTypeById(data) {
    data.appId=xmbBaseInfo.appId
    return request({
        url: `${basicPath2}livestock/livestockCategory/selectById`,
        method: 'post',
        data: data
    })
}

// 删除
export function animalTypeDel(data) {
    data.appId=xmbBaseInfo.appId
    return request({
        url: `${basicPath2}livestock/livestockCategory/delete`,
        method: 'post',
        data: data
    })
}

//修改
export function animalTypeEdit(data) {
    data.appId=xmbBaseInfo.appId
    return request({
        url: `${basicPath2}livestock/livestockCategory/edit`,
        method: 'post',
        data: data
    })
}

//新增animalTypeList
export function animalTypeAdd(data) {
    data.appId=xmbBaseInfo.appId
    return request({
        url: `${basicPath2}livestock/livestockCategory/add`,
        method: 'post',
        data: data
    })
}