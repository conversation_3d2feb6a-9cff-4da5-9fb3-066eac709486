import request from '@/utils/request'
import {basicPath2} from '@/api/base.js'
import { xmbBaseInfo } from '@/api/xmb/xmbCommon'

// 活畜列表
export function livestockList(data) {
    data.appId=xmbBaseInfo.appId
    return request({
        url: `${basicPath2}livestock/livestock/list`,
        method: 'post',
        data: data
    })
}
// 查询
export function agentById(data) {
    data.appId=xmbBaseInfo.appId
    return request({
        url: `${basicPath2}livestock/livestock/selectById`,
        method: 'post',
        data: data
    })
}

// 删除
export function livestockDel(data) {
    data.appId=xmbBaseInfo.appId
    return request({
        url: `${basicPath2}livestock/livestock/delete`,
        method: 'post',
        data: data
    })
}

//修改
export function livestockEdit(data) {
    data.appId=xmbBaseInfo.appId
    return request({
        url: `${basicPath2}livestock/livestock/edit`,
        method: 'post',
        data: data
    })
}

//新增
export function livestockAdd(data) {
    data.appId=xmbBaseInfo.appId
    return request({
        url: `${basicPath2}livestock/livestock/add`,
        method: 'post',
        data: data
    })
}