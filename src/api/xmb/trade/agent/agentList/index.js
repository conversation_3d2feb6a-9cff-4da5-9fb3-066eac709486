import request from '@/utils/request'
import {basicPath2} from '@/api/base.js'
import { xmbBaseInfo } from '@/api/xmb/xmbCommon'

// 经纪人列表
export function agentList(data) {
    data.appId=xmbBaseInfo.appId
    return request({
        url: `${basicPath2}agent/agent/list`,
        method: 'post',
        data: data
    })
}
// 经纪人查询
export function agentById(data) {
    data.appId=xmbBaseInfo.appId
    return request({
        url: `${basicPath2}agent/agent/queryById`,
        method: 'post',
        data: data
    })
}

// 经纪人审核
export function agentApprove(data) {
    data.appId=xmbBaseInfo.appId
    return request({
        url: `${basicPath2}agent/agent/agentApprove`,
        method: 'post',
        data: data
    })
}
