import request from '@/utils/request'
import {basicPath2} from '@/api/base.js'
import { xmbBaseInfo } from '@/api/xmb/xmbCommon'

// 栏位列表
export function fieldList(data) {
    return request({
        url: `${basicPath2}agentField/list`,
        method: 'post',
        data: data
    })
}
// 栏位新增
export function fieldAdd(data) {
    data.appId=xmbBaseInfo.appId
    return request({
        url: `${basicPath2}agentField/add`,
        method: 'post',
        data: data
    })
}

// 栏位编辑
export function fieldEdit(data) {
    data.appId=xmbBaseInfo.appId
    return request({
        url: `${basicPath2}agentField/edit`,
        method: 'post',
        data: data
    })
}

// 栏位删除
export function fieldDel(data) {
    data.appId=xmbBaseInfo.appId
    return request({
        url: `${basicPath2}agentField/deleteById`,
        method: 'post',
        data: data
    })
}

// 栏位详情
export function fieldById(data) {
    data.appId=xmbBaseInfo.appId
    return request({
        url: `${basicPath2}agentField/queryById`,
        method: 'post',
        data: data
    })
}

//经纪人查询
export function agentList(data) {
    data.appId=xmbBaseInfo.appId
    return request({
        url: `${basicPath2}agent/agent/queryAgentsByIds`,
        method: 'post',
        data: data
    })
}