import request from '@/utils/request'
import { basicPath2 } from '@/api/base.js'
import { xmbBaseInfo } from '@/api/xmb/xmbCommon'

/**
 * publishAnimal 发布活畜管理   活畜发布
 */


// 发布求购列表
export function livestockList(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath2}livestockRelease/list`,
        method: 'post',
        data: data
    })
}


// 发布求购详情查询
export function livestockInfo(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath2}livestockRelease/queryById`,
        method: 'post',
        data: data
    })
}


// 发布求购删除
export function livestockDel(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath2}livestockRelease/deleteById`,
        method: 'post',
        data: data
    })
}




// 发布求购审核
export function livestockExamine(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath2}livestockRelease/checkRelease`,
        method: 'post',
        data: data
    })
}


// 求购供应评论
export function commentList(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath2}/demandComment/list`,
        method: 'post',
        data: data
    })
}






// 活畜分类
export function livestockCategory(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath2}livestock/livestockCategory/list`,
        method: 'post',
        data: data
    })
}


// 活畜品种
export function livestockVarieties(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath2}livestock/livestockVarieties/list`,
        method: 'post',
        data: data
    })
}


// 种类
export function livestockCategoryTable(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath2}livestock/livestock/list`,
        method: 'post',
        data: data
    })
}

