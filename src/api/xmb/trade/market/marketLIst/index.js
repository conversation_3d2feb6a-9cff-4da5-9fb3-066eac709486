import request from '@/utils/request'
import {basicPath2} from '@/api/base.js'
import { xmbBaseInfo } from '@/api/xmb/xmbCommon'

// 市场列表
export function marketList(data) {
    data.appId=xmbBaseInfo.appId
    return request({
        url: `${basicPath2}market/market/list`,
        method: 'post',
        data: data
    })
}
// 市场新增
export function marketAdd(data) {
    data.appId=xmbBaseInfo.appId
    return request({
        url: `${basicPath2}market/market/add`,
        method: 'post',
        data: data
    })
}

// 市场编辑
export function marketEdit(data) {
    data.appId=xmbBaseInfo.appId
    return request({
        url: `${basicPath2}market/market/edit`,
        method: 'post',
        data: data
    })
}

// 市场删除
export function marketDel(data) {
    data.appId=xmbBaseInfo.appId
    return request({
        url: `${basicPath2}market/market/delete`,
        method: 'post',
        data: data
    })
}

// 市场详情
export function marketById(data) {
    data.appId=xmbBaseInfo.appId
    return request({
        url: `${basicPath2}market/market/queryById`,
        method: 'post',
        data: data
    })
}