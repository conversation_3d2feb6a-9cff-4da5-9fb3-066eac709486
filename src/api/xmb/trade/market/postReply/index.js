import request from '@/utils/request'
import { basicPath2 } from '@/api/base.js'
import { xmbBaseInfo } from '@/api/xmb/xmbCommon'

// 发布回复列表查询
export function demandCommentList(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath2}demandComment/list`,
        method: 'post',
        data: data
    })
}



// 发布回复详情查询
export function demandCommentInfo(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath2}demandComment/queryById`,
        method: 'post',
        data: data
    })
}


// 发布回复删除
export function demandCommentDel(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath2}demandComment/deleteById`,
        method: 'post',
        data: data
    })
}
