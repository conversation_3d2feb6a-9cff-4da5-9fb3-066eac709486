import request from '@/utils/request'
import { basicPath2 ,basicPath3} from '@/api/base.js'
import { xmbBaseInfo } from '@/api/xmb/xmbCommon'

// 佣金列表查询
export function commissionList(data) {
    return request({
        url: `${basicPath2}commission/page`,
        method: 'post',
        data: data
    })
}

//佣金添加
export function commissionAdd(data) {
    return request({
        url: `${basicPath2}commission/add`,
        method: 'post',
        data: data
    })
}

//佣金详情
export function commissionInfo(data) {
    return request({
        url: `${basicPath2}commission/info`,
        method: 'post',
        data: data
    })
}
//佣金修改
export function commissionEdit(data) {
    return request({
        url: `${basicPath2}commission/update`,
        method: 'post',
        data: data
    })
}

//启用和禁用
export function commissionenable(data) {
    return request({
        url: `${basicPath2}commission/enable`,
        method: 'post',
        data: data
    })
}

//佣金记录列表查询
export function commissionRecord(data) {
    data.appId=xmbBaseInfo.appId
    return request({
        url: `${basicPath2}order/commission/pageList`,
        method: 'post',
        data: data
    })
}
//佣金记录详情
export function commissioninfo(data) {
    return request({
        url: `${basicPath2}order/commission/info`,
        method: 'post',
        data: data
    })
}
//平台服务费列表查询
export function platformList(data) {
    return request({
        url: `${basicPath2}platform/page`,
        method: 'post',
        data: data
    })
}
//平台服务费新增
export function platformAdd(data) {
    data.appId=xmbBaseInfo.appId
    return request({
        url: `${basicPath2}platform/add`,
        method: 'post',
        data: data
    })
}

//平台服务费详情查看
export function platformInfo(data) {
    return request({
        url: `${basicPath2}platform/info`,
        method: 'post',
        data: data
    })
}

//平台服务费修改
export function platformUpdate(data) {
    return request({
        url: `${basicPath2}platform/update`,
        method: 'post',
        data: data
    })
}

//平台服务费上下线
export function platformEnable(data) {
    return request({
        url: `${basicPath2}platform/enable`,
        method: 'post',
        data: data
    })
}

// 新增会员费用
export const addVipAmount = (data) => {
    return request({
        url: `${basicPath2}commission/addVipAmount`,
        method: 'post',
        data: data
    })
}

// 会员费查询
export const selectVipAmount = (data) => {
    return request({
        url: `${basicPath2}commission/selectVipAmount`,
        method: 'post',
        data: data
    })
}

// 编辑会员费
export const editVipAmount = (data) => {
    return request({
        url: `${basicPath2}commission/editVipAmount`,
        method: 'post',
        data: data
    })
}


// 分佣规则列表-集团
export function commissionRuleList(data) {
    return request({
        url: `${basicPath2}commission/rule/page`,
        method: 'post',
        data: data
    })
}


// 分佣规则查看-集团
export function commissionRuleDetail(data) {
    return request({
        url: `${basicPath2}commission/rule/platList`,
        method: 'post',
        data: data
    })
}

//分佣规则修改-集团
export function commissionBatchUpdateRate(data) {
    return request({
        url: `${basicPath2}commission/plat/edit`,
        method: 'post',
        data: data
    })
}


//分账明细列表-集团
export function commissionDetailList(data) {
    return request({
        url: `${basicPath2}commission/plat/income/page`,
        method: 'post',
        data: data
    })
}

// 分佣规则列表- 运营中心
export function commissionRuleListPlatform(data) {
    return request({
        url: `${basicPath2}commission/ope/rule/page`,
        method: 'post',
        data: data
    })
}


// // 分佣规则查看-运营中心
export function commissionRuleDetailPlatform(data) {
    return request({
        url: `${basicPath2}commission/rule/platPage`,
        method: 'post',
        data: data
    })
}

//分账明细列表-运营中心
export function commissionDetailListPlatform(data) {
    return request({
        url: `${basicPath2}commission/ope/income/page`,
        method: 'post',
        data: data
    })
}



//分账明细列表-查看详情-运营中心
export function commissionDetailInfoPlatform(data) {
    return request({
        url: `${basicPath2}commission/income/info`,
        method: 'post',
        data: data
    })
}

//分佣规则修改-运营中心
export function commissionBatchUpdateRatePlatform(data) {
    return request({
        url: `${basicPath2}commission/ope/edit`,
        method: 'post',
        data: data
    })
}

//分佣规则禁用启用-集团
export function commissionUpdateStatusPlatform(data) {
    return request({
        url: `${basicPath2}commission/plat/rule/status`,
        method: 'post',
        data: data
    })
}
//分佣明细运营中心统计
export function commissionStatistics(data) {
    return request({
        url: `${basicPath2}statistics/commission/details`,
        method: 'post',
        data: data
    })
}

export function exportExcel(data){
    return request({
        url: `${basicPath2}commission/export`,
        method: 'post',
        responseType: 'blob',
        data: data
    })
}

export function exportComExcel(data){
    return request({
        url: `${basicPath2}commission/export`,
        method: 'post',
        responseType: 'blob',
        data: data
    })
}



//订单详情
export function commissionOrderInfo(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}commission/order/info`,
        method: 'post',
        data: data
    })
}


//会员拉新 详情
export function vipInfo(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath2}order/vip/info`,
        method: 'post',
        data: data
    })
}


/**
 * *额度服务费  额度配置
 */

// 新增
export const insertPlatformFeeList = (data) => {
   data.appId = xmbBaseInfo.appId
   return request({
     url: `${basicPath3}payfee/insertPlatformFeeList`,
     method: 'post',
     data: data
   })
}

// 列表
export const selectPlatformFeeList = (data) => {
    data.appId = xmbBaseInfo.appId
    return request({
      url: `${basicPath3}payfee/selectPlatformFeeList`,
      method: 'post',
      data: data
    })
 }


 // 详情
export const selectPlatformFeeInfo = (data) => {
    data.appId = xmbBaseInfo.appId
    return request({
      url: `${basicPath3}payfee/selectPayFeeByInfoId`,
      method: 'post',
      data: data
    })
 }

 // 编辑
export const updatePlatformFeeList = (data) => {
    data.appId = xmbBaseInfo.appId
    return request({
      url: `${basicPath3}payfee/updatePayFeeById`,
      method: 'post',
      data: data
    })
 }




// 分佣规则 v4.3.3 (0202)
// 分佣规则列表
export function ruleList(data) {
    return request({
        url: `${basicPath2}commission/rule/page`,
        method: 'post',
        data: data
    })
}

//分佣规则详情
export function ruleInfo(data) {
    return request({
        url: `${basicPath2}commission/rule/info`,
        method: 'post',
        data: data
    })
}

//分佣规则-平台编辑
export function platEdit(data) {
    return request({
        url: `${basicPath2}commission/plat/edit`,
        method: 'post',
        data: data
    })
}
//分佣规则-运营中心编辑
export function opeEdit(data) {
    return request({
        url: `${basicPath2}commission/ope/edit`,
        method: 'post',
        data: data
    })
}

//分佣规则-运营中心 任务类新增编辑
export function opeAddEdit(data) {
    return request({
        url: `${basicPath2}commission/plat/income/insert`,
        method: 'post',
        data: data
    })
}


//分佣规则-运营中心(集团) 编辑-周建提供
export function commissionEdit1(data) {
    return request({
        url: `${basicPath2}commission/edit`,
        method: 'post',
        data: data
    })
}

//新增分佣规则
export function commissionConfigCreate(data) {
    return request({
        url: `${basicPath3}acc/commissionConfig/create`,
        method: 'post',
        data: data
    })
}
//修改分佣规则
export function commissionConfigUpdate(data) {
    return request({
        url: `${basicPath3}acc/commissionConfig/update`,
        method: 'post',
        data: data
    })
}
//分佣规则查询
export function commissionConfigSelectPageList(data) {
    return request({
        url: `${basicPath3}acc/commissionConfig/selectPageList`,
        method: 'post',
        data: data
    })
}
//分佣规则查询
export function commissionConfigInfo(data) {
    return request({
        url: `${basicPath3}acc/commissionConfig/selectById`,
        method: 'post',
        data: data
    })
}
//获取查询的平台列表
export function selectQueryPlatformList(data) {
    return request({
        url: `${basicPath3}acc/tradeSettlement/selectQueryPlatformList`,
        method: 'post',
        data: data
    })
}
//账单查询
export function tradeSettlementSelectPageList(data) {
    return request({
        url: `${basicPath3}acc/tradeSettlement/selectPageList`,
        method: 'post',
        data: data
    })
}
//账单详情
export function tradeSettlementSelectDetail(data) {
    return request({
        url: `${basicPath3}acc/tradeSettlement/selectDetailById`,
        method: 'post',
        data: data
    })
}
//生成结算单
export function createSupplierSettlement(data) {
    return request({
        url: `${basicPath3}acc/tradeSettlement/createSupplierSettlement`,
        method: 'post',
        data: data
    })
}
//生成结算单
export function updatePaymentStatus(data) {
    return request({
        url: `${basicPath3}acc/tradeSettlement/updatePaymentStatus`,
        method: 'post',
        data: data
    })
}
//生成结算单
export function updateReceiveStatus(data) {
    return request({
        url: `${basicPath3}acc/tradeSettlement/updateReceiveStatus`,
        method: 'post',
        data: data
    })
}
//供应商结算信息预览
export function selectPreviewSupplierById(data) {
    return request({
        url: `${basicPath3}acc/tradeSettlement/selectPreviewSupplierById`,
        method: 'post',
        data: data
    })
}
//佣金结算信息预览
export function selectPreviewCommissionById(data) {
    return request({
        url: `${basicPath3}acc/tradeSettlement/selectPreviewCommissionById`,
        method: 'post',
        data: data
    })
}
//佣金结算信息预览
export function createCommissionSettlement(data) {
    return request({
        url: `${basicPath3}acc/tradeSettlement/createCommissionSettlement`,
        method: 'post',
        data: data
    })
}
//生成账单
export function createTradeSettlement(data) {
    return request({
        url: `${basicPath3}acc/tradeSettlement/createTradeSettlement`,
        method: 'post',
        data: data
    })
}
//佣金结算信息查询
export function selectChildSettlementList(data) {
    return request({
        url: `${basicPath3}acc/tradeSettlement/selectChildSettlementList`,
        method: 'post',
        data: data
    })
}
