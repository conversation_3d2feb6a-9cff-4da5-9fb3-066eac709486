import request from "@/utils/request";
import { basicPath3 } from "@/api/base.js";
import { xmbBaseInfo } from "@/api/xmb/xmbCommon";
import store from "@/store";

console.log(store.state.user.user.platformId);
let platformId = store.state.user.user.platformId;
let createBy = store.state.user.user.userId;
/**
 *
 * @param {配送点管理}
 */

// 配送管理列表接口
export function expressConfigList(data) {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath3}goodsExpress/selectGoodsExpressConfigList`,
    method: "post",
    data: data,
  });
}

// 配送管理新增接口
export function addExpressConfig(data) {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath3}goodsExpress/insertGoodsExpressConfig`,
    method: "post",
    data: data,
  });
}

// 配送管理删除接口
export function delExpressConfig(data) {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath3}goodsExpress/deleteGoodsExpress`,
    method: "post",
    data: data,
  });
}

// 配送管理修改接口
export function updateExpressConfig(data) {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath3}goodsExpress/updateGoodsExpressConfig`,
    method: "post",
    data: data,
  });
}

// 配送管理查询接口-详情
export function expressConfigInfo(data) {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath3}goodsExpress/selectGoodsExpressConfigByExpressId`,
    method: "post",
    data: data,
  });
}

/**
 * @param { 配送地址 } String
 *
 */

// 添加
export const insertDeliveryArea = (data) => {
  data.appId = 2078;
  data.platformId = platformId;
  data.createBy = createBy;
  return request({
    url: `${basicPath3}goodsfreight/insertDeliveryArea`,
    method: "post",
    data: data,
  });
};
// 编辑
export const updateDeliveryArea = (data) => {
  data.appId = 2078;
  data.platformId = platformId;
  data.createBy = createBy;
  return request({
    url: `${basicPath3}goodsfreight/updateDeliveryArea`,
    method: "post",
    data: data,
  });
};

// 查询
export const selectDeliveryArea = (data) => {
  data.appId = 2078;
  data.platformId = platformId;
  data.createBy = createBy;
  return request({
    url: `${basicPath3}goodsfreight/selectDeliveryArea`,
    method: "post",
    data: data,
  });
};

/**
 * @param { 用费模版默认配置修改 } String
 */

//修改
export const updateCotrlStatus = (data) => {
  data.appId = 2078;
  data.platformId = platformId;
  data.createBy = createBy;
  return request({
    url: `${basicPath3}goodsfreight/updateCotrlStatus`,
    method: "post",
    data: data,
  });
};

// 查询
export const selectDefauleGoodsFreight = (data) => {
  data.appId = 2078;
  data.platformId = platformId;
  data.createBy = createBy;
  return request({
    url: `${basicPath3}goodsfreight/selectDefauleGoodsFreight`,
    method: "post",
    data: data,
  });
};

/**
 *
 * @param { 配送规则 } String
 */

// 商品配送费列表
export const goodsFreightList = (data) => {
  data.appId = 2078;
  data.platformId = platformId;
  data.createBy = createBy;
  return request({
    url: `${basicPath3}goodsfreight/selectGoodsFreightList`,
    method: "post",
    data: data,
  });
};

// 创建规则
export const insertGoodsFreight = (data) => {
  data.appId = 2078;
  data.platformId = platformId;
  data.createBy = createBy;
  return request({
    url: `${basicPath3}goodsfreight/insertGoodsFreight`,
    method: "post",
    data: data,
  });
};

// 规则关联商品列表
export const freightGoodsList = (data) => {
  data.appId = 2078;
  data.platformId = platformId;
  data.createBy = createBy;
  return request({
    url: `${basicPath3}goodsfreight/selectGoodsFreightGoodsList`,
    method: "post",
    data: data,
  });
};

// 设置默认配送规则
export const updateSetDefault = (data) => {
  data.appId = 2078;
  data.platformId = platformId;
  data.createBy = createBy;
  return request({
    url: `${basicPath3}goodsfreight/updateSetDefault`,
    method: "post",
    data: data,
  });
};

// 删除配送规则
export const deleteGoodsFreightById = (data) => {
  data.appId = 2078;
  data.platformId = platformId;
  data.createBy = createBy;
  return request({
    url: `${basicPath3}goodsfreight/deleteGoodsFreightById`,
    method: "post",
    data: data,
  });
};

// 配送规则详情
export const selectGoodsFreightInfo = (data) => {
  data.appId = 2078;
  data.platformId = platformId;
  data.createBy = createBy;
  return request({
    url: `${basicPath3}goodsfreight/selectGoodsFreightInfo`,
    method: "post",
    data: data,
  });
};

// 编辑配送规则
export const updateGoodsFreight = (data) => {
  data.appId = 2078;
  data.platformId = platformId;
  data.createBy = createBy;
  return request({
    url: `${basicPath3}goodsfreight/updateGoodsFreight`,
    method: "post",
    data: data,
  });
};
