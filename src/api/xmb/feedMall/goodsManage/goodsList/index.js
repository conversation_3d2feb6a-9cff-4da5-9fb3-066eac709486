import request from '@/utils/request'
import { basicPath3 } from '@/api/base.js'
import { xmbBaseInfo } from '@/api/xmb/xmbCommon'


/**
 *
 * @param {商品管理}
 */

// 商品单位列表
export function goodsUnit(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}goodsUnit/selectGoodsUnitList`,
        method: 'post',
        data: data
    })
}


// 商品列表
export function getGoodsList(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        // url: `${basicPath3}goods/selectGoodsList`,
        url: `${basicPath3}goods/page`,
        method: 'post',
        data: data
    })
}


// 商品分类列表 全部分类
export function gootsCategoryList(data) {
    data.appId = xmbBaseInfo.appId
    data.pageNum=1
    data.pageSize=100
    return request({
        // url: `${basicPath3}gootsCategory/selectGootsCategoryList`,
        url: `${basicPath3}goods/category/page`,
        method: 'post',
        data: data
    })
}

// 商品新增
export function addGoods(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        // url: `${basicPath3}goods/insertGoods`,
        url: `${basicPath3}goods/add`,
        method: 'post',
        data: data
    })
}

// 商品修改
/*
* 牛超市版本新增服务费配置，修改商品编辑接口
*/
/* export function updateGoods(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}goods/updateGoods`,
        method: 'post',
        data: data
    })
} */
export function updateGoods(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}payfee/updatePayFee`,
        method: 'post',
        data: data
    })
}

// 重新上架商品

export function putOnGoods(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}goods/updateGoods`,
        method: 'post',
        data: data
    })
}



// 根据商品id查询单个商品
export function goodsInfo(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}goods/selectGoodsInfo`,
        method: 'post',
        data: data
    })
}


// 商品删除，支持批量删除和单个删除
export function deleteGoodsByIds(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}goods/deleteGoodsByBatchIds`,
        method: 'post',
        data: data
    })
}


// 商品统计(出售中（数量）、已售罄（数量）、草稿（数量）、审核中（数量）统计
export function selectGoodsCount(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}goods/selectGoodsCount`,
        method: 'post',
        data: data
    })
}


// 批量上架
export function goodsPut(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}goods/uppershelves`,
        method: 'post',
        data: data
    })
}


// 批量下架
export function goodsUp(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}goods/down/shelves`,
        method: 'post',
        data: data
    })
}




// 品牌列表查询
export function goodsBrandList(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}goodsBrand/selectGoodsBrandList`,
        method: 'post',
        data: data
    })
}


// 商品审核
export function examineGoodsById(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}goods/examineGoodsById`,
        method: 'post',
        data: data
    })
}



/**
 * 商品上架状态直接编辑
 */
export function resetEdit(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}goods/edit`,
        method: 'post',
        data: data
    })
}





/**
 * 社会化服务版本新增接口
 */

// 下架
export function shelves(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}goods/down/shelves`,
        method: 'post',
        data: data
    })
}


// 重新上架商品
export function goodsUpdate(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}goods/update`,
        method: 'post',
        data: data
    })
}

// 商品详情
export function selectedGoodInfo(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}goods/info`,
        method: 'post',
        data: data
    })
}
// 商品审核
export function goodsAudit(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}goods/audit`,
        method: 'post',
        data: data
    })
}


