import request from '@/utils/request'
import { basicPath3 } from '@/api/base.js'
import { xmbBaseInfo } from '@/api/xmb/xmbCommon'

// 查询品牌列表
export function listBrand(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}goodsBrand/selectGoodsBrandList`,
        method: 'post',
        data: data
    })
}

// 查询品牌详细
export function getBrand(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: basicPath3 + 'goodsBrand/selectGoodsBrandInfo',
        method: 'post',
        data: data
    })
}

// 新增品牌
export function addBrand(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: basicPath3 + 'goodsBrand/insertGoodsBrand',
        method: 'post',
        data: data
    })
}

// 修改品牌
export function updateBrand(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: basicPath3 + 'goodsBrand/updateGoodsBrand',
        method: 'post',
        data: data
    })
}

// 删除品牌
export function delBrand(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: basicPath3 + 'goodsBrand/deleteGoodsBrandById',
        method: 'post',
        data: data
    })
}
