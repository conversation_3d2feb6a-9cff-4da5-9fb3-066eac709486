import request from '@/utils/request'
import { basicPath3, basicPath2 } from '@/api/base.js'
import { xmbBaseInfo } from '@/api/xmb/xmbCommon'
/**
 *
 * @param {订单管理}
 */

// 订单列表
export function orderList(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}order/mall/page`,
        method: 'post',
        data: data
    })
}


// 订单详情
export function orderInfo(data) {
     data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}order/info`,
        method: 'post',
        data: data
    })
}
//订单发货
export function orderDelivery(data) {
     data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}order/delivery`,
        method: 'post',
        data: data
    })
}
//取消订单/
export function orderCancel(data) {
     data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}order/cancel`,
        method: 'post',
        data: data
    })
}
//确认收货
export function orderReceive(data) {
     data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}order/receive`,
        method: 'post',
        data: data
    })
}
//修改订单价格
export function orderAmount(data) {
     data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}order/amount`,
        method: 'post',
        data: data
    })
}

// 线下收款
export function enterOffline(data) {
    data.appId = xmbBaseInfo.appId
   return request({
       url: `${basicPath3}order/offlineReceipt`,
       method: 'post',
       data: data
   })
}


// 导出excel
export function exportExcel(data){
    return request({
        url: `${basicPath3}order/orderexport`,
        method: 'post',
        responseType: 'blob',
        data: data
    })
}


export const updateFreightFee = (data) => {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}order/updateFreightFee`,
        method: 'post',
        data: data
    })
}
export const selectGoodList = (data) => {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}order/goods/pageList`,
        method: 'post',
        data: data
    })
}

/**
 * 4.3.5 导出
 */
export function orderExport(data){
    return request({
        url: `${basicPath3}order/export`,
        method: 'post',
        responseType: 'blob',
        data: data
    })
}


// 接单
export function orderAccept(data){
    return request({
        url: `${basicPath3}order/accept`,
        method: 'post',
        data: data
    })
}

/**
 *
 * 关单
 */

export function orderClose(data){
    return request({
        url: `${basicPath3}order/close`,
        method: 'post',
        data: data
    })
}

// 上传附件
export function editOrderFile(data){
    return request({
        url: `${basicPath3}order/editOrderFile`,
        method: 'post',
        data: data
    })
}

// 确认收款
export function confirmThePayment(data){
    return request({
        url: `${basicPath3}order/confirmThePayment`,
        method: 'post',
        data: data
    })
}
    