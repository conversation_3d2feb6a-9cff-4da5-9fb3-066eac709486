import request from '@/utils/request'
import { basicPath3 } from '@/api/base.js'
import { xmbBaseInfo } from '@/api/xmb/xmbCommon'
/**
 *
 * @param {自提点管理}
 */

// 自提点列表接口
export function placeList(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        // url: `${basicPath3}goodsPlace/selectGoodsPickUpPlaceList`,
        url: `${basicPath3}station/page`,
        method: 'post',
        data: data
    })
}



// 自提点新增接口
export function addPlace(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        // url: `${basicPath3}goodsPlace/insertGoodsPickUpPlace`,
        url: `${basicPath3}station/add`,
        method: 'post',
        data: data
    })
}


// 自提点修改接口
export function updatePlace(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}station/edit`,
        method: 'post',
        data: data
    })
}


// 自提点单个查询接口
export function placeOne(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}station/info`,
        method: 'post',
        data: data
    })
}


// 自提点删除接口
export function delPlace(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}station/delete`,
        method: 'post',
        data: data
    })
}

