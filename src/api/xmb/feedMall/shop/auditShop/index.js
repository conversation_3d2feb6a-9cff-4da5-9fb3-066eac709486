import request from '@/utils/request'
import { basicPath3 } from '@/api/base.js'
import { xmbBaseInfo } from '@/api/xmb/xmbCommon'


//店铺列表查询
export function storeList(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        // url: `${basicPath3}goodsStore/selectStoreList`,
        url: `${basicPath3}store/page`,
        method: 'post',
        data: data
    })
}
//店铺详情
export function storeInfo(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}goodsStore/selectStoreInfo`,
        method: 'post',
        data: data
    })
}
// //店铺审核
export function storeUpdata(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}goodsStore/updateStore`,
        method: 'post',
        data: data
    })
}
//审核通过
export function examineUpdata(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}goodsStore/examineStore`,
        method: 'post',
        data: data
    })
}
//审核通过
export function rejectUpdata(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}goodsStore/rejectStore`,
        method: 'post',
        data: data
    })
}
//跨区域申请审批列表
export function selectStoreEditApprovePageList(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}store/selectStoreEditApprovePageList`,
        method: 'post',
        data: data
    })
}
//跨区域申请审批
export function storeEditApprove(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}store/storeEditApprove`,
        method: 'post',
        data: data
    })
}
//跨区域申请审批详情
export function selectStoreEditApprovalById(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}store/selectStoreEditApprovalById`,
        method: 'post',
        data: data
    })
}