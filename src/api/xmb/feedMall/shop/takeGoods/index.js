import request from '@/utils/request'
import { basicPath3 } from '@/api/base.js'
import { xmbBaseInfo } from '@/api/xmb/xmbCommon'


// 查看提货列表
export const listForPC = (data) => {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}pickUpManage/listForPC`,
        method: 'post',
        data: data
    })
}

// 确认核销
export const updateStatus = (data) => {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}pickUpManage/updateStatus`,
        method: 'post',
        data: data
    })
}

//确认到货
export const confirmArrival = (data) => {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}pickUpManage/confirmArrival`,
        method: 'post',
        data: data
    })
}

// 提货ID查看详情
export const selectById = (data) => {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}pickUpManage/selectById`,
        method: 'post',
        data: data
    })
}