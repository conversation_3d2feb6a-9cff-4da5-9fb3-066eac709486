import request from '@/utils/request'
import { basicPath3 } from '@/api/base.js'
import { xmbBaseInfo } from '@/api/xmb/xmbCommon'
//店铺查询
export function storeList(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}goodsStore/selectStoreByInByPhone`,
        method: 'post',
        data: data
    })
}
//店铺新增
export function AddStore(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}goodsStore/insertStore`,
        method: 'post',
        data: data
    })
}

//店铺修改
export function updateStore(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}goodsStore/updateStore`,
        method: 'post',
        data: data
    })
}
//查看详情
export function   detailStore(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}goodsStore/selectStoreInfo`,
        method: 'post',
        data: data
    })
}
//获取地区
export function area(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}baseArea/list`,
        method: 'post',
        data: data
    })
}
//店铺获取可跨区域的运营平台列表
export function selectCrossPlatformList(data) {
    // data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}store/selectCrossPlatformList`,
        method: 'post',
        data: data
    })
}
//创建店铺跨区域申请
export function createStoreCrossAreaApply(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}store/createStoreCrossAreaApply`,
        method: 'post',
        data: data
    })
}
//跨区域申请审批列表
export function selectStoreEditApprovePageList(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}store/selectStoreEditApprovePageList`,
        method: 'post',
        data: data
    })
}
//跨区域申请审批
export function storeEditApprove(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}store/storeEditApprove`,
        method: 'post',
        data: data
    })
}
//跨区域申请审批详情
export function selectStoreEditApprovalById(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}store/selectStoreEditApprovalById`,
        method: 'post',
        data: data
    })
}
//最后一次拒绝的审批记录
export function selectLastRefuseApprovalByStoreId(data) {
    data.appId = xmbBaseInfo.appId
    return request({
        url: `${basicPath3}store/selectLastRefuseApprovalByStoreId`,
        method: 'post',
        data: data
    })
}