import request from '@/utils/request'
import { basicPath4 } from '@/api/base'
// 仓库列表
export function warehouseList(data) {
    return request({
        url: 'mesapp-service/warehouse/page',
        method: 'post',
        data: data
    })
}





// 产品类型列表
export function productTypeList(data) {
    return request({
        url: 'mesapp-service/productType/page',
        method: 'post',
        data: data
    })
}

// 消费信息
export function settlementInfo(data) {
    return request({
        url: '/mesapp-service/settlement/info',
        method: 'post',
        data: data
    })
}
//采购信息
export function settlementdetail(data) {
    return request({
        url: '/mesapp-service/settlement/detail',
        method: 'post',
        data: data
    })
}

//检斤单
export function settlementWeigthDetail(data) {
    return request({
        url: '/mesapp-service/settlement/weight/detail',
        method: 'post',
        data: data
    })
}
//补扣明细
export function settlementButcherDetail(data) {
    return request({
        url: '/mesapp-service/settlement/butcherFee/detail',
        method: 'post',
        data: data
    })
}

//出库明细
export function inventoryPage(data) {
    return request({
        url: '/mesapp-service/inventory/page',
        method: 'post',
        data: data
    })
}
// 入库记录 -- 详情
export function inventoryInfo(data) {
    return request({
        url: '/mesapp-service/inventory/info',
        method: 'post',
        data: data
    })
}
//导出
export function inventoryExport(data) {
    return request({
        url: '/mesapp-service/inventory/export',
        method: 'post',
        responseType: 'blob',
        data: data
    })
}

//库存查询
export function groupByProduct(data) {
    return request({
        url: '/mesapp-service/warehouse/groupByProduct',
        method: 'post',
        data: data
    })
}
//排酸
export function acidTaskPage(data) {
    return request({
        url: '/mesapp-service/acidTask/page',
        method: 'post',
        data: data
    })
}
//排酸导出
export function acidTaskExport(data) {
    return request({
      url: `/mesapp-service/acidTask/export`,
      method: 'post',
      data: data,
      responseType: 'blob'
    })
  }
  // 库存导出
export function groupByProductExport(data) {
    return request({
        url: '/mesapp-service/warehouse/groupByProduct/export',
        method: 'post',
        responseType: 'blob',
        data: data
    })
}
//上传附件

export function photoUpload(data) {
    return request({
        url: basicPath4 +'supervise/photoUpload',
        method: 'post',
        data: data
    })
}

// 粮仓无货质押

// 仓库列表
export function warehouseListWms(data) {
    return request({
        url: 'wmsapp-service/warehouse/list',
        method: 'post',
        data: data
    })
}

// 物料列表
export function materialsTypeList(data) {
    return request({
        url: 'wmsapp-service/materialsType/list',
        method: 'post',
        data: data
    })
}
// 物料品种列表
export function materialsVarietyList(data) {
    return request({
        url: 'wmsapp-service/materialsVariety/list',
        method: 'post',
        data: data
    })
}

// 等级列表
export function materialsLevelList(data) {
    return request({
        url: 'wmsapp-service/materialsLevel/list',
        method: 'post',
        data: data
    })
}

// 库存管理列表
export function inventoryList(data) {
    return request({
        url: 'wmsapp-service/inventory/warehouse/page',
        method: 'post',
        data: data
    })
}

// 库存导出
export function warehouseExport(data) {
    return request({
        url: 'wmsapp-service/excel/export/inventory/warehouse',
        method: 'post',
        data: data,
        responseType: 'blob'
    })
}


// 入库明细列表
export function checkInList(data) {
    return request({
        url: 'wmsapp-service/checkIn/company/page',
        method: 'post',
        data: data
    })
}

// 入库明细列表 - 导出
export function checkInExport(data) {
    return request({
        url: 'wmsapp-service/excel/export/checkIn',
        method: 'post',
        data: data,
        responseType: 'blob'
    })
}

// 入库信息列表（详情中的）
export function checkInRecordList(data) {
    return request({
        url: 'wmsapp-service/inventory/record/page',
        method: 'post',
        data: data
    })
}

// 下载入库单
export function checkInDocumentsExport(data) {
    return request({
        url: 'wmsapp-service/excel/export/checkIn/documents',
        method: 'post',
        data: data,
        responseType: 'blob'
    })
}


// 出库明细列表
export function checkOutPage(data) {
    return request({
        url: 'wmsapp-service/checkOut/company/page',
        method: 'post',
        data: data
    })
}

// 出库明细列表 - 导出
export function checkOutExport(data) {
    return request({
        url: 'wmsapp-service/excel/export/checkOut',
        method: 'post',
        data: data,
        responseType: 'blob'
    })
}

// 入库、出库记录-详情
export function inventoryRecordPage(data) {
    return request({
        url: 'wmsapp-service/inventory/record/page',
        method: 'post',
        data: data
    })
}

// 下载出库单
export function checkOutDocumentsExport(data) {
    return request({
        url: 'wmsapp-service/excel/export/checkOut/documents',
        method: 'post',
        data: data,
        responseType: 'blob'
    })
}

