import request from "@/utils/request";
import { basicPath4, basicPath1 } from "@/api/base";
import { rabBaseInfo } from "@/api/ffs/xmbCommon";

// 查询调研列表
export function list(data) {
  data.appId = rabBaseInfo.appId;
  return request({
    url: basicPath4 + "investigation/list",
    method: "post",
    data: data,
  });
}

// 查询调研详情
export function queryById(data) {
  data.appId = rabBaseInfo.appId;
  return request({
    url: basicPath4 + "investigation/queryById",
    method: "post",
    data: data,
  });
}

//根据意向单id查询单条调研单详情
export function selectOne(data) {
  data.appId = rabBaseInfo.appId;
  return request({
    url: basicPath4 + "investigation/selectByIntentionListId",
    method: "post",
    data: data,
  });
}

// 放款银行列表
export function lendingBank(data) {
  return request({
    url: basicPath4 + "/enterprise/list",
    method: "post",
    data: data,
  });
}

// 信贷员列表
export function loanOfficerList(data) {
  data.appId = rabBaseInfo.appId;
  return request({
    url: basicPath4 + "enterprise/getBindUser",
    method: "post",
    data: data,
  });
}

// 提交调研意见--运营评估活体或仓单信息
export function submitAssess(data) {
  data.appId = rabBaseInfo.appId;
  return request({
    url: basicPath4 + "investigation/assessStorage",
    method: "post",
    data: data,
  });
}

// 下发调研任务新增
export function liveAdd(data) {
  data.appId = rabBaseInfo.appId;
  return request({
    url: basicPath4 + "investigation/add",
    method: "post",
    data: data,
  });
}
//保存活体质押调研
export function directAdd(data) {
  data.appId = rabBaseInfo.appId;
  return request({
    url: basicPath4 + "investigation/directAdd",
    method: "post",
    data: data,
  });
}
//保存草稿
export function provisionalSave(data) {
  data.appId = rabBaseInfo.appId;
  return request({
    url: basicPath4 + "investigation/provisionalSave",
    method: "post",
    data: data,
  });
}

//新增计分项
export function addSit(data) {
  return request({
    url: basicPath4 + "sit/add",
    method: "post",
    data: data,
  });
}
//获取积分列表
export function sitPage(data) {
  return request({
    url: basicPath4 + "sit/page",
    method: "post",
    data: data,
  });
}
//更改是否记分
export function updateSit(data) {
  return request({
    url: basicPath4 + "sit/update",
    method: "post",
    data: data,
  });
}
//添加分数等级区间
export function sitAddScores(data) {
  return request({
    url: basicPath4 + "sit/addscores",
    method: "post",
    data: data,
  });
}
//编辑计分项
export function sitEdit(data) {
  return request({
    url: basicPath4 + "sit/updatedetail",
    method: "post",
    data: data,
  });
}
//获取分数等级回显及编辑
export function editScores(data) {
  return request({
    url: basicPath4 + "/sit/scores",
    method: "post",
    data: data,
  });
}
//编辑分数等级提交
export function editScoresSubmit(data) {
  return request({
    url: basicPath4 + "/sit/updatescores",
    method: "post",
    data: data,
  });
}
