import request from "@/utils/request";
import { basicPath4, basicPath2 } from "@/api/base";
import { rabBaseInfo } from "@/api/ffs/xmbCommon";

// 查询意向单列表列表
export function intentionlist(params) {
  params.appId = rabBaseInfo.appId;
  return request({
    url: basicPath4 + "intentionlist/page",
    method: "post",
    data: params,
  });
}
//根据意向单的id查看详情

export function intentionInfo(params) {
  params.appId = rabBaseInfo.appId;
  return request({
    url: basicPath4 + "intentionlist/info",
    method: "post",
    data: params,
  });
}
//根据意向单id 查看调研单
export function ByIntentionInfo(params) {
  params.appId = rabBaseInfo.appId;
  return request({
    url: basicPath4 + "investigation/selectByIntentionListId",
    method: "post",
    data: params,
  });
}
//上传监管方案
export function upfile(params) {
  params.appId = rabBaseInfo.appId;
  return request({
    url: basicPath4 + "investigation/onlyConfirm",
    method: "post",
    data: params,
  });
}

//评估调研单
export function submitAssessnew(params) {
  params.appId = rabBaseInfo.appId;
  return request({
    url: basicPath4 + "investigation/assess",
    method: "post",
    data: params,
  });
}

//评估调研单(不提交调研表，只有评估数据)
export function submitOnlyAssessnew(params) {
  params.appId = rabBaseInfo.appId;
  return request({
    url: basicPath4 + "investigation/onlyAssess",
    method: "post",
    data: params,
  });
}

//根据意向单id查询单条调研单详情
export function byinvestigationId(params) {
  params.appId = rabBaseInfo.appId;
  return request({
    url: basicPath4 + "investigation/selectByIntentionListId",
    method: "post",
    data: params,
  });
}

//畜牧师列表查询
export function pastorList(params) {
  return request({
    url: basicPath2 + "pastor/list",
    method: "post",
    data: params,
  });
}

//下发调研任务--委托调研
export function addByIntentionList(params) {
  params.appId = rabBaseInfo.appId;
  return request({
    url: basicPath4 + "investigation/addByIntentionList",
    method: "post",
    data: params,
  });
}

//待确认状态的意向单 审核（拒绝 或者接受）
export function auditIntention(params) {
  params.appId = rabBaseInfo.appId;
  return request({
    url: basicPath4 + "intentionlist/audit",
    method: "post",
    data: params,
  });
}
//银行驳回

export function reject(params) {
  params.appId = rabBaseInfo.appId;
  return request({
    url: basicPath4 + "intentionlist/reject ",
    method: "post",
    data: params,
  });
}

// 被监管方管理列表
export function supervisePartyTable(params) {
  params.appId = rabBaseInfo.appId;
  return request({
    url: basicPath4 + "superviseApplyer/list",
    method: "post",
    data: params,
  });
}
//意向单删除
export function delteById(params) {
    params.appId = rabBaseInfo.appId;
    return request({
      url: basicPath4 + "intentionlist/delteById",
      method: "post",
      data: params,
    });
  }