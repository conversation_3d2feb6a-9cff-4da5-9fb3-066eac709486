import request from '@/utils/request'
import {basicPath4, basicPath2, basicPath1} from '@/api/base'
import { rabBaseInfo } from '@/api/ffs/xmbCommon'
import { xmbBaseInfo } from '@/api/xmb/xmbCommon'
import axios from 'axios'
//监管单列表--gx
export function superviseList(params) {
        params.appId = rabBaseInfo.appId
        return request({
                url: basicPath4 + 'supervise/page',
                method: 'post',
                data: params
        })
}

//保险单到期监管单列表--gx
export function queryInsuranceEndTimeData(params) {
        params.appId = rabBaseInfo.appId
        return request({
                url: basicPath4 + 'supervise/queryInsuranceEndTimeData',
                method: 'post',
                data: params
        })
}

//意向单列表
export function investigationList(params) {
        params.appId = rabBaseInfo.appId
        return request({
                url: basicPath4 + 'intentionlist/page',
                method: 'post',
                data: params
        })
}

//创建监管单--提交保存
export function superviseSave(params) {
        params.appId = rabBaseInfo.appId
        return request({
                url: basicPath4 + 'supervise/save',
                method: 'post',
                data: params
        })
}


//创建监管单--提交草稿
export function superviseSaveDraft(params) {
        params.appId = rabBaseInfo.appId
        return request({
                url: basicPath4 + 'supervise/saveDraft',
                method: 'post',
                data: params
        })
}

//创建监管单--查询详情
export function superviseInfo(params) {
        params.appId = rabBaseInfo.appId
        return request({
                url: basicPath4 + 'supervise/info',
                method: 'post',
                data: params
        })
}




//根据意向单id查询单条调研单详情
export function byinvestigationId(params) {
        params.appId = rabBaseInfo.appId
        return request({
                url: basicPath4 + 'investigation/selectByIntentionListId',
                method: 'post',
                data: params
        })
}


//信贷员列表
export function loanOfficerList(params) {
        params.appId = rabBaseInfo.appId
        return request({
                url: basicPath4 + 'enterprise/getBindUser',
                method: 'post',
                data: params
        })
}

//牧场列表
export function getPastureList(params) {
        params.appId = rabBaseInfo.appId
        return request({
                url: basicPath4 + 'pasture/list',
                method: 'post',
                data: params
        })
}

//选择活畜列表
export function selectbatchInsert(params) {
        params.appId = rabBaseInfo.appId
        return request({
                url: basicPath4 + 'pastureLivestock/list',
                method: 'post',
                data: params
        })
}

//保存选择的活畜列表
export function pastureLivestockGroupCount(params) {
        params.appId = rabBaseInfo.appId
        return request({
                url: basicPath4 + 'pastureLivestock/pastureLivestockGroupCount',
                method: 'post',
                data: params
        })
}


//获取监管单关联的活畜列表---抵押活畜信息栏目列表
export function superviseLivestockList(params) {
        params.appId = rabBaseInfo.appId
        return request({
                url: basicPath4 + 'supervise/livestockList',
                method: 'post',
                data: params
        })
}

//根据监管单获取无耳标获取
export function livestockNoTagAndAllList(params) {
    params.appId = rabBaseInfo.appId
    return request({
            url: basicPath4 + 'supervise/livestockNoTagAndAllList',
            method: 'post',
            data: params
    })
}


//修改监管单中，紧急联系人，所有监管单中附件，监管员
export function superviseEditSome(params) {
        params.appId = rabBaseInfo.appId
        return request({
                url: basicPath4 + 'supervise/edit',
                method: 'post',
                data: params
        })
}

//修改附件
export function editSuperviseFile(params) {
    params.appId = rabBaseInfo.appId
    return request({
            url: basicPath4 + 'supervise/editSuperviseFile',
            method: 'post',
            data: params
    })
}

//查询巡检记录列表
export function inspectionLog(params) {
        params.appId = rabBaseInfo.appId
        return request({
                url: basicPath4 + 'patrol/page',
                method: 'post',
                data: params
        })
}

//来源页面：巡检统计 时 查询巡检记录列表
export function inspectionStatisticsLog(params) {
        params.appId = rabBaseInfo.appId
        return request({
                url: basicPath4 + 'patrol/pageFromXj',
                method: 'post',
                data: params
        })
}

//查询巡检记录详情
export function inspectionInfo(params) {
        params.appId = rabBaseInfo.appId
        return request({
                url: basicPath4 + 'patrol/info',
                method: 'post',
                data: params
        })
}

//查询删除
export function inspectionDelete(params) {
        params.appId = rabBaseInfo.appId
        return request({
                url: basicPath4 + 'supervise/delete',
                method: 'post',
                data: params
        })
}



//查询企业信息详情
export function getEnterpriseInfo(params) {
        params.appId = rabBaseInfo.appId
        return request({
                url: basicPath4 + 'enterprise/queryById',
                method: 'post',
                data: params
        })
}


// 查询企业认证列表
export function listEnterprise(params) {
  return request({
    url: basicPath4 + 'enterprise/list',
    method: 'post',
    data: params
  })
}

// 根据仓库ids查询监控视频列表
// export function warehouseOfflineVideo(params) {
//         return request({
//             url: basicPath4 + 'offlineVideo/listByIds',
//             method: 'post',
//             data: params
//         })
//     }

// 根据牧场ids查询监控视频列表
// export function listByPastureIds(params) {
//         return request({
//             url: basicPath4 + 'offlineVideo/listByPastureIds',
//             method: 'post',
//             data: params
//         })
//     }


// 关闭监管单
export function superviseClose(params) {
    return request({
        url: basicPath4 + 'supervise/close',
        method: 'post',
        data: params
    })
}
//查询服务费是否结清
export function superviseService(params) {
    return request({
        url: basicPath4 + 'serviceFee/selectFeeBySuperviseId',
        method: 'post',
        data: params
    })
}
//修改监管单巡检次数
export function editInspectNum(params) {
  params.appId = rabBaseInfo.appId
  return request({
      url: basicPath4 + 'supervise/editInspectNum',
      method: 'post',
      data: params
  })
}
//盘点列表
export function patrolList(params) {
    params.appId = rabBaseInfo.appId
    return request({
        url: basicPath4 + 'patrolNew/list',
        method: 'post',
        data: params
    })
}
//盘点详情
export function queryById(params) {
    params.appId = rabBaseInfo.appId
    return request({
        url: basicPath4 + 'patrolNew/queryByIdAndTime',
        method: 'post',
        data: params
    })
}

//获取分佣规则列表
export function commissionRule(params) {
  // params.appId = rabBaseInfo.appId
  return request({
      url:  basicPath4+'commission/ope/rule/page',
      method: 'post',
      data: params
  })
}

//监管单关联分佣规则
export function relationRule(params) {
  params.appId = rabBaseInfo.appId
  return request({
      url: basicPath4 + 'supervise/bindAccRule',
      method: 'post',
      data: params
  })
}


//修改服务费
export function editFee(params) {
  params.appId = rabBaseInfo.appId
  return request({
      url: basicPath4 + 'superviseamountreceiverecord/edit',
      method: 'post',
      data: params
  })
}


//根据监管单查询服务费列表
export function bySupervisionFeeList(params) {
  params.appId = rabBaseInfo.appId
  return request({
      url: basicPath4 + 'superviseamountreceiverecord/page',
      method: 'post',
      data: params
  })
}
//根据监管单查询服务费列表---导出
export function exportRecord(params) {
  params.appId = rabBaseInfo.appId
  return request({
      url: basicPath4 + 'superviseamountreceiverecord/exportRecord',
      method: 'post',
      data: params,
      responseType: 'arraybuffer'
  })
}

//根据监管单查询服务费列表---导出
export function selectBySuperviseId(params) {
  params.appId = rabBaseInfo.appId
  return request({
      url: basicPath4 + 'supervise/selectBySuperviseId',
      method: 'post',
      data: params
  })
}


//维护预警信息
export function saveEarly(params) {
    return request({
        url: basicPath4 + 'supervise/saveEarly',
        method: 'post',
        data: params
    })
}
//监管单授权
export function editSuperviseRange(params) {
    return request({
        url: basicPath4 + 'supervise/editSuperviseRangeListById',
        method: 'post',
        data: params
    })
}

//监管单导出·
export function exportSuperviseList(params) {
    return request({
        url: basicPath4 + 'supervise/exportSuperviseList ',
        method: 'post',
        data: params,
        responseType:'arraybuffer'
    })
}
export function exportAllSuperviseList(params) {
  return request({
    url: basicPath4 + 'supervise/exportAllSuperviseList ',
    method: 'post',
    data: params,
    responseType:'arraybuffer'
  })
}

//巡检地址和巡检时间修改
export function editPatrolTimeAndPos(params) {
    return request({
        url: basicPath4 + 'patrol/editPatrolTimeAndPos',
        method: 'post',
        data: params,
    })
}
//查询活畜数量
export function getLivestockListNum(params) {
    return request({
        url: basicPath4 + 'pastureLivestock/selectPledgeLivestockList',
        method: 'post',
        data: params,
    })
}
//完善耳标/supervise/perfectEarTag
export function pledgeLivestockList(params) {
    return request({
        url: basicPath4 + '/supervise/perfectEarTag',
        method: 'post',
        data: params,
    })
}
//查询牧场活畜
export function checkActivateLivestock(params) {
    return request({
        url: basicPath4 + '/pastureLivestock/checkActivateLivestock',
        method: 'post',
        data: params,
    })
}
//查询上次修改服务费
export function queryEndTime(params) {
    return request({
        url: basicPath4 + 'superviseamountreceiverecord/queryEndTime',
        method: 'post',
        data: params,
    })
}
//延期数据提交
export function delayEndTime(params) {
    return request({
        url: basicPath4 + 'superviseamountreceiverecord/delayEndTime',
        method: 'post',
        data: params,
    })
}
//延期记录 /
export function queryDelayRecord(params) {
    return request({
        url: basicPath4 + 'superviseamountreceiverecord/queryDelayRecord',
        method: 'post',
        data: params,
    })
}
//资金流向导出 /1
export function exportZjlxSuperviseList(params) {
    return request({
        url: basicPath4 + 'supervise/exportZjlxSuperviseList',
        method: 'post',
        data: params,
        responseType:'arraybuffer'
    })
}
//根据经纬度解析地址  location=116.310003,39.991957
export function buylnglatGetAddress(location) {
        return new Promise((reslove, reject) => {
                const url = `https://restapi.amap.com/v3/geocode/regeo?output=JSON&location=${location}&key=c53f740a6711a055e456212c86180881&radius=1000&extensions=all`
                axios.get(url,
                        {},
                        {
                                headers: { 'Content-Type': 'application/json' }
                        }
                ).then((res) => {
                        console.log('根据经纬度解析地址res：', res.data);
                        reslove(res)
                }).catch(err => {
                        console.log('根据经纬度解析异常：', err);
                        reject(err)
                })
        })

}

// 监管单支持修改服务费结清 设备拆除功能
export function superviseEdit(params) {
  return request({
    url: basicPath4+'supervise/editSuperviseFile',
    method: 'post',
    data: params,
  })
}

//银行数据查询
export function treeListByParentId(params) {
    return request({
      url: basicPath4+'user/treeListByParentId',
      method: 'post',
      data: params,
    })
  }

// 查询监管主体
export function supervisingSubjectList(params) {
  return request({
    url: basicPath4+'user/supervisingSubjectList',
    method: 'post',
    data: params
  })
}



// 还款列表
export function superviseRepaymentList(params) {
  params.appId = rabBaseInfo.appId;
  return request({
    url: basicPath4 + "superviseRepayment/listBySupervise",
    method: "post",
    data: params,
  });
}

// 还款导出
export function exportRecordSuperviseRepayment(params) {
  params.appId = rabBaseInfo.appId;
  return request({
    url: basicPath4 + "superviseRepayment/exportListBySupervise",
    method: "post",
    data: params,
    responseType: "arraybuffer",
  });
}