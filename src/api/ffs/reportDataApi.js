import request from '@/utils/request'
import { basicPath4 } from '@/api/base'
import { rabBaseInfo } from '@/api/ffs/xmbCommon'

// 数据源列表
export function getDataSourceList(data) {
        data.appId = rabBaseInfo.appId
        return request({
                url: basicPath4 + 'dataSource/pageList',
                method: 'post',
                data: data
        })
}


// 新生成一条数据源信息，同时导入excel表格内容
// export function insertDataSource(data) {
//         return process.env.VUE_APP_BASE_API + `${basicPath4}dataSource/insert`
// }

export function insertDataSource(data) {
        data.appId = rabBaseInfo.appId
        return request({
                url: basicPath4 + 'dataSource/insert',
                method: 'post',
                data: data
        })
}

// 删除一条数据源信息，同时删除对应的excel表格内容
export function deleteDataSource(data) {
        data.appId = rabBaseInfo.appId
        return request({
                url: basicPath4 + 'dataSource/delete',
                method: 'post',
                data: data
        })
}


// 报表记录列表
export function getRecordList(data) {
        data.appId = rabBaseInfo.appId
        return request({
                url: basicPath4 + 'bankReportRecord/pageList',
                method: 'post',
                data: data
        })
}



// 生成一条报表
export function insertBankReport(data) {
        data.appId = rabBaseInfo.appId
        return request({
                url: basicPath4 + 'excel/bankReport/calculationBankReport',
                method: 'post',
                data: data
        })
}


// 返回excel报表模板名称列表
export function getTemplateList(data) {
        data.appId = rabBaseInfo.appId
        return request({
                url: basicPath4 + 'dataSource/getTemplateList',
                method: 'post',
                data: data
        })
}


// 生成S63的等等等的报表
export function calculationBankReportV2(data) {
        data.appId = rabBaseInfo.appId
        return request({
                url: basicPath4 + 'excel/bankReport/calculationBankReportV2',
                method: 'post',
                data: data
        })
}
