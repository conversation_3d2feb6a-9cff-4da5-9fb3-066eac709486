import request from '@/utils/request'
import {basicPath4, basicPath2, basicPath1} from '@/api/base'
import { rabBaseInfo } from '@/api/ffs/xmbCommon'
import { xmbBaseInfo } from '@/api/xmb/xmbCommon'
import axios from 'axios'
//监管单列表--gx
export function inspectionList(params) {

        return request({
                url: basicPath4 + 'supervise/listXjByPlatformUser',
                method: 'post',
                data: params
        })
}