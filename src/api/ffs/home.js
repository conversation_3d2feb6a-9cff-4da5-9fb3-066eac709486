import request from '@/utils/request'
import { basicPath4 } from '@/api/base'
import { rabBaseInfo } from '@/api/ffs/xmbCommon'
// 数据源列表
export function homeList(data) {
    data.appId = rabBaseInfo.appId
    return request({
        url: basicPath4 + 'superviseStatistics/pcHomePageByArea',
        method: 'post',
        data: data
    })
}
//柱状图
export function trendList(data) {
    data.appId = rabBaseInfo.appId
    return request({
        url: basicPath4 + 'superviseStatistics/superviseGrowthTrend',
        method: 'post',
        data: data
    })
}
//即将到期监管
export function expiringSoonList(data) {
    data.appId = rabBaseInfo.appId
    return request({
        url: basicPath4 + 'superviseStatistics/expiringSoonSupervise',
        method: 'post',
        data: data
    })
}
//区域数据查询
export function getAreaTreeList(data) {
    return request({
        url: basicPath4 + 'supervise/selectPlatformIdAreaTreeList',
        method: 'post',
        data: data
    })
}