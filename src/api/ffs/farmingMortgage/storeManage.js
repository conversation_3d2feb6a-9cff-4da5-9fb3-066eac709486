import request from "@/utils/request";
import { basicPath4, basicPath2 } from "@/api/base";
import { rabBaseInfo } from "@/api/ffs/xmbCommon";

//仓库新增
export function addStoreHous(params) {
  return request({
    url: basicPath4 + "farmingStoreHouse/add",
    method: "post",
    data: params,
  });
}
//仓库列表
export function storeHousList(params) {
  return request({
    url: basicPath4 + "farmingStoreHouse/list",
    method: "post",
    data: params,
  });
}
//仓库详情
export function selectById(params) {
  return request({
    url: basicPath4 + "farmingStoreHouse/selectById",
    method: "post",
    data: params,
  });
}

//仓库删除
export function deleteById(params) {
  return request({
    url: basicPath4 + "farmingStoreHouse/deleteById",
    method: "post",
    data: params,
  });
}
//仓库编辑
export function editStoreHouse(params) {
    return request({
        url: basicPath4 + "farmingStoreHouse/update",
        method: "post",
        data: params,
    });
}


//货区列表
export function positionList(params) {
  return request({
    url: basicPath4 + "farmingStoreHousePosition/list",
    method: "post",
    data: params,
  });
}
//货区新增
export function addPosition(params) {
    return request({
        url: basicPath4 + "farmingStoreHousePosition/add",
        method: "post",
        data: params,
    });
}
//货区编辑
export function editPosition(params) {
    return request({
        url: basicPath4 + "farmingStoreHousePosition/edit",
        method: "post",
        data: params,
    });
}
//货区删除
export function delPosition(params) {
    return request({
        url: basicPath4 + "farmingStoreHousePosition/deleteById",
        method: "post",
        data: params,
    });
}
//校验仓库编号的唯一性
export function checkStoreNo(params) {
    return request({
        url: basicPath4 + "farmingStoreHouse/checkStorehouseNo",
        method: "post",
        data: params,
    });
}
//视频列表
export function videoList(params) {
    return request({
        url: basicPath4 + "farmingStoreHouseVideo/list",
        method: "post",
        data: params,
    });
}
//视频添加
export function addVideo(params) {
    return request({
        url: basicPath4 + "farmingStoreHouseVideo/add",
        method: "post",
        data: params,
    });
}
//视频删除
export function delVideo(params) {
    return request({
        url: basicPath4 + "farmingStoreHouseVideo/deleteById",
        method: "post",
        data: params,
    });
}

//视频编辑
export function editVideo(params) {
    return request({
        url: basicPath4 + "farmingStoreHouseVideo/update",
        method: "post",
        data: params,
    });
}
//传感器分页
export function equipmentList(params) {
    return request({
        url: basicPath4 + "superviseEquipment/select/page",
        method: "post",
        data: params,
    });
}
//传感器编辑
export function equipmentEdit(params) {
    return request({
        url: basicPath4 + "superviseEquipment/edit",
        method: "post",
        data: params,
    });
}
//传感器新增
export function equipmentAdd(params) {
    return request({
        url: basicPath4 + "superviseEquipment/insert",
        method: "post",
        data: params,
    });
}
//特殊萤石云摄像头新增
export function cameraBusinessAdd(params) {
    return request({
        url: basicPath4 + "cameraBusiness/addCameraBusiness",
        method: "post",
        data: params,
    });
}

//特殊萤石云摄像编辑
export function cameraBusinessEdit(params) {
    return request({
        url: basicPath4 + "cameraBusiness/editCameraBusiness",
        method: "post",
        data: params,
    });
}
//特殊萤石删除
export function cameraBusinessDel(params) {
    return request({
        url: basicPath4 + "cameraBusiness/deleteCameraBusiness",
        method: "post",
        data: params,
    });
}
