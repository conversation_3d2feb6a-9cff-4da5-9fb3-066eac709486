import request from '@/utils/request'
import { basicPath4, basicPath2 } from '@/api/base'
import { rabBaseInfo } from '@/api/ffs/xmbCommon'

//商品新增
export function addCommodity(params) {
    return request({
        url: basicPath4 + 'farmingCommodity/insert',
        method: 'post',
        data: params
    })
}
//商品修改
export function editCommodity(params) {
    return request({
        url: basicPath4 + 'farmingCommodity/update',
        method: 'post',
        data: params
    })
}
//商品信息列表
export function commodityList(params) {
    return request({
        url: basicPath4 + 'farmingCommodity/pageList',
        method: 'post',
        data: params
    })
}
//商品详情
export function commodityInfo(params) {
    return request({
        url: basicPath4 + 'farmingCommodity/info',
        method: 'post',
        data: params
    })
}
//商品删除
export function delCommodity(params) {
    return request({
        url: basicPath4 + 'farmingCommodity/delete',
        method: 'post',
        data: params
    })
}
//校验商品编号
export function checkCommodity(params) {
    return request({
        url: basicPath4 + 'farmingCommodity/checkCommodityNoOnly',
        method: 'post',
        data: params
    })
}