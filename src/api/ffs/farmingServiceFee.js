import request from '@/utils/request'
import { basicPath4 } from '@/api/base'
import { rabBaseInfo } from '@/api/ffs/xmbCommon'
// 数据源列表
export function feeList(data) {
    // data.appId = rabBaseInfo.appId
    return request({
            url: basicPath4 + 'farmingServiceFee/feeList',
            method: 'post',
            data: data
    })
}
//服务费详情
export function recordList(data) {
    data.appId = rabBaseInfo.appId
    return request({
            url: basicPath4 + 'farmingServiceFee/recordList',
            method: 'post',
            data: data
    })
}
//服务费详情
export function serviceFeeAdd(data) {
    data.appId = rabBaseInfo.appId
    return request({
            url: basicPath4 + 'farmingServiceFee/add',
            method: 'post',
            data: data
    })
}
//顶部统计接口
export function feeStatistics(data) {
    data.appId = rabBaseInfo.appId
    return request({
            url: basicPath4 + 'farmingServiceFee/feeStatistics',
            method: 'post',
            data: data
    })
}
//服务费关闭
export function feeClose(data) {
    data.appId = rabBaseInfo.appId
    return request({
        url: basicPath4 + 'farmingServiceFee/edit',
        method: 'post',
        data: data
    })
}
//服务费导出
export function exportFeeList(data) {
    data.appId = rabBaseInfo.appId
    return request({
        url: basicPath4 + 'farmingServiceFee/exportFeeList',
        method: 'post',
        data: data,
        responseType: 'arraybuffer'
    })
}

//发生金额明细
export function occurredAmount(data) {
    data.appId = rabBaseInfo.appId
    return request({
        url: basicPath4 + 'occurredAmount/list',
        method: 'post',
        data: data,
    })
}
