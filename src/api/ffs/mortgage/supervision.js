import request from "@/utils/request";
import { basicPath4, basicPath2 } from "@/api/base";
import { rabBaseInfo } from "@/api/ffs/xmbCommon";

//根据仓单监管单获取绑定的货区
export function storehouseListBuySuperviseId(params) {
  return request({
    url: basicPath4 + "supervise/storehouseList",
    method: "post",
    data: params,
  });
}

//根据仓单监管单获取绑定的商品
export function commodityListBuySuperviseId(params) {
  return request({
    url: basicPath4 + "supervise/commodityList",
    method: "post",
    data: params,
  });
}

//根据仓单监管单获取监管日志
export function superviseLogPage(params) {
  return request({
    url: basicPath4 + "ffsSuperviseLog/pageList",
    method: "post",
    data: params,
  });
}

//日志详情
export function superviseLogInfo(params) {
  return request({
    url: basicPath4 + "ffsSuperviseLog/info",
    method: "post",
    data: params,
  });
}

//日志-修改
export function superviseLogEdit(params) {
  return request({
    url: basicPath4 + "ffsSuperviseLog/edit",
    method: "post",
    data: params,
  });
}
//仓单监管单详情商品里面的商品出入库明细
export function superviseDetailPage(params) {
  return request({
    url: basicPath4 + "/storehouseMotionRecord/detailPage",
    method: "post",
    data: params,
  });
}

//仓单监管单详情里面的出入库明细
export function outentRecordPage(params) {
  return request({
    url: basicPath4 + "/storehouseMotionRecord/page",
    method: "post",
    data: params,
  });
}
//监管单还款记录
export function paymenHistoryList(params) {
  return request({
    url: basicPath4 + "/superviseRepayment/list",
    method: "post",
    data: params,
  });
}

//根据监管单 库存查询
export function countByParam(params) {
  return request({
    url: basicPath4 + "/storehouseMotionRecord/countByParam",
    method: "post",
    data: params,
  });
}

//根据监管单 查询仓单巡检列表
export function superviseinspectPage(params) {
  return request({
    url: basicPath4 + "/superviseinspect/page",
    method: "post",
    data: params,
  });
}

//仓单巡检详情
export function superviseinspectInfo(params) {
  return request({
    url: basicPath4 + "/superviseinspect/info",
    method: "post",
    data: params,
  });
}

//用信记录新增接口
export function addSuperviseamount(params) {
  return request({
    url: basicPath4 + "/superviseamountreceiverecord/add",
    method: "post",
    data: params,
  });
}

//追加用信记录列表
export function addSuperviseamountList(params) {
  return request({
      url: basicPath4 + "superviseamountreceiverecord/page",
    method: "post",
    data: params,
  });
}


//日常巡检-巡检详情-导出
export function mortgageInspectionExport(params) {
  return request({
    url: basicPath4 + "superviseinspect/exportSuperviseInspect",
    method: "post",
    responseType: 'blob',
    data: params,
  });
}

//巡检时间修改
export function inspectTimeEdit(params) {
    return request({
      url: basicPath4 + "superviseinspect/edit",
      method: "post",
      data: params,
    });
  }

  // 删除还款记录
export function deleteByRepayment(params) {
  return request({
    url: basicPath4 + "/superviseRepayment/deleteById",
    method: "post",
    data: params,
  });
}
