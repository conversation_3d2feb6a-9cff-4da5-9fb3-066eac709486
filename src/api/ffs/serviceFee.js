import request from '@/utils/request'
import { basicPath4 } from '@/api/base'
import { rabBaseInfo } from '@/api/ffs/xmbCommon'
// 数据源列表
export function feeList(data) {
    // data.appId = rabBaseInfo.appId
    return request({
            url: basicPath4 + 'serviceFee/feeList',
            method: 'post',
            data: data
    })
}
//服务费详情
export function recordList(data) {
    data.appId = rabBaseInfo.appId
    return request({
            url: basicPath4 + 'serviceFee/recordList',
            method: 'post',
            data: data
    })
}
//服务费详情
export function serviceFeeAdd(data) {
    data.appId = rabBaseInfo.appId
    return request({
            url: basicPath4 + 'serviceFee/add',
            method: 'post',
            data: data
    })
}
//顶部统计接口
export function feeStatistics(data) {
    data.appId = rabBaseInfo.appId
    return request({
            url: basicPath4 + 'serviceFee/feeStatistics',
            method: 'post',
            data: data
    })
}
//服务费关闭
export function feeClose(data) {
    data.appId = rabBaseInfo.appId
    return request({
        url: basicPath4 + 'serviceFee/edit',
        method: 'post',
        data: data
    })
}
//服务费导出
export function exportFeeList(data) {
    data.appId = rabBaseInfo.appId
    return request({
        url: basicPath4 + 'serviceFee/exportFeeList',
        method: 'post',
        data: data,
        responseType: 'arraybuffer'
    })
}

//发生金额明细
export function occurredAmount(data) {
    data.appId = rabBaseInfo.appId
    return request({
        url: basicPath4 + 'occurredAmount/list',
        method: 'post',
        data: data,
    })
}


// 数据源列表
export function superviseServiceFeeList(data) {
  // data.appId = rabBaseInfo.appId
  return request({
    url: basicPath4 + 'superviseServiceFee/list',
    method: 'post',
    data: data
  })
}
