import request from '@/utils/request'
import { basicPath1, basicPath2, basicPath4 } from '@/api/base'

// 用户列表
export function effectiveAdminList(data) {
    return request({
        url: basicPath1 + 'system/user/effectiveAdminList',
        method: 'post',
        data: data
    })
}
// 通过userid查询用户所有活畜
export function livestockListByUserId(data) {
    return request({
        url: basicPath2 + 'pastureLivestock/livestockListByUserId',
        method: 'post',
        data: data
    })
}
// 新增保单
export function ffsInsuranceAdd(data) {
    return request({
        url: basicPath4 + 'ffsInsurance/add',
        method: 'post',
        data: data
    })
}

// 保单列表 - 分页
export function ffsInsurancePage(data) {
    return request({
        url: basicPath4 + 'ffsInsurance/page',
        method: 'post',
        data: data
    })
}
// 保单列表 - 不分页
export function ffsInsuranceList(data) {
    return request({
        url: basicPath4 + 'ffsInsurance/list',
        method: 'post',
        data: data
    })
}
// 保单详情
export function ffsInsuranceInfo(data) {
    return request({
        url: basicPath4 + 'ffsInsurance/info',
        method: 'post',
        data: data
    })
}
// 保单修改
export function ffsInsuranceEdit(data) {
    return request({
        url: basicPath4 + 'ffsInsurance/update',
        method: 'post',
        data: data
    })
}
// 保单删除
export function ffsInsuranceDelete(data) {
    return request({
        url: basicPath4 + 'ffsInsurance/delete',
        method: 'post',
        data: data
    })
}