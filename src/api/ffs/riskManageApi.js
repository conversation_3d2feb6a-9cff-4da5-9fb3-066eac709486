import request from '@/utils/request'
import { basicPath4 } from '@/api/base'
import { rabBaseInfo } from '@/api/ffs/xmbCommon'

// 风控问题列表PC
export function getQuestionList(data) {
        data.appId = rabBaseInfo.appId
        return request({
                url: basicPath4 + 'risk/question/page',
                method: 'post',
                data: data
        })
}


// 新增风控问题
export function addQuestion(data) {
  data.appId = rabBaseInfo.appId
  return request({
          url: basicPath4 + 'risk/question/add',
          method: 'post',
          data: data
  })
}


// 编辑风控问题
export function updateQuestion(data) {
  data.appId = rabBaseInfo.appId
  return request({
          url: basicPath4 + 'risk/question/update',
          method: 'post',
          data: data
  })
}

// 删除风控问题
export function delQuestion(data) {
  data.appId = rabBaseInfo.appId
  return request({
          url: basicPath4 + 'risk/question/delete',
          method: 'post',
          data: data
  })
}






// 批量新增用户风控评级等级
export function addLevelBatch(data) {
  data.appId = rabBaseInfo.appId
  return request({
          url: basicPath4 + 'riskRatingLevel/insertBatch',
          method: 'post',
          data: data
  })
}

// 查询用户风控评级等级列表
export function getLevel(data) {
  data.appId = rabBaseInfo.appId
  return request({
          url: basicPath4 + 'riskRatingLevel/page',
          method: 'post',
          data: data
  })
}