import request from '@/utils/request'
import { basicPath4 } from '@/api/base'
import { rabBaseInfo } from '@/api/ffs/xmbCommon'
// 萤石云列表
export function getEzvizList(data) {
    data.appId = rabBaseInfo.appId
    return request({
        url: basicPath4 + 'ezviz/list',
        method: 'post',
        data: data
    })
}

// 萤石云详情
export function byIdInfo(data) {
    data.appId = rabBaseInfo.appId
    return request({
        url: basicPath4 + 'ezviz/selectById',
        method: 'post',
        data: data
    })
}

// 萤石云修改
export function ezvizUpdate(data) {
    data.appId = rabBaseInfo.appId
    return request({
        url: basicPath4 + 'ezviz/update',
        method: 'post',
        data: data
    })
}
// 萤石云删除
export function deleteById(data) {
    data.appId = rabBaseInfo.appId
    return request({
        url: basicPath4 + 'ezviz/deleteById',
        method: 'post',
        data: data
    })
}
//添加
export function addEzviz(data) {
    data.appId = rabBaseInfo.appId
    return request({
        url: basicPath4 + 'ezviz/add',
        method: 'post',
        data: data
    })
}