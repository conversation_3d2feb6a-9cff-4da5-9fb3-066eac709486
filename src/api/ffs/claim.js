import request from '@/utils/request'
import { basicPath4 } from '@/api/base'

// 理赔单列表 - 分页 
export function ffsInsuranceClaimsPage(data) {
    return request({
        url: basicPath4 + 'ffsInsuranceClaims/page',
        method: 'post',
        data: data
    })
}
// 理赔单新增
export function ffsInsuranceClaimsAdd(data) {
    return request({
        url: basicPath4 + 'ffsInsuranceClaims/add',
        method: 'post',
        data: data
    })
}
// 理赔单新增
export function ffsInsuranceClaimsInfo(data) {
    return request({
        url: basicPath4 + 'ffsInsuranceClaims/info',
        method: 'post',
        data: data
    })
}
// 理赔单修改
export function ffsInsuranceClaimsUpdate(data) {
    return request({
        url: basicPath4 + 'ffsInsuranceClaims/update',
        method: 'post',
        data: data
    })
}
// 理赔单删除
export function ffsInsuranceClaimsDelete(data) {
    return request({
        url: basicPath4 + 'ffsInsuranceClaims/delete',
        method: 'post',
        data: data
    })
}
// 理赔单审核
export function ffsInsuranceClaimsAudit(data) {
    return request({
        url: basicPath4 + 'ffsInsuranceClaims/audit',
        method: 'post',
        data: data
    })
}
