import request from '@/utils/request'
import {basicPath2} from '@/api/base.js'

// 日志列表
export function logList(query) {
    return request({
        url: `${basicPath2}productLog/list`,
        method: 'post',
        data: query
    })
}

// 日志详情
export function logSelect(query) {
    return request({
        url: `${basicPath2}productLog/select`,
        method: 'post',
        data: query
    })
}
// 日志审核
export function logApprove(query) {
    return request({
        url: `${basicPath2}productLog/approve`,
        method: 'post',
        data: query
    })
}