import request from "@/utils/request"
import { basicPath3 } from "@/api/base.js"
import { baseInfo } from "@/api/nlb/nlbCommon"

// 利润分配规则
// 列表
export const changeSettingList = (data) => {
  data.appId = baseInfo.appId
  return request({
    url: `${basicPath3}storeChangeSetting/selectStoreChangeSettingList`,
    method: "post",
    data: data
  })
}

// 新增
export const changeSettingAdd = (data) => {
  data.appId = baseInfo.appId
  return request({
    url: `${basicPath3}storeChangeSetting/insertStoreChangeSetting`,
    method: "post",
    data: data
  })
}
export const getChangeSettingInfo = (data) => {
  data.appId = baseInfo.appId
  return request({
    url: `${basicPath3}storeChangeSetting/selectStoreChangeSettingInfo`,
    method: "post",
    data: data
  })
}

export const editStoreChangeSetting =(data) => {
  data.appId = baseInfo.appId
  return request({
    url: `${basicPath3}storeChangeSetting/updateStoreChangeSetting`,
    method: "post",
    data: data
  })
}


// 用户列表----根据用户手机号查询收款方，手机号
export const selectUserPageList = (data) => {
  data.appId = baseInfo.appId
  return request({
    url: `${basicPath3}stroeUserRelation/selectUserPageList`,
    method: "post",
    data: data
  })
}