import request from '@/utils/request'
import { basicPath3 } from '@/api/base.js'
import { baseInfo } from '@/api/nlb/nlbCommon'

/**
 * 
 * @param {店铺管理} 
 */

// 店铺列表
export function getStoreList(data) {
    data.appId = baseInfo.appId
    return request({
        // url: `${basicPath3}goodsStore/selectStoreList`,
        url: `${basicPath3}store/page`,
        method: 'post',
        data: data
    })
}

// 店铺修改
export function editStoreInfo(data) {
    data.appId = baseInfo.appId
    return request({
        url: `${basicPath3}goodsStore/updateStore`,
        method: 'post',
        data: data
    })
}

// 店铺详情
export function getDetailInfo(data) {
    data.appId = baseInfo.appId
    return request({
        url: `${basicPath3}goodsStore/selectStoreInfo`,  // 
        method: 'post',
        data: data
    })
}

// 获取点区域列表
export function getRegionAllList(data){
    return request({
        url: `${basicPath3}region/selectRegionListAll`,
        method: 'post',
        data: data
    })
}

// 获取市场列表
export function getMarketAllList(data) {
    return request({
        url: `${basicPath3}market/selectMarketListAll`,
        method: 'post',
        data: data
    })
}

// 获取店铺列表
export function getStoreAll(data) {
    return request({
        url: `${basicPath3}/goodsStore/selectStoreListAll`,
        method: 'post',
        data: data
    })
}