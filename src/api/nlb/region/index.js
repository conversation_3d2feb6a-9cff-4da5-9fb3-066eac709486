import request from '@/utils/request'
import { basicPath3 } from '@/api/base.js'
import { baseInfo } from '@/api/nlb/nlbCommon'

// 查询区域列表
export function getRegionList(data) {
    data.appId = baseInfo.appId
    return request({
        url: `${basicPath3}region/selectRegionList`,
        method: 'post',
        data: data
    })
}

// 运营区域查询
export function selectManageByIRegionId(data) {
    data.appId = baseInfo.appId
    console.log(data)
    return request({
        url: `${basicPath3}region/selectManageByIRegionId`,
        method: 'post',
        data: data
    })
}

// 运营区域修改
export function updateRegion(data) {
    data.appId = baseInfo.appId
    return request({
        url: `${basicPath3}region/updateRegion`,
        method: 'post',
        data: data
    })
}

// 运营区域删除
export function deleteRegionByInfoId(data) {
    data.appId = baseInfo.appId
    return request({
        url: `${basicPath3}region/deleteRegionByInfoId`,
        method: 'post',
        data: data
    })
}

// 运营区域新增
export function insertBaseRegion(data) {
    data.appId = baseInfo.appId
    return request({
        url: `${basicPath3}region/insertBaseRegion`,
        method: 'post',
        data: data
    })
}