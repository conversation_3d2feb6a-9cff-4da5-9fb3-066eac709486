import request from '@/utils/request'
import { basicPath3 } from '@/api/base.js'
import { baseInfo } from '@/api/nlb/nlbCommon'

/**
 * 
 * @noticeType      通告类型
 * @noticeTitle     通告标题
 * @noticeContent   通告内容
 * @status          状态0 正常 1 禁用
 * @userId          用户ID
 */

// 添加公告
export const insertNotice = (data) =>{
    data.appId = baseInfo.appId;
    return request({
        url: `${basicPath3}basenotice/insertBaseNotice`,
        method: 'post',
        data: data
    })
}

// 获取公告列表
export const getNotice = (data) => {
    data.appId = baseInfo.appId;
    return request({
        url: `${basicPath3}basenotice/selectBaseNoticeList`,
        method: 'post',
        data: data
    })
}

// 获取公告详情
export const getNoticeId = (data) => {
    data.appId = baseInfo.appId;
    return request({
        url: `${basicPath3}basenotice/selectBaseNoticeInfo`,
        method: 'post',
        data: data
    })
}

// 公告修改
export const editNotice = (data) => {
    data.appId = baseInfo.appId;
    return request({
        url: `${basicPath3}basenotice/updateBaseNotice`,
        method: 'post',
        data: data
    })
}

// 公告删除
export const deleteNotice = (data) => {
    data.appId = baseInfo.appId;
    return request({
        url: `${basicPath3}basenotice/deleteBaseNoticeById`,
        method: 'post',
        data: data
    })
}

