import request from "@/utils/request"
import { basicPath3 } from "@/api/base.js"
import { baseInfo } from "@/api/nlb/nlbCommon"

// 会员列表
export const selectUserManageer = (data) => {
    data.appId = baseInfo.appId;
    return request({
        url: `${basicPath3}stroeUserRelation/selectUserManageer`,
        method: "post",
        data: data
    })
}

////////////////==============》
/* 会员管理个人信息 */

// 会员销售订单
export const memberOrderBywaiter = (data) => {
    data.appId = baseInfo.appId;
    return request({
        url: `${basicPath3}order/bywaiter`,
        method: "post",
        data: data
    })
}

// 店铺信息
export const storeDetailUserInfo = (data) => {
    data.appId = baseInfo.appId;
    return request({
        url: `${basicPath3}goodsStore/selectStoreListAll`,
        method: "post",
        data: data
    })
}

// 收货地址
export const personAddress = (data) => {
    data.appId = baseInfo.appId
    return request({
      url: `${basicPath3}baseuserAddress/selectBaseUserAddressList`,
      method: "post",
      data: data
    })
}

// 会员采购订单
export const orderPerson = (data) => {
    data.appId = baseInfo.appId
    return request({
      url: `${basicPath3}order/page`,
      method: "post",
      data: data
    })
}

// 销售订单
/**
 * 
 * @param {*} data CreateBy: id
 * @returns 
 */
export const orderRelation = (data) => {
    data.appId = baseInfo.appId
    return request({
      url: `${basicPath3}order/page`,
      method: "post",
      data: data
    })
}


////////////////==============》
/* 会员关联店铺信息 */
export const selectStroeUserRelationByUserIds = (data) => {
    data.appId = baseInfo.appId
    return request({
      url: `${basicPath3}stroeUserRelation/selectStroeUserRelationByUserIds`,
      method: "post",
      data: data
    })
}

/* 会员详情 */
export const selectUserByUserId = (data) => {
    data.appId = baseInfo.appId;
    return request({
        url: `${basicPath3}stroeUserRelation/selectUserByUserId`,
        method: "post",
        data: data
    })
}

// 会员总数
export const selectUserCount = (data) => {
    return request({
        url: `${basicPath3}stroeUserRelation/selectUserCount`,
        method: "post",
        data: data
    })
}