/**
 *
 * @农链帮订单管理
 */
import request from "@/utils/request"
import { basicPath3 } from "@/api/base.js"
import { baseInfo } from "@/api/nlb/nlbCommon"

/**
 *
 * @订单列表
 */
export const orderList = (data) => {
  data.appId = baseInfo.appId
  return request({
    url: `${basicPath3}order/page`,
    method: "post",
    data: data
  })
}

/**
 *
 * @订单详情
 */
export const orderDetail = (data) => {
  data.appId = baseInfo.appId
  return request({
    url: `${basicPath3}order/info`,
    method: "post",
    data: data
  })
}

/**
 *
 * @取消订单
 */
export const orderCancel = (data) => {
  data.appId = baseInfo.appId
  return request({
    url: `${basicPath3}order/cancel`,
    method: "post",
    data: data
  })
}