// 市场管理
import request from '@/utils/request'
import {basicPath3} from '@/api/base.js'
import { baseInfo } from '@/api/nlb/nlbCommon'

// 获取列表
export function getMarketList(data) {
    data.appId = baseInfo.appId
    return request({
        url: `${basicPath3}market/selectMarketList`,
        method: 'post',
        data: data
    })
}

// 添加市场
export function insertMarket(data) {
    data.appId = baseInfo.appId
    return request({
        url: `${basicPath3}market/insertMarket`,
        method: 'post',
        data: data
    })
}

// 修改市场
export function updateMarket(data) {
    data.appId = baseInfo.appId
    return request({
        url: `${basicPath3}market/updateMarket`,
        method: 'post',
        data: data
    })
}

// 市场查询单个
export function selectMarketInfo(data) {
    data.appId = baseInfo.appId
    return request({
        url: `${basicPath3}market/selectMarketInfo`,
        method: 'post',
        data: data
    })
}