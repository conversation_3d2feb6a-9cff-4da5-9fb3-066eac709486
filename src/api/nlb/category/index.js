import request from '@/utils/request'
import {basicPath3} from '@/api/base.js'
import { baseInfo } from '@/api/nlb/nlbCommon'

// 获取商品单位列表
export function selectGoodsUnitList(data) {
    data.appId =data.appId?data.appId:baseInfo.appId
    return request({
        url: `${basicPath3}goodsUnit/selectGoodsUnitList`,
        method: 'post',
        data: data
    })
}

// 商品单位新增
export function insertGoodsUnit(data) {
    data.appId =data.appId?data.appId:baseInfo.appId
    return request({
        // url: `${basicPath3}goodsUnit/insertGoodsUnit`,
        url: `${basicPath3}goodsUnit/add`,
        method: 'post',
        data: data
    })
}

// 商品单位查询
export function selectGoodsUnitInfo(data) {
    data.appId =data.appId?data.appId:baseInfo.appId
    return request({
        url: `${basicPath3}goodsUnit/selectGoodsUnitInfo`,
        method: 'post',
        data: data
    })
}

// 商品单位修改
export function updateGoodsUnitManage(data) {
    data.appId =data.appId?data.appId:baseInfo.appId
    return request({
        url: `${basicPath3}goodsUnit/updateGoodsUnitManage`,
        method: 'post',
        data: data
    })
}

// 商品单位删除
export function deleteGootsCategoryById(data) {
    data.appId =data.appId?data.appId:baseInfo.appId
    return request({
        url: `${basicPath3}goodsUnit/deleteGootsCategoryById`,
        method: 'post',
        data: data
    })
}

/**
 *
 * @returns 分类管理
 */
// 商品列表
export function selectGootsCategoryList(data) {
    data.appId =data.appId?data.appId:baseInfo.appId
    if(data.pageSize){

    }else{
      data.pageNum=1
      data.pageSize=100
    }
    return request({
        // url: `${basicPath3}gootsCategory/selectGootsCategoryList`,
        url: `${basicPath3}goods/category/page`,
        method: 'post',
        data: data
    })

}

// 商品分类新增
export function insertCategory(data) {
    data.appId =data.appId?data.appId:baseInfo.appId
    return request({
        // url: `${basicPath3}gootsCategory/insertGootsCategory`,
        url: `${basicPath3}goods/category/add`,
        method: 'post',
        data: data
    })
}

// 商品分类单个删除
export function deleteCategory(data) {
    return request({
        // url: `${basicPath3}gootsCategory/deleteGootsCategoryById`,
        url: `${basicPath3}goods/category/delete`,
        method: 'post',
        data: data
    })
}

// 商品分类单个查询
export function selectCategoryById(data) {
    data.appId =data.appId?data.appId:baseInfo.appId
    return request({
        // url: `${basicPath3}gootsCategory/selectGootsCategoryById`,
        url: `${basicPath3}goods/category/info`,
        method: 'post',
        data: data
    })
}

// 商品分类修改
export function updateCategory(data) {
    data.appId =data.appId?data.appId:baseInfo.appId
    return request({
        // url: `${basicPath3}gootsCategory/updateupdateGootsCategory`,
        url: `${basicPath3}goods/category/update`,
        method: 'post',
        data: data
    })
}

// 获取商品一级分类
export function getCategoryFirstList(data) {
    data.appId =data.appId?data.appId:baseInfo.appId
    return request({
        url: `${basicPath3}gootsCategory/selectGootsCategoryFirstList`,
        method: 'post',
        data: data
    })
}


// 获取商品二级分类
export function getCategorySecondList(data) {
    data.appId =data.appId?data.appId:baseInfo.appId
    return request({
        url: `${basicPath3}gootsCategory/selectGootsCategorySecondList`,
        method: 'post',
        data: data
    })
}
export const gootsCategoryList = (data) => {
    data.appId = baseInfo.appId
    if(data.pageSize){

    }else{
      data.pageNum=1
      data.pageSize=100
    }

    return request({
        // url: `${basicPath3}gootsCategory/selectGootsCategoryList`,
        url: `${basicPath3}goods/category/page`,
        method: 'post',
        data: data
    })
}

/**
 * 商品审核
 */

// 获取审核列表
export function getSelectGoodsList(data) {
    data.appId =data.appId?data.appId:baseInfo.appId
    return request({
        url: `${basicPath3}goods/selectGoodsList`,
        method: 'post',
        data: data
    })
}

// 审核商品
export function getExamineGoodsById(data) {
    data.appId =data.appId?data.appId:baseInfo.appId
    return request({
        url: `${basicPath3}goods/updateGoods`,
        method: 'post',
        data: data
    })
}

// 商品详情
export function getSelectGoodsInfo(data) {
    data.appId =data.appId?data.appId:baseInfo.appId
    return request({
        url: `${basicPath3}goods/selectGoodsInfo`,
        method: 'post',
        data: data
    })
}
