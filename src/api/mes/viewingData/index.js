import request from '@/utils/request'
import {basicPath6} from '@/api/base.js'

// 统计
export function titleCount(data) {
    return request({
        url: `${basicPath6}face/titleCount`,
        method: 'post',
        data: data
    })
}
//原料占比
export function materialsPercent(data) {
    return request({
        url: `${basicPath6}face/materialsPercent`,
        method: 'post',
        data: data
    })
}
//屠宰趋势
export function butcherTrend(data) {
    return request({
        url: `${basicPath6}face/butcherTrend`,
        method: 'post',
        data: data
    })
}
//生产数据
export function produceCount(data) {
    return request({
        url: `${basicPath6}face/produceCount`,
        method: 'post',
        data: data
    })
}
//入急冻库
export function inFreezeTop(data) {
    return request({
        url: `${basicPath6}face/inFreezeTop10`,
        method: 'post',
        data: data
    })
}
//无货导出

export function dailyReports(data) {
    return request({
        url: `${basicPath6}rongAn/dailyReports`,
        method: 'post',
        data: data,
        responseType: 'blob',
    })
}
//查询视频
export function equipmentList(data) {
    return request({
        url: `${basicPath6}rongAn/equipmentList`,
        method: 'post',
        data: data,
    })
}