import request from "@/utils/request"
import { basicPath3 } from "@/api/base.js"
import { baseInfo } from "@/api/nlb/nlbCommon"


/* 农赢保 ---> 担保贷款管理 */

// 查询企业列表
export const confQuotaGuar = (data) => {
  data.extsysCode = 100000010
  return request({
    url: "pay1.0/empayOperator/customerQueryPage",
    method: "post",
    data: data
  })
}

// 查询企业本身以及分配的担保信息
export const empayOperatorQueryByParentId = (data) => {
    data.extsysCode = 100000010
    return request({
        url: "pay1.0/empayOperator/queryByParentId",
        method: "post",
        data: data
    })
}

// 状态 (0取消 1提交成功，2畜牧师成功，3运营人成功，4服务店铺成功，5银行成功)

// 设置、移除担保企业
export const setConfQuotaGuar = (data) => {
    data.extsysCode = 100000010
    return request({
        url: "pay1.0/empayOperator/confQuotaGuar",
        method: "post",
        data: data
    })
}

//  新增企业担保额度
export const applyCorp = (data) => {
  data.extsysCode = 100000010
  return request({
      url: "pay1.0/payFQuota/applyCorp",
      method: "post",
      data: data
  })
}