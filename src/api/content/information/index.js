import request from '@/utils/request'
import {basicPath2} from '@/api/base.js'

// 查询资讯列表
export function listInformation(query) {
    return request({
        url: `${basicPath2}infoManage/queryInfoManageList`,
        method: 'post',
        data: query
    })
}


// 查询资讯详细
export function getInformation(query) {
  return request({
    url: `${basicPath2}infoManage/queryById`,
    method: 'post',
    data: query
})
}


// 新增资讯
export function addInformation(data) {
  return request({
    url: `${basicPath2}infoManage/insertInfo`,
    method: 'post',
    data: data
})
  }

// 修改资讯
export function updateInformation(data) {
  return request({
    url: `${basicPath2}infoManage/updateInfo`,
    method: 'post',
    data: data
})
  }
  
  // 删除资讯
  export function delInformation(query) {
    return request({
      url: `${basicPath2}infoManage/deleteInfo`,
      method: 'post',
      data: query
  })
  }
