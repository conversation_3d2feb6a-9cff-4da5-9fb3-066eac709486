import request from '@/utils/request'
import { basicPath2 } from '@/api/base.js'

//查询轮播图列表
export function operatingList(data) {
    return request({
        url: `${basicPath2}banner/queryBannerList`,
        method: 'post',
        data: data
    })
}
//查询轮播图详情
export function operatingDetails(data) {
    return request({
        url: `${basicPath2}banner/queryById`,
        method: 'post',
        data: data
    })
}
//查询轮播图编辑
export function operatingEdit(data) {
    return request({
        url: `${basicPath2}banner/updateBanner`,
        method: 'post',
        data: data
    })
}
//查询轮播图删除
export function operatingDelte(data) {
    return request({
        url: `${basicPath2}banner/deleteBanner`,
        method: 'post',
        data: data
    })
}
//查询轮播图新增
export function operatingAdd(data) {
    return request({
        url: `${basicPath2}banner/insertBanner`,
        method: 'post',
        data: data
    })
}
//获取全路径名
export function picPath() {
    return request({
        url: '/xmb-common/common/oss/getOssUrl',
        method: 'post'
    })
}