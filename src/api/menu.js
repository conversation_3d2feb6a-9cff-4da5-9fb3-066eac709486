import request from '@/utils/request'
import {basicPath1,basicPath2,basicPath3} from '@/api/base.js'
// 获取路由
export const getRouters = () => {
  return request({
    url: basicPath1 + 'getRouters',
    method: 'get'
  })
}

//首页统计数据
export function xmbuser(data) {
    return request({
        url: `${basicPath2}xmbuser/statistics`,
        method: 'post',
        data: data
    })
}
//店铺数据统计
export function Countbuser(data) {
    return request({
        url: `${basicPath3}goodsStore/selectStoreCount`,
        method: 'post',
        data: data
    })
}

// 运营-牛超市-商城店铺
export function storeTabulate(data) {
  return request({
    url: `${basicPath3}nlbOperating/storeTabulate`,
    method: 'post',
    data: data
  })
}


/**
 * @会员概览
 *
 */

  // 畜牧师数据
 export function memberTabulate(data) {
  return request({
      url: `${basicPath2}operating/memberTabulate`,
      method: 'post',
      data: data
  })
}




/**
 * @待办事项
 */

// 待办事项-牛超市-入驻供应商-待上架-待发货
export function nlbOperating(data) {
  return request({
      url: `${basicPath3}nlbOperating/backlog`,
      method: 'post',
      data: data
  })
}

// 活畜供求发布待审核
export function xmbOperating(data) {
  return request({
      url: `${basicPath2}operating/backlog`,
      method: 'post',
      data: data
  })
}

// 额度待审批统计
export function quotaToBeApprovedCount(data) {
  data.extsysCode = 100000010
  return request({
    url: "pay1.0/payExtsys/quotaToBeApprovedCount",
    method: 'post',
    data: data
  })
}

//  额度放款、消费统计
export function quotaStatistics(data) {
  data.extsysCode = 100000010
  return request({
    url: "pay1.0/payExtsys/quotaStatistics",
    method: 'post',
    data: data
  })
}