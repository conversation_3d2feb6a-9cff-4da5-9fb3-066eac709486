import request from '@/utils/request'
import { basicPath4 } from '@/api/base'

// 网关耳标数据记录
export function networkEarRecord(data) {
    return request({
        url: basicPath4 + 'iotData/list',
        method: 'post',
        data: data
    })
}

// 网关耳标数据汇总
export function networkEarSum(data) {
    return request({
        url: basicPath4 + 'dataSummary/list',
        method: 'post',
        data: data
    })
}
// 智能耳标数据记录 
export function intelEarRecord(data) {
    return request({
        url: basicPath4 + 'intelligentEarTag/list',
        method: 'post',
        data: data
    })
}
// 智能耳标预警记录
export function intelEarWarning(data) {
    return request({
        url: basicPath4 + 'iotWarning/selectList',
        method: 'post',
        data: data
    })
}