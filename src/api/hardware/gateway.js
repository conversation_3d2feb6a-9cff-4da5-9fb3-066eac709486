import request from '@/utils/request'
import { basicPath4 } from '@/api/base'
// 列表
export function networkList(data) {

    return request({
        url: basicPath4 + 'hardwareEartagNetwork/list',
        method: 'post',
        data: data
    })
}
//新增
export function networkInsert(data) {

    return request({
        url: basicPath4 + 'hardwareEartagNetwork/insert',
        method: 'post',
        data: data
    })
}
//修改
export function networkUpdata(data) {

    return request({
        url: basicPath4 + 'hardwareEartagNetwork/update',
        method: 'post',
        data: data
    })
}
//删除
export function networkDel(data) {

    return request({
        url: basicPath4 + 'hardwareEartagNetwork/delete',
        method: 'post',
        data: data
    })
}
//查看
export function networkInfo(data) {

    return request({
        url: basicPath4 + 'hardwareEartagNetwork/info',
        method: 'post',
        data: data
    })
}
//设备供应商
export function networkUsername(data) {

    return request({
        url: basicPath4 + 'deviceProvider/list',
        method: 'post',
        data: data
    })
}

//牧场网关列表
export function pastureGatList(data) {

    return request({
        url: basicPath4 + 'pastureGateway/pageList',
        method: 'post',
        data: data
    })
}
//牧场网关新增
export function pastureGatAdd(data) {
    return request({
        url: basicPath4 + 'pastureGateway/add',
        method: 'post',
        data: data
    })
}
//牧场网关编辑
export function pastureGatUp(data) {
    return request({
        url: basicPath4 + 'pastureGateway/update',
        method: 'post',
        data: data
    })
}

//牧场网关删除
export function pastureGatDel(data) {
    return request({
        url: basicPath4 + 'pastureGateway/delete',
        method: 'post',
        data: data
    })
}
//牧场网关详情
export function pastureGatInfo(data) {
    return request({
        url: basicPath4 + 'pastureGateway/info',
        method: 'post',
        data: data
    })
}


//  传感器列表接口
export function readinfoSelect(data) {
    return request({
        url: basicPath4 + 'readinfo/select',
        method: 'post',
        data: data
    })
}

