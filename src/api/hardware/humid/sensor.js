import request from "@/utils/request"
const basicPath = '/wmsapp-service/'

// 查询列表
export const getList = (data) => {
    return request({
        url: basicPath + "hardwareDeviceHumidity/page",
        method: "post",
        data: data
    })
}
// 添加
export const addList = (data) => {
    return request({
        url: basicPath + "hardwareDeviceHumidity/add",
        method: "post",
        data: data
    })
}
// 详情
export const queryList = (data) => {
    return request({
        url: basicPath + "hardwareDeviceHumidity/info",
        method: "post",
        data: data
    })
}
// 编辑
export const editList = (data) => {
    return request({
        url: basicPath + "hardwareDeviceHumidity/update",
        method: "post",
        data: data
    })
}
export const deleteList = (data) => {
    return request({
        url: basicPath + "hardwareDeviceHumidity/delete",
        method: "post",
        data: data
    })
}
export const exportQuotaTrans = (data) => {
    return request({
        url:basicPath + "hardwareDeviceHumidity/export",
        method: "post",
        responseType: 'blob',
        data: data,
    })
}