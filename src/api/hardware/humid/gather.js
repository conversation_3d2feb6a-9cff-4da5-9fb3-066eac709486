import request from "@/utils/request"
const basicPath = '/wmsapp-service/'
// SN码

// 查询SN码列表
export const getList = (data) => {
    return request({
        url: basicPath + "humitureEquipment/page",
        method: "post",
        data: data
    })
}

// 新增
export const addList = (data) => {
    return request({
        url: basicPath + "humitureEquipment/add",
        method: "post",
        data: data
    })
}

// 详情
export const queryList = (data) => {
    return request({
        url: basicPath + "humitureEquipment/info",
        method: "post",
        data: data
    })
}
// 编辑
export const editList = (data) => {
    return request({
        url: basicPath + "humitureEquipment/update",
        method: "post",
        data: data
    })
}
// 删除
export const deleteList = (data) => {
    return request({
        url: basicPath + "humitureEquipment/delete",
        method: "post",
        data: data
    })
}
export const exportQuotaTrans = (data) => {
    return request({
        url:basicPath + "humitureEquipment/export",
        method: "post",
        responseType: 'blob',
        data: data,
    })
}