import request from "@/utils/request"
const basicPath = '/wmsapp-service/'
// SN码

// 查询SN码列表
export const getList = (data) => {
    return request({
        url: basicPath + "humitureGateway/page",
        method: "post",
        data: data
    })
}

// 新增
export const addList = (data) => {
    return request({
        url: basicPath + "humitureGateway/add",
        method: "post",
        data: data
    })
}

// 详情
export const queryList = (data) => {
    return request({
        url: basicPath + "humitureGateway/info",
        method: "post",
        data: data
    })
}
// 编辑
export const editList = (data) => {
    return request({
        url: basicPath + "humitureGateway/update",
        method: "post",
        data: data
    })
}
// 删除
export const deleteList = (data) => {
    return request({
        url: basicPath + "humitureGateway/delete",
        method: "post",
        data: data
    })
}

// ----------------------------------------

// SN列表
export const numberList = (data) => {
    return request({
        url:basicPath + "humitureGateway/list",
        method: "post",
        data: data
    })
}

// 导出担保交易明细
/* export const exportQuotaTrans = (name, code) => {
  // data.extsysCode = "*********"
  return request({
    url: `pay1.0/export/exportQuotaTrans?fileName=${name}&orderCodes=${code}&extsysCode=*********`,
    method: "get",
    data: {
      extsysCode: *********
    }
  })
} */
export const exportQuotaTrans = (data) => {
    return request({
        url:basicPath + "humitureGateway/export",
        method: "post",
        responseType: 'blob',
        data: data,
    })
}



// 担保交易明细导出记录
export const queryExportRecord = (data) => {
    data.extsysCode = "*********"
    return request({
        url: "pay1.0/empayOperator/queryExportRecord",
        method: "post",
        data: data
    })
}


// 上传
export const uploadRepaymentRecords = (data) => {
    data.extsysCode = "*********"
    return request({
        url: "pay1.0/empayOperator/uploadRepaymentRecords",
        method: "post",
        data: data
    })
}

// 还款 - 查询预存记录 - 对比
export const queryPreRepayment = (data) => {
    return request({
        url: "pay1.0/empayOperator/queryPreRepayment",
        method: "post",
        data: data
    })
}

// 取消上传
export const cancelUploadRepayment = (data) => {
    data.extsysCode = "*********"
    return request({
        url: "pay1.0/empayOperator/cancelUploadRepayment",
        method: "post",
        data: data
    })
}

// 确认上传
export const confirmUploadRepayment = (data) => {
    data.extsysCode = "*********"
    return request({
        url: "pay1.0/empayOperator/confirmUploadRepayment",
        method: "post",
        data: data
    })
}

// 推荐企业列表
export const payFQuotaGuarantorList = (data) => {
    data.extsysCode = "*********"
    return request({
        url: "pay1.0/payFQuotaGuarantor/queryPage",
        method: "post",
        data: data
    })
}

// 字典接口
export const qryParamByType = (data) => {
    data.extsysCode = "*********"
    return request({
        url: "pay1.0/empayOperator/qryParamByType",
        method: "post",
        data: data
    })
}
// 导出
export const exportQuotaRecord = (query) => {
    query.extsysCode = "*********"
    return request({
        url: "pay1.0/export/exportQuotaRecord",
        method: "get",
        responseType: 'blob',
        params: query
    })
}

// 字典接口
export const payFQuotaGuarantorQueryPage = (data) => {
    data.extsysCode = "*********"
    return request({
        url: "pay1.0/payFQuotaGuarantor/queryPage",
        method: "post",
        data: data
    })
}
// 银行列表
export const selectBank = (data) => {
    data.extsysCode = "*********"
    return request({
        url: "pay1.0/payFCustomer/selectBank",
        method: "post",
        data: data
    })
}
// 撤销
export const empayRollback = (data) => {
    data.extsysCode = "*********"
    return request({
        url: "pay1.0/empayOperator/rollback",
        method: "post",
        data: data
    })
}
