import request from '@/utils/request'
import {basicPath1} from '@/api/base'

// 查询缓存详细
export function getDevicesList(query) {
  return request({
    url: basicPath1 + 'hardware/camera/deviceList',
    method: 'get',
    params: query
  })
}

export function getChannelList(query) {
  return request({
    url: basicPath1 + 'hardware/camera/channelList',
    method: 'get',
    params: query
  })
}

export function edit(query){
  return request({
    url: basicPath1 + 'hardware/camera/edit',
    method: 'get',
    params: query
  })
}

export function transport(data) {
  return request({
    url: basicPath1 + 'hardware/camera/transport',
    method: 'post',
    data: data
  })
}

export function play(query) {
  return request({
    url: basicPath1 + 'hardware/camera/play',
    method: 'get',
    params: query
  })
}

