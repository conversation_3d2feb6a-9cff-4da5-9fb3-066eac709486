import request from '@/utils/request'
import {basicPath4} from '@/api/base'

export function deviceAdd(data) {
  return request({
    url: basicPath4 + 'deviceProvider/add',
    method: 'post',
    data
  })
}

export function deviceList(data) {
  return request({
    url: basicPath4 + 'deviceProvider/list',
    method: 'post',
    data
  })
}


export function deviceUpdate(data) {
  return request({
    url: basicPath4 + 'deviceProvider/update',
    method: 'post',
    data
  })
}

export function devicDel(data) {
  return request({
    url: basicPath4 + 'deviceProvider/deleteById',
    method: 'post',
    data
  })
}
