<template>
  <div>
    <el-dialog
      title="绑定活畜"
      :visible.sync="dialogEar.open"
      width="800px"
      :close-on-click-modal="false"
      @close="refuse"
      class="dialogEar"
      append-to-body
    >
      <el-tabs
        v-model="activeName"
        type="card"
        @tab-click="handleClick"
        :stretch="true"
        v-if="!dialogEar.tab"
      >
        <el-tab-pane label="单只绑定" name="1"></el-tab-pane>

        <el-tab-pane label="批量绑定" name="2"></el-tab-pane>
      </el-tabs>

      <!-- 单只耳标 -->

      <el-form
        :model="form"
        :rules="rules"
        ref="ruleForm"
        label-width="120px"
        class="demo-ruleForm"
      >
        <el-form-item label="养殖场名称">
          <el-input v-model="pastureName" disabled />
        </el-form-item>

        <el-form-item label="养殖场地址">
          <el-input v-model="pastureArea" disabled />
        </el-form-item>
        <el-form-item label="所在圈舍" prop="penId">
          <el-select
            v-model="form.penId"
            clearable
            class="selectWidth"
            @change="selectPenId"
            :disabled="disable"
          >
            <el-option
              v-for="(item,index) in penList"
              :label="item.penName"
              :value="item.penId"
              :key="index"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="耳标来源">
          <el-radio-group v-model="form.source" @change="selectChange" :disabled="dialogEar.tab">
            <el-radio label="1">系统生成</el-radio>

            <el-radio label="2">第三方</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 批量绑定 -->

        <div v-if="activeName==2">

          <el-row>
            <el-col :span="12">
              <el-form-item label="公扣编号" prop="pinNoStart">
                <el-input type="number" v-model="form.pinNoStart" placeholder="起始编号" />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="至" prop="pinNoEnd" label-width="35px">
                <el-input type="number" v-model="form.pinNoEnd" placeholder="结束编号" />
              </el-form-item>
            </el-col>
          </el-row>
          <span style="color:red;padding-left: 120px; ">注：耳标数量不能大于500</span>
          <el-row>

            <el-col :span="12">
              <el-form-item :label="'耳标编号'" prop="earTagStart" class="earTagNo">
                <span  >{{prefix}}</span>

                <el-select
                  class="selectWidth"
                  v-model="form.earTagStart"
                  clearable
                  filterable
                  remote
                  reserve-keyword
                  placeholder="请输入耳标编号"
                  :remote-method="remoteMethod"
                  @change="sectEarTag"
                >
                  <el-option
                    v-for="item in options"
                    :key="item.earTagId"
                    :label="form.source==1?item.earTagNo.slice(1): item.earTagNo"
                    :value="form.source==1?item.earTagNo.slice(1): item.earTagNo"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item
                :label="'至'"
                prop="earTagEnd"
                class="earTagNo"
                label-width="40px"
              >
                <span>{{prefix}}</span>

                <el-select
                  class="selectWidth"
                  v-model="form.earTagEnd"
                  clearable
                  filterable
                  remote
                  reserve-keyword
                  placeholder="请输入耳标编号"
                  :remote-method="remoteMethod"
                  @change="sectEarTag"
                >
                  <el-option
                    v-for="item in options"
                    :key="item.earTagId"
                    :label="form.source==1?item.earTagNo.slice(1): item.earTagNo"
                    :value="form.source==1?item.earTagNo.slice(1): item.earTagNo"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row style="margin-bottom:20px;color:red;padding-left: 120px;" >
            耳标数量：{{differEar||0}}
          </el-row>
        </div>

        <!-- 单只耳标 -->

        <div v-if="activeName==1">
          <el-form-item label="母扣编号" prop="maleEarTagNo">
            <el-input type="number" v-model="form.maleEarTagNo" placeholder="请输入母扣编号" :disabled="dialogEar.tab"/>
          </el-form-item>

          <el-form-item :label="'耳标编号'+prefix" prop="earTagNo" class="earTagNo">
            <!-- <span style="margin-right:10px">{{prefix}}</span> -->

            <el-select
              :disabled="dialogEar.tab"
              class="selectWidth"
              v-model="form.earTagNo"
              clearable
              filterable
              remote
              reserve-keyword
              placeholder="请输入耳标编号"
              :remote-method="remoteMethod"
              @change="tagNoChange"
            >
              <el-option
                v-for="item in options"
                :key="item.earTagId"
                :label="form.source==1?item.earTagNo.slice(1): item.earTagNo "
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="芯片编号" prop="earTagUid">
            <el-input v-model="form.earTagUid" disabled />
          </el-form-item>
        </div>

        <el-form-item label="活畜类别" prop="typeId">
          <el-select
            v-model="form.typeId"
            clearable
            class="selectWidth"
            @change="selectCategory"
            :disabled="disable"
          >
            <el-option
              v-for="(item,index) in animalsCategory"
              :label="item.livestockName"
              :value="item.livestockId"
              :key="index"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="活畜品种" prop="varietiesId">
          <el-select
            v-model="form.varietiesId"
            clearable
            class="selectWidth"
            @change="selectType('1')"
            :disabled="disable"
          >
            <el-option
              v-for="(item,index) in animalsVarieties"
              :label="item.varietiesName"
              :value="item.varietiesId"
              :key="index"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="活畜类型" prop="categoryId">
          <el-select
            v-model="form.categoryId"
            clearable
            class="selectWidth"
            @change="selectType('2')"
            :disabled="disable"
          >
            <el-option
              v-for="(item,index) in animalsType"
              :label="item.categoryName"
              :value="item.categoryId"
              :key="index"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="怀胎次数" v-if="showPregnanciesNum && activeName==1">
          <el-input type="number" v-model="form.pregnanciesNum" />
        </el-form-item>

        <!-- 单只耳标 -->

        <div v-if="activeName==1">
          <el-form-item label="活畜月龄" prop="livestockAge">
            <el-select
              v-model="form.livestockAge"
              placeholder="请选择活畜月龄"
              clearable
              class="selectWidth"
            >
              <el-option
                v-for="dict in dict.type.livestock_age"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>


          <el-form-item label="出生日期" prop="birthday">
            <el-date-picker
              value-format="yyyy-MM-dd hh:mm:ss"
              v-model="form.birthday"
              type="date"
              placeholder="选择日期"
              class="selectWidth"
            ></el-date-picker>
          </el-form-item>

          <el-form-item label="父亲编号类型" prop="fatherTagType">
            <el-select v-model="form.fatherTagType" placeholder="请选择" clearable class="selectWidth">
              <el-option
                v-for="dict in dict.type.livestock_tag_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="父亲编号" prop="fatherTagNo">
            <el-input v-model="form.fatherTagNo" />
          </el-form-item>

          <el-form-item label="母亲编号类型" prop="motherTagType">
            <el-select v-model="form.motherTagType" placeholder="请选择" clearable class="selectWidth">
              <el-option
                v-for="dict in dict.type.livestock_tag_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="母亲编号" prop="motherTagNo">
            <el-input v-model="form.motherTagNo" />
          </el-form-item>

          <el-form-item label="活畜图片" prop="livestockImage">
            <image-upload :disabled="disabled" v-model="form.livestockImage" :limit="5"></image-upload>
          </el-form-item>
        </div>

        <el-form-item label="检疫证明" prop="quarantineReportUrl">
            <image-upload :disabled="disabled" v-model="form.quarantineReportUrl" :limit="5"></image-upload>
          </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="refuse">关闭</el-button>

        <el-button type="primary" @click="submitForm" v-if="!dialogEar.tab">提交</el-button>

        <el-button type="primary" @click="submitEdit" v-if="dialogEar.tab">提交</el-button>
      </span>
    </el-dialog>
  </div>
</template>



    <script>
import { getDicts } from "@/api/system/dict/data.js";

import { earTaglist, pasturePenList, pastureFenceCodeList } from "@/api/system/earTagManage";

import {
  pastureById,
  earTagAdd,
  earTagBatch,
  livestockList,
  varietiesList,
  animalTypeList,
  earTagEdit,
} from "@/api/ffs/supervisionSheet/siteManagement.js";

export default {
  dicts: ["livestock_age", "livestock_tag_type"],

  props: {
    dialogEar: {
      type: Object,

      default: () => {
        return {};
      },
    },
  },

  data() {
    // 是否是正整数 不包含0

    const isMobile = (rule, value, callback) => {
      if (!/^1[3456789]\d{9}$/.test(value)) {
        return callback(new Error("手机号格式不正确"));
      } else {
        callback();
      }
    };

    return {
      prefix: "A", //耳标前缀

      pastureName: "", //养殖场

      pastureArea: "", //养殖场

      options: [],
      disable:false,
      differEar:null,
      form: {
        source: "1", //耳标来源

        pinNoStart: undefined,

        pinNoEnd: undefined,

        earTagStart: undefined,

        earTagEnd: undefined,

        earTagUidStart: undefined,

        earTagUidEnd: undefined,

        pastureId: "",

        userId: "",

        typeId: undefined,

        typeName: undefined,

        categoryId: undefined,

        categoryName: undefined,

        varietiesId: undefined,

        varietiesName: undefined,

        earTagNo: undefined,

        maleEarTagNo: undefined,

        livestockAge: undefined,

        livestockAgeValue: undefined,

        birthday: undefined,

        fatherTagType: undefined,

        fatherTagTypeValue: undefined,

        fatherTagNo: undefined,

        motherTagType: undefined,

        motherTagTypeValue: undefined,

        motherTagNo: undefined,

        livestockImage: undefined,
        quarantineReportUrl: undefined,
        penId: '', // 圈舍id
        fenceCode: '', // 栏位编号
        pregnanciesNum: null, // 怀胎次数
      },
      showPregnanciesNum: false,

      activeName: "1",

      animalsCategory: [], //活畜类别

      animalsVarieties: [], //品种

      animalsType: [], //类型

      selectList: [],

      disabled: false,

      rules: {
        earTagStart: [
          {
            required: true,

            message: "请填写",

            trigger: "blur",
          },
        ],

        earTagNo: [
          {
            required: true,

            message: "请填写",

            trigger: "blur",
          },
        ],
        penId: [
          {
            required: true,

            message: "请填写",

            trigger: "blur",
          },
        ],
        typeId: [
          {
            required: true,

            message: "请选择",

            trigger: "blur",
          },
        ],

        categoryId: [
          {
            required: true,

            message: "请选择",

            trigger: "blur",
          },
        ],

        varietiesId: [
          {
            required: true,

            message: "请选择",

            trigger: "blur",
          },
        ],

        livestockAge: [
          {
            required: false,

            message: "请选择",

            trigger: "blur",
          },
        ],

        birthday: [
          {
            required: false,

            message: "请选择",

            trigger: "blur",
          },
        ],

        fatherTagType: [
          {
            required: false,

            message: "请选择",

            trigger: "blur",
          },
        ],

        fatherTagNo: [
          {
            required: false,

            message: "请填写",

            trigger: "blur",
          },
        ],

        motherTagNo: [
          {
            required: false,

            message: "请填写",

            trigger: "blur",
          },
        ],

        motherTagType: [
          {
            required: false,

            message: "请选择",

            trigger: "blur",
          },
        ],

        livestockImage: [
          {
            required: true,

            message: "请上传",

            trigger: "blur",
          },
        ],
      },
      penList: [],
      fenceList: [],
    };
  },

  created() {
    this.form.pastureId = this.$route.query.pastureId;
    console.log('pastureId');
    console.log(this.$route.query.pastureId)
    console.log('pastureId');
    this.getCategory();

    this.getPasturebyId();

    this.$nextTick(() => {
      if (this.dialogEar.tab) {
        this.getVarieties(this.form.typeId);

        this.getType(this.form.typeId);
      }
    });
    this.getPasturePenList()
  },

  methods: {
    //活畜养殖场信息

    getPasturebyId() {
      pastureById({ ids: [this.form.pastureId] }).then((res) => {
        if (res.code == 200) {
          this.form.userId = res.result.userId;

          this.pastureName = res.result.pastureName;

          this.pastureArea =
            res.result.provinceName +
            res.result.cityName +
            res.result.countyName;
        }
      });
    },
    sectEarTag(){
        this.differEar=null
        if(this.form.earTagEnd&&this.form.earTagStart){
            if(parseInt(this.form.earTagStart)>=parseInt(this.form.earTagEnd)){
            this.$modal.msgWarning("起始编号不能大于等于结束编号");
            return
        }
            if(parseInt(this.form.earTagEnd) - parseInt(this.form.earTagStart)>500){
            this.$modal.msgWarning("耳标数量不能大于500");

            return
            }
            this.differEar=parseInt(this.form.earTagEnd) - parseInt(this.form.earTagStart) +1
        }

    },
    selectChange() {
      this.options = [];

      if (this.activeName == 1) {
        this.form.earTagNo = "";
      } else {
        this.form.earTagEnd = "";

        this.form.earTagStart = "";
      }

      this.form.source == 1 ? (this.prefix = "A") : (this.prefix = "");
    },

    tagNoChange() {
      this.form.earTagUid = this.form.earTagNo.earTagUid;

      this.form.earTagQrCode = this.form.earTagNo.earTagQrCode;

      if (this.form.source == 1) {
        this.form.earTagNo = this.form.earTagNo.earTagNo.slice(1);
      } else {
        this.form.earTagNo = this.form.earTagNo.earTagNo;
      }
    },

    //搜索耳标

    remoteMethod(value) {
      earTaglist({
        pageNum: 1,

        pageSize: 10,

        statusStr: "1,2",

        source: this.form.source,

        earTagNo: this.prefix + value,
      }).then((res) => {
        if (res.code == 200) {
          this.options = res.result.list;
        }
      });
    },

    submitForm() {
      this.$refs["ruleForm"].validate(async (valid) => {
        if (!valid) {
          return;
        }
        this.$modal.loading("正在提交，请稍候...");
        if (this.activeName == 1) {
          this.singleUpload();
        } else {
          this.batchUpload();
        }
      });
    },

    //编辑方法

    submitEdit() {
      this.$refs["ruleForm"].validate(async (valid) => {
        if (!valid) {
          return;
        }
        this.$modal.loading("正在提交，请稍候...");
        if (this.form.source == 1) {
          this.form.earTagNo = this.form.earTagNo;
        }

        let obj = JSON.parse(JSON.stringify(this.form));

        earTagEdit(obj).then((res) => {
          if (res.code == 200) {
            this.$modal.closeLoading();
            this.$message({
              type: "success",

              message: "编辑成功",
            });

            this.refuse();
          }

        }).catch(()=>{
        this.$modal.closeLoading();
      });
      });
    },

    //单只上传

    singleUpload() {
      if (!this.form.earTagUid) {
        this.$message({
          message: "该耳标未绑定芯片编号，不可被绑定",

          type: "error",
        });
        this.$modal.closeLoading();
        return;
      }

      let obj = JSON.parse(JSON.stringify(this.form));

      earTagAdd(obj).then((res) => {

        if (res.code == 200) {
            this.$modal.closeLoading();
          this.$message({
            type: "success",

            message: "添加成功",
          });

          this.refuse();
        }

      }).catch(()=>{
        this.$modal.closeLoading();
      });
    },

    //批量上传

    batchUpload() {
      let obj = {
        pastureId: this.form.pastureId,

        userId: this.form.userId,

        earTagNo: [this.form.earTagStart, this.form.earTagEnd].toString(),

        typeName: this.form.typeName,

        typeId: this.form.typeId,

        varietiesId: this.form.varietiesId,

        varietiesName: this.form.varietiesName,

        categoryId: this.form.categoryId,

        categoryName: this.form.categoryName,
        source: this.form.source,
        penId: this.form.penId,
        quarantineReportUrl: this.form.quarantineReportUrl,
        fenceCode: this.form.fenceCode
      };

      if (this.form.pinNoStart && this.form.pinNoEnd) {
        obj.maleEarTagNo = [
          this.form.pinNoStart,
          this.form.pinNoEnd,
        ].toString();
      }

      if (this.form.pinNoEnd && this.form.pinNoEnd) {
        if (
          parseInt(this.form.pinNoEnd) - parseInt(this.form.pinNoStart) !=
          parseInt(this.form.earTagEnd) - parseInt(this.form.earTagStart)
        ) {
          this.$modal.msgWarning("公扣编号数量和耳标编号数量要一致！");
          this.$modal.closeLoading();
          return;
        }

      }

      if(this.form.earTagEnd&&this.form.earTagStart){
            if(parseInt(this.form.earTagStart)>=parseInt(this.form.earTagEnd)){
            this.$modal.msgWarning("起始编号不能大于等于结束编号");
            this.$modal.closeLoading();
            return
        }
            if(parseInt(this.form.earTagEnd) - parseInt(this.form.earTagStart)>500){
            this.$modal.msgWarning("耳标数量不能大于500");
            this.$modal.closeLoading();
            return
            }
        }
      earTagBatch(obj).then((res) => {
        if (res.code == 200) {
            this.$modal.closeLoading();
          this.$message({
            type: "success",

            message: "添加成功",
          });

          this.refuse();
        }

      }).catch(()=>{
        this.$modal.closeLoading();
      });

    },

    refuse() {
      this.$emit("close");

      this.$emit("refresh");
    },

    //获取活畜信息

    getCategory() {
      livestockList({ pageNum: 1, pageSize: 100000 }).then((res) => {
        this.animalsCategory = res.result;
      });
    },

    getVarieties(typeId) {
      animalTypeList({
        pageNum: 1,

        pageSize: 100000,

        categoryType: typeId,
      }).then((res) => {
        this.animalsType = res.result;
      });
    },

    //或者品种

    getType(typeId) {
      varietiesList({
        pageNum: 1,

        pageSize: 100000,

        categoryType: typeId,
      }).then((res) => {
        this.animalsVarieties = res.result;
      });
    },

    handleClick({ name }) {
      this.activeName = name;
    },

    selectType(type, val) {
      if (type == 1) {
        this.animalsVarieties.forEach((item) => {
          if (this.form.varietiesId == item.varietiesId) {
            this.form.varietiesName = item.varietiesName;
          }
        });
      }

      if (type == 2) {
        this.animalsType.forEach((item) => {
          if (this.form.categoryId == item.categoryId) {
            this.form.categoryName = item.categoryName;
            console.log(this.form.categoryName)
            if(this.form.categoryName.includes('母')){
              this.showPregnanciesNum = true
            } else {
              this.showPregnanciesNum = false
            }
          }
        });
      }
    },

    selectCategory() {
      this.form.varietiesId = "";

      this.form.categoryId = "";

      this.getVarieties(this.form.typeId);

      this.getType(this.form.typeId);

      this.animalsCategory.forEach((item) => {
        if (this.form.typeId == item.livestockId) {
          this.form.typeName = item.livestockName;
        }
      });
    },
    // 获取当前牧场圈舍
    getPasturePenList(){
      pasturePenList({ pastureId: this.form.pastureId || '', pid: 0 }).then(res => {
        console.log(res)
        if(res.code == 200) {
          this.penList = res.result || []
        }
      })
    },

    // 获取当前圈舍栏位
    selectPenId(){
      this.form.fenceCode = ''
      console.log(this.form.penId)
      pastureFenceCodeList({penId: this.form.penId}).then(res => {
        console.log(res)
        if(res.code ==200) {
          this.fenceList = res.result || []
        }
      })
    }
  },
};
</script>



    <style lang="scss">
.dialogEar {
  .el-dialog__header {
    background-color: #f4f4f4;
  }

  .earTagNo {
    .el-input__prefix {
      left: 22px;
    }
  }

  .el-dialog__footer {
    text-align: center;
  }

  .el-form-item__content {
    display: flex;
  }

  .selectWidth {
    width: 100%;
  }
}

.inputWidth {
  width: 100%;
}
</style>

