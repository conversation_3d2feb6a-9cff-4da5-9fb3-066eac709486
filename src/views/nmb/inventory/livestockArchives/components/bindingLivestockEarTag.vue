<template>
  <div>
    <el-dialog
      title="绑定耳标"
      :visible.sync="dialog.openBingEarTag"
      width="1100px"
      :close-on-click-modal="false"
      @close="refuse"
      class="dialogEar"
      append-to-body
    >
        <!-- 没有pastureId时显示养殖场选择 -->
        <el-row v-if="!hasPastureId" style="margin: 0 10px 10px 10px">
          <el-col :span="12">
            <span>选择养殖场：</span>
            <el-select
              v-model="queryParams.pastureId"
              clearable
              filterable
              style="margin: 0 10px; width: 200px;"
              @change="onPastureChange"
              placeholder="请选择养殖场"
            >
              <el-option
                v-for="(item, index) in pastureList"
                :label="item.pastureName"
                :value="item.pastureId"
                :key="index"
              />
            </el-select>
          </el-col>
          <el-col :span="12">
            <span>选择圈舍：</span>
            <el-select
              v-model="queryParams.penId"
              clearable
              filterable
              style="margin: 0 10px;"
              :disabled="!queryParams.pastureId"
              @change="getList"
            >
              <el-option label="全部" value />
              <el-option
                v-for="(item, index) in penList"
                :label="item.penName"
                :value="item.penId"
                :key="index"
              />
            </el-select>
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="getList"
              :disabled="!queryParams.pastureId"
            >查询</el-button>
          </el-col>
        </el-row>

        <el-row style="margin: 0 10px 10px 10px">
          <el-col :span="24" style="margin-top: 10px;">
              <span>耳标来源：</span>
              <el-radio-group v-model="earTagSource">
                <el-radio label="1">系统生成</el-radio>
                <el-radio label="2">第三方</el-radio>
              </el-radio-group>
          </el-col>
        </el-row>
      <el-table
        ref="multipleTable"
        :header-cell-class-name="cellClass"
        :data="tableData"
        stripe
        style="width: 100%"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        :height="460"
        border
      >
        <!-- <el-table-column type="selection" width="55" :selectable="selectEnable"></el-table-column> -->
        <el-table-column align="center" prop="penName" label="所在圈舍"></el-table-column>
        <el-table-column align="center" prop="earTagNo" label="耳标编号"  width="260">
          <template slot-scope="scope">
<!--            <el-input type="text" v-model="scope.row.earTagNo" @change="(val)=>{ changeInput(val, scope.row) }"></el-input>-->
            <el-input type="number" v-model="scope.row.earTagNo" style="width: 220px;margin: 10px;">
              <template slot="prepend" v-if="earTagSource == '1'">A</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="varietiesName" label="活畜品种"></el-table-column>
        <el-table-column align="center" prop="categoryName" label="活畜类型"></el-table-column>
        <el-table-column align="center" prop="livestockAge" label="月龄">
          <template slot-scope="scope">{{ getAgeVal(scope.row.livestockAge) }}</template>
        </el-table-column>
        <el-table-column align="center" prop="birthday" label="出生日期">
          <template slot-scope="scope">{{ formatYmd(scope.row.birthday) }}</template>
        </el-table-column>
        <el-table-column align="center" prop="weightTime" label="称重日期">
          <template slot-scope="scope">{{ formatYmd(scope.row.weightTime) }}</template>
        </el-table-column>
        <el-table-column align="center" prop="livestockWeight" label="重量"></el-table-column>
        <el-table-column align="center" prop="status" label="状态" width="120">
          <template
            slot-scope="scope"
          >{{ getLivestockStatusVal(scope.row.status) }}</template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
      <span slot="footer" class="dialog-footer">
        <el-button @click="refuse">关闭</el-button>
        <el-button type="primary" @click="saveEarTag">保存修改</el-button>
      </span>
    </el-dialog>
  </div>
</template>
    <script>
      import {
        selectPasturePenList
      } from "@/api/ffs/supervisionSheet/siteManagement.js";
      import { getDicts } from "@/api/system/dict/data.js";
      import { bindEarTag, getLivestockList } from "@/api/xmb/pasture/index.js";
      import { pasturelist } from "@/api/nmb/inventory/index.js";
export default {
  props: {
    dialog: {
      type: Object,
      default: {},
    },
  },

  data() {
    return {
      earTagSource: "1",
      tableData: [],
      loading: true,
      total: 0,
      ageData: [], //年龄
      earRagData: [], //编号类型
      penList: [],
      pastureList: [], //养殖场列表
      queryParams: {
        pastureId: "",
        penId: "",
        emptyEarTagStatus: "1",
        pageNum: 1,
        pageSize: 50,
      }
    };
  },

  computed: {
    // 判断是否有pastureId参数
    hasPastureId() {
      return !!this.$route.query.pastureId;
    }
  },

  created() {
    this.getEarTagData();
    this.getAgeData();
    this.queryParams.pastureId = this.$route.query.pastureId;

    if (this.hasPastureId) {
      // 有pastureId的情况，直接获取圈舍列表和活畜列表
      this.getPasturePenList();
      this.getList();
    } else {
      // 没有pastureId的情况，先获取养殖场列表，并设置loading为false
      this.loading = false;
      this.getPastureList();
    }
  },

  methods: {
    formatYmd (value) {
      if (!value) return '';
      let date = new Date(value);
      let year = date.getFullYear();
      let month = ("0" + (date.getMonth() + 1)).slice(-2);
      let day = ("0" + date.getDate()).slice(-2);
      return `${year}-${month}-${day}`;
    },
    getLivestockStatusVal(status){
      if(status === 1){
        return "正常";
      }else if(status === 2){
        return "抵押"
      }else if(status === 3){
        return "代养"
      }
      return "--";
    },
    saveEarTag(){
      let changed = [];
      this.tableData.forEach(item=>{
        if(item.earTagNo){
          let earTagNo = item.earTagNo;
          if(this.earTagSource == '1'){
            earTagNo = "A"+earTagNo;
          }
          changed.push({livestockId: item.livestockId, earTagNo: earTagNo});
        }
      })
      let data = {};
      data.source = this.earTagSource;
      data.pastureId = this.queryParams.pastureId;
      data.livestockList = changed;
      data = JSON.parse(JSON.stringify(data));
      console.log(data);

      this.$modal.loading("正在提交，请稍候...");

      bindEarTag(data).then((res) => {
        if (res.code == 200) {
          this.$modal.closeLoading();
          this.$message({
            type: "success",
            message: "绑定成功",
          });
          this.refuse();
        }
      }).catch(() => {
        this.$modal.closeLoading();
      });
    },
    changeInput(val, row){
      const regex = /^(A)?\d+$/;
      if(!regex.test(val)){
        row.earTagNo = row.lastEerTagNo;
      }else{
        row.lastEerTagNo = row.earTagNo;
      }
    },
    //查看数据字典
    getAgeData() {
      getDicts("livestock_age").then((res) => {
        if (res.code == 200) {
          this.ageData = res.data;
        }
      });
    },
    getEarTagData() {
      getDicts("livestock_code_type").then((res) => {
        if (res.code == 200) {
          this.earRagData = res.data;
        }
      });
    },
    // 获取养殖场列表
    getPastureList() {
      pasturelist({ pageNum: 1, pageSize: 100 }).then((res) => {
        if (res.code == 200) {
          this.pastureList = res?.result?.list || [];
        }
      });
    },
    // 获取圈舍列表
    getPasturePenList() {
      if (!this.queryParams.pastureId) {
        this.penList = [];
        return;
      }
      selectPasturePenList({ pageNum: 1, pageSize: 50, pastureId: this.queryParams.pastureId }).then((res) => {
        console.log(res.result)
        this.penList = res.result || [];
      });
    },
    // 养殖场变化处理
    onPastureChange() {
      // 清空圈舍选择
      this.queryParams.penId = "";
      this.penList = [];
      this.tableData = [];
      this.total = 0;

      if (this.queryParams.pastureId) {
        // 获取新的圈舍列表
        this.getPasturePenList();
      }
    },
    refuse() {
      this.$emit("close");
      this.$emit("refresh");
    },
    //列表查询
    getList() {
      if (!this.queryParams.pastureId) {
        this.$message.warning('请先选择养殖场');
        this.loading = false;
        this.tableData = [];
        this.total = 0;
        return;
      }

      this.loading = true;
      getLivestockList(this.queryParams).then((res) => {
        if (res.code == 200) {
          this.tableData = res?.result?.list || [];
          this.tableData.forEach(it=>{
            it.oldEarTagNo = it.earTagNo;
            it.lastEerTagNo = it.earTagNo;
          })
          console.log(this.tableData)
          this.total = Number(res?.result?.total);
          this.loading = false;
        }
      }).catch(() => {
        this.loading = false;
      });
    },
    handelAge() {
      return (value, list) => {
        let name = "";
        list.forEach((item) => {
          if (value == item.dictValue) {
            name = item.dictLabel;
          }
        });
        return name;
      };
    },
    getAgeVal(val){
      let name = "";
      if(val){
        this.ageData.forEach(item=>{
          if (val == item.dictValue) {
            name = item.dictLabel;
          }
        });
      }
      return name;
    },
    getEarRagVal(val){
      let name = "";
      this.earRagData.forEach(item=>{
        if (Number(val) == Number(item.dictValue)) {
          name = item.dictLabel;
        }
      });
      return name;
    },
//多选
    handleSelectionChange(val) {
      this.ids = [];
      val.forEach((item) => {
        this.ids.push({
          livestockId: item.livestockId,
          earTagNo: item.earTagNo,
        });
      });
    },
    //全选按钮隐藏
    cellClass(row) {
      if (this.DisableSelection) {
        // if (row.columnIndex === 0) {
        return "DisableSelection";
        // }
      }
    },

  }
};
</script>



    <style lang="scss">
.dialogEar {
  .el-dialog__header {
    background-color: #f4f4f4;
  }

  .earTagNo {
    .el-input__prefix {
      left: 22px;
    }
  }

  .el-dialog__footer {
    text-align: center;
  }

  .el-form-item__content {
    display: flex;
  }

  .selectWidth {
    width: 100%;
  }
}

.inputWidth {
  width: 100%;
}
</style>

