<template>
  <div class="app-container">
    <HeadForm :queryParams="queryParams" ref="queryForm">
      <el-form-item label="监管状态" prop="superviseStatus">
        <el-select v-model="queryParams.superviseStatus" placeholder="全部" clearable size="small" style="width: 200px">
          <el-option label="监管中" :value="1" />
          <el-option label="结束监管" :value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="监管方公司名称" prop="companyName">
        <el-input v-model="queryParams.companyName" placeholder="请输入监管方公司名称" clearable size="small"
          style="width: 200px" />
      </el-form-item>
      <el-form-item label="剩余天数" prop="daysUntilEnd">
        <el-select v-model="queryParams.daysUntilEnd" placeholder="全部" clearable size="small" style="width: 200px">
          <el-option label="1天" :value="1" />
          <el-option label="2天" :value="2" />
          <el-option label="3天" :value="3" />
        </el-select>
      </el-form-item>
      <el-form-item label="监管开始日期范围" prop="dateRange">
        <el-date-picker v-model="queryParams.dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" size="small" style="width: 240px" value-format="yyyy-MM-dd" />
      </el-form-item>
      <el-form-item label="监管到期日期范围" prop="dateRange2">
        <el-date-picker v-model="queryParams.dateRange2" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" size="small" style="width: 240px" value-format="yyyy-MM-dd" />
      </el-form-item>
    </HeadForm>

    <el-card shadow="never">
      <el-row class="mb8 form_btn">
        <el-col class="form_btn_col">
          <el-button type="primary" plain size="mini" @click="handleAdd">新增</el-button>
        </el-col>
      </el-row>

      <el-table :data="dataList" border v-loading="loading" style="width: 100%" height="400"
        v-tableHeight="{ bottomOffset: 69 }">
        <el-table-column prop="purchaseOrderCode" label="采购订单编号" align="center" min-width="120" />
        <el-table-column prop="companyName" label="监管方公司名称" align="center" min-width="120" />
        <el-table-column prop="fundsArrivalDate" label="资金到账时间" align="center" min-width="120" />
        <el-table-column prop="capitalAmount" label="资金金额" align="center" min-width="100">
          <template slot-scope="scope">
            {{ (scope.row.capitalAmount / 10000).toFixed(2) }}万元
          </template>
        </el-table-column>
        <el-table-column label="监管周期" align="center" min-width="150">
          <template slot-scope="scope">
            {{ scope.row.supervisionStartDate }}~{{ scope.row.supervisionEndDate }}
          </template>
        </el-table-column>
        <el-table-column prop="repaymentTime" label="还款日期" align="center" min-width="100" />
        <el-table-column prop="daysUntilEnd" label="还款剩余天数" align="center" min-width="120">
          <template slot-scope="scope">
            <span v-if="scope.row.superviseStatus === 1">{{ scope.row.daysUntilEnd }}天</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="superviseStatus" label="监管状态" align="center" min-width="100">
          <template slot-scope="scope">
            <span v-if="scope.row.superviseStatus === 1">监管中</span>
            <span v-else-if="scope.row.superviseStatus === 2">结束监管</span>
            <span v-else>{{ scope.row.superviseStatus }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" min-width="150" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleExtend(scope.row)"
              v-if="scope.row.superviseStatus === 1">延长监管日期</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 分页 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 新增弹窗 -->
    <AddForm v-if="addDialogVisible" :visible.sync="addDialogVisible" @success="handleAddSuccess" />
  </div>
</template>

<script>
import { purchaseSupervisePage } from '@/api/nmb/inventory/index.js'
import HeadForm from '@/components/HeadForm/index'
import AddForm from './components/addForm.vue'
import { tableUi } from '@/utils/mixin/tableUi.js'

export default {
  mixins: [tableUi],
  components: {
    HeadForm,
    AddForm
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 新增弹窗显示状态
      addDialogVisible: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        superviseStatus: null,
        companyName: null,
        daysUntilEnd: null,
        startTime: '',
        endTime: '',
        startTime2: '',
        endTime2: '',
        dateRange: [],
        dateRange2: []
      },
      // 表格数据
      dataList: [],
      // 总条数
      total: 0
    }
  },
  created() {
  },
  mounted() {
    this.loading = false
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true

      // 处理时间范围
      const params = { ...this.queryParams }
      if (params.dateRange && params.dateRange.length === 2) {
        params.startTime = params.dateRange[0]
        params.endTime = params.dateRange[1]
      }
      if (params.dateRange2 && params.dateRange2.length === 2) {
        params.startTime2 = params.dateRange2[0]
        params.endTime2 = params.dateRange2[1]
      }
      delete params.dateRange
      delete params.dateRange2
      purchaseSupervisePage(params).then(res => {
        if (res.code === 200) {
          this.dataList = res.result.list || []
          this.total = Number(res.result.total || 0)
        } else {
          this.$message.error(res.message || '获取数据失败')
        }
        this.loading = false
      }).catch(error => {
        console.error('获取列表数据失败:', error)
        this.$message.error('获取数据失败')
        this.loading = false
      })
     
    },

    // 搜索按钮操作
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },

    // 重置按钮操作
    resetQuery() {
      // 重置查询参数到初始状态
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        superviseStatus: null,
        companyName: null,
        daysUntilEnd: null,
        startTime: '',
        endTime: '',
        startTime2: '',
        endTime2: '',
        dateRange: [],
        dateRange2: []
      }
      this.handleQuery()
    },

    // 重置表单
    resetForm(refName) {
      if (this.$refs[refName]) {
        this.$refs[refName].resetFields()
      }
    },

    // 新增按钮操作
    handleAdd() {
      console.log('handleAdd called, current addDialogVisible:', this.addDialogVisible)
      this.addDialogVisible = true
      console.log('after setting, addDialogVisible:', this.addDialogVisible)
    },

    // 新增成功回调
    handleAddSuccess() {
      this.getList()
    },

    // 延长监管日期
    handleExtend(row) {
      this.$message.info(`延长监管日期功能待开发，订单号：${row.purchaseOrderCode}`)
    },

    // 查看详情
    handleDetail(row) {
      this.$message.info(`查看详情功能待开发，订单号：${row.purchaseOrderCode}`)
    }
  }
}
</script>

<style scoped lang='scss'></style>
