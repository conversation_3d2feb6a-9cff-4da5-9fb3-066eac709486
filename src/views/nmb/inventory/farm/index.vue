<template>
  <div class="app-container">
    <HeadForm :queryParams='queryParams'>
      <el-form-item label="用户姓名：" prop="corprateName">
        <el-input v-model="queryParams.corprateName" placeholder="请输入用户姓名" clearable />
      </el-form-item>

      <el-form-item label="用户联系电话：" prop="userPhone">
        <el-input oninput="if(value.length>11)value=value.slice(0,11)" v-model="queryParams.userPhone"
          placeholder="请输入用户联系电话" clearable />
      </el-form-item>
      <el-form-item label="养殖场性质：" prop="pastureNature">
        <el-select v-model="queryParams.pastureNature" clearable class="selectWidth">
          <el-option v-for="dict in dict.type.pasture_nature" :key="dict.label" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间：">
        <el-date-picker v-model="time" value-format="yyyy-MM-dd" type="daterange" range-separator="至"
          start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
    </HeadForm>

    <!-- 表格数据 -->
    <!-- <el-table :data="tableData" stripe   v-loading="loading" :summary-method="getSummaries"
    show-summary style="width: 100%"  height="300" v-tableHeight="{bottomOffset:69}"> -->
    <el-card shadow="never">
      <el-row class="mb8 form_btn">
        <el-col class="form_btn_col">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="addFrom">新增</el-button>
        </el-col>
      </el-row>
      <el-table :data="tableData" border v-loading="loading" style="width: 100%" height="300"
        v-tableHeight="{ bottomOffset: 69 }">
        <el-table-column type="index" align="center" label="序号" fixed="left" width="50"></el-table-column>

        <el-table-column prop="pastureName" label="养殖场名称" min-width="120" align="center" />
        <el-table-column prop="pastureName" label="养殖场性质" min-width="120" align="center"> </el-table-column>
        <el-table-column prop="subjectName" label="详细地址" min-width="200" align="center" show-overflow-tooltip>
          <template slot-scope="scope">{{ scope.row.provinceName }}{{ scope.row.cityName }}{{ scope.row.countyName }} {{
            scope.row.address }}</template>
        </el-table-column>
        <el-table-column prop="corprateName" label="用户姓名" align="center" width="120">
          <template slot-scope="scope">
            {{ scope.row.corprateName || scope.row.nickName || scope.row.userName || scope.row.phonenumber }}
            <!-- {{ scope.row.corprateName }} -->
          </template>
        </el-table-column>
        <el-table-column prop="userPhone" label="联系电话" width="120" align="center"></el-table-column>
        <el-table-column prop="livestockNum" label="存栏活畜" width="110" align="center"></el-table-column>
        <el-table-column prop="earTagNum" label="耳标活畜" width="110" align="center"></el-table-column>
        <el-table-column prop="createTime" label="创建日期" width="200" align="center"></el-table-column>
        <el-table-column label="操作" fixed="right" width="300" align="center">
          <template slot-scope="scope">
            <el-button @click="goInfo(scope.row.pastureId)" icon="el-icon-share" size="mini"
              type="text">活畜信息</el-button>
            <el-button class="btn_color_four" @click="goVideos(scope.row.pastureId)" icon="el-icon-video-camera"
              size="mini" type="text">视频</el-button>
            <el-button @click="goPen(scope.row.pastureId)" v-if="userType != 2 && userType != 3" icon="el-icon-film"
              size="mini" type="text">圈舍管理</el-button>
            <el-button @click="goods(scope.row)" v-if='scope.row.shopId' icon="el-icon-shopping-cart-1" size="mini"
              type="text">货品管理</el-button>
            <el-button @click="goBringAdopt(scope.row.pastureId)" v-if="scope.row.jynFlag" icon="el-icon-film"
              size="mini" type="text">认养管理</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" />
    </el-card>

    <el-dialog title="查看" :visible.sync="dialog.open" width="700px" :close-on-click-modal="true" @close="close"
      class="fieldList">

      <div class="itemSpan-title">养殖场基础信息</div>
      <div style="padding-left: 20px">
        <el-row class="itemSpan">
          <el-col :span="12">养殖场性质：{{ pasture.pastureNature }} </el-col>

        </el-row>
        <el-row class="itemSpan">
          <el-col :span="12">养殖场地址：{{ pasture.provinceName }}{{ pasture.cityName }}{{ pasture.countyName }} </el-col>
        </el-row>
        <el-row class="itemSpan">
          <el-col :span="12">养殖场详细地址：{{ pasture.address }} </el-col>
        </el-row>
        <el-row class="itemSpan">
          <el-col :span="12">养殖方式：{{ pasture.cultivateType }} </el-col>
        </el-row>
      </div>



      <div class="itemSpan-title">活畜信息</div>
      <div v-for='(item) in pasture.pastureBreedingList' :key="item.breedingId"
        style="margin-bottom: 9px; padding-left: 20px">
        <el-row>
          <el-col :span="12">活畜类别： {{ item.typeName }} - {{ item.categoryName }} - {{ item.varietiesName }}</el-col>
        </el-row>
        <el-row>
          <el-col :span="12">养殖规模： {{ item.breedingScale }}</el-col>
        </el-row>
        <el-row>
          <el-col :span="12">养殖生产率（%）： {{ item.productivity }}</el-col>
        </el-row>
        <el-row>
          <el-col :span="12">养殖成本（元/天）： {{ item.breedingCost }}</el-col>
        </el-row>
      </div>

      <div class="itemSpan-title">草场信息</div>
      <div v-for='(item) in pasture.pastureMeadowList' :key="item.meadowId"
        style="margin-bottom: 9px; padding-left: 20px">
        <el-row class="itemSpan">
          <el-col :span="12">草场类型：{{ item.meadowType }} </el-col>
        </el-row>
        <el-row class="itemSpan">
          <el-col :span="12">草场面积：{{ item.meadowArea }} 亩</el-col>
        </el-row>
        <el-row class="itemSpan" v-if="item.livestockShedImg">
          <el-col :span="12">棚圈照片：
            <el-image v-for="(e, i) in item?.livestockShedImg?.split(',')" style="width: 100px; height: 100px" :src="e"
              :key="i" :preview-src-list="item?.livestockShedImg?.split(',')">
            </el-image>

          </el-col>
        </el-row>
        <el-row class="itemSpan" v-if="item.meadowImg">
          <el-col :span="12">草场照片：
            <el-image v-for="(e, i) in item?.meadowImg?.split(',')" style="width: 100px; height: 100px" :src="e"
              :key="i" :preview-src-list="item?.meadowImg?.split(',')">
            </el-image>
          </el-col>
        </el-row>
        <el-row class="itemSpan" v-if="item.troughImg">
          <el-col :span="12">水槽料槽照片：
            <el-image v-for="(e, i) in item?.troughImg?.split(',')" :key="i" style="width: 100px; height: 100px"
              :src="e" :preview-src-list="item?.troughImg?.split(',')">
            </el-image>
          </el-col>
        </el-row>
        <el-row class="itemSpan" v-if="item.equipmentImg">
          <el-col :span="12">机械设备照片：
            <el-image v-for="(e, i) in item?.equipmentImg?.split(',')" :key="i" style="width: 100px; height: 100px"
              :src="e" :preview-src-list="item?.equipmentImg?.split(',')">
            </el-image>
          </el-col>
        </el-row>
      </div>

      <div class="itemSpan-title">饲草料消耗</div>
      <div v-for='(item) in pasture.pastureForageList' :key="item.forageId"
        style="margin-bottom: 9px; padding-left: 20px">
        <div v-if='item.forageType == 2'>
          <el-row class="itemSpan">
            <el-col :span="12" style="color: #000">牧草消耗</el-col>
          </el-row>
          <el-row class="itemSpan">
            <el-col :span="12">牧草类型：{{ item.forageName }} </el-col>
          </el-row>
          <el-row class="itemSpan">
            <el-col :span="12">用量(吨/年)：{{ item.forageCost }} </el-col>
          </el-row>
        </div>
        <div v-if='item.forageType == 1'>
          <el-row class="itemSpan">
            <el-col :span="12">饲料消耗</el-col>
          </el-row>

          <el-row class="itemSpan">
            <el-col :span="12">饲料类型：{{ item.forageName }} </el-col>
          </el-row>
          <el-row class="itemSpan">
            <el-col :span="12">用量(吨/年)：{{ item.forageCost }} </el-col>
          </el-row>
        </div>
      </div>
    </el-dialog>
    <!-- 新增 -->
    <addModel v-if="dialogAdd.open" :dialogAdd="dialogAdd" @close="close" @refresh="refresh"></addModel>
  </div>
</template>

<script>
import { pasturelist, goodsLivestockListExport } from "@/api/nmb/inventory/index.js";
import { tableUi } from "@/utils/mixin/tableUi.js";
import addModel from "./components/addModel.vue";
import { exportExcel } from "@/utils/east";
import HeadForm from '@/components/HeadForm/index'


export default {
  mixins: [tableUi],
  dicts: ["pasture_nature"],
  components: {
    addModel,
    HeadForm
  },
  data() {
    return {
      dialog: {
        open: false,
        title: "",
        id: "",
      },
      //新增
      dialogAdd: {
        open: false,
        title: "",
        id: "",
      },
      time: undefined,
      queryParams: {
        userPhone: '',
        corprateName: '',
        pastureNature: '',
        startTime: '',
        endTime: '',
        pageNum: 1,
        pageSize: 20,
      },
      loading: true,
      total: 0,
      tableData: [],
      pastureNatureList: [],
      pasture: {},
      totalEarTagNum: 0,
      totalLivestockNum: 0,
      userInfo: {},
      userType: 0
    };
  },
  computed: {

  },
  created() {
    const userInfo = JSON.parse(localStorage.getItem('USERINFO'))
    // 平台用户
    if (userInfo.platformModel && userInfo.platformModel.defaultPlatform == 1) {
      this.userType = 1
    }
    // 省级
    else if (userInfo.platformModel && userInfo.platformModel.defaultPlatform == 0 && !userInfo.enterpriseModel && userInfo.userType === '00') {
      this.userType = 2
    }
    // 市县
    else if (userInfo.platformModel && userInfo.platformModel.defaultPlatform == 0 && userInfo.enterpriseModel && userInfo.enterpriseModel.companyType == 9 && userInfo.userType === '00') {
      this.userType = 3
    }
    // this.userInfo = JSON.parse(window.localStorage.getItem('USERINFO'))
    this.getList();
  },

  methods: {
    goDetail(item) {
      this.dialog.open = true
      this.pasture = item
    },
    showStatus(val) {
      if (val == 0) {
        return '未监管'
      } else {
        return '监管中'
      }
    },
    //列表查询
    getList() {
      pasturelist({ ...this.queryParams, platformId: this.$store.state.user.user.platformId }).then((res) => {
        if (res.code == 200) {
          this.tableData = res.result.list;
          this.total = Number(res.result.total);
          this.loading = false;
          this.totalLivestockNum = res.result.params.totalLivestockNum
          this.totalEarTagNum = res.result.params.totalEarTagNum
        }
      });
    },
    reset() {
      this.resetForm("queryForm");
    },
    close() {
      this.dialog.open = false;
      this.dialog.id = "";
      this.dialogAdd.open = false;
      this.dialogAdd.id = "";
    },
    //创建养殖场
    addFrom() {
      this.dialogAdd.open = true;
      this.dialogAdd.title = "新增";
      this.dialogAdd.id = null
    },
    //编辑
    goEdit(id) {
      this.dialogAdd.open = true;
      this.dialogAdd.title = "编辑";
      this.dialogAdd.id = id;
    },
    //重置
    resetQuery() {
      this.reset();
      this.time = null
      this.handleQuery();
    },
    //刷新页面
    refresh() {

      this.getList();
    },
    //搜索
    handleQuery() {
      if (this.time && this.time.length > 0) {
        this.queryParams.startTime = this.time[0];
        this.queryParams.endTime = this.time[1];
      } else {
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
      }
      this.queryParams.pageNum = 1;
      this.getList();
    },

    goEdit(item) {
      this.$router.push('/pasture/details/animalList/' + item)
    },

    //活畜详情
    goInfo(id) {
      this.$router.push({ path: "/inventoryapp/livestock/inventory", query: { pastureId: id } });
    },
    //视频监控
    goVideos(id) {
      this.$router.push({ path: "/pasture/monitorList", query: { pastureId: id } });
    },
    goPen(id) {
      this.$router.push({ path: '/pasture/penList', query: { id: id } })
    },
    goods(item) {
      this.$router.push({ path: '/pasture/goods', query: { id: item.pastureId, shopId: item.shopId, name: item.pastureName } })
    },
    goBringAdopt(id) {
      this.$router.push({ path: '/pasture/bringAdopt', query: { id: id } })
    },
    exportList() {
      exportExcel(goodsLivestockListExport, {
      }, '货品列表');
    },
  },
};
</script>

<style lang="scss" scoped>
.el-col-12 {
  display: flex;
  align-items: center;
  margin: 10px 0;
}
</style>
