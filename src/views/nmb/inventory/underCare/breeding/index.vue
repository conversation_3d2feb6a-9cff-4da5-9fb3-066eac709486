<template>
  <div class="app-container">
      <HeadForm :queryParams="queryParams" ref="queryForm">
        <el-form-item label="投喂圈舍" prop="livestockManageId">
          <el-input v-model="queryParams.livestockManageId" placeholder="请输入投喂圈舍" clearable size="small"
            style="width: 200px" />
        </el-form-item>
        <el-form-item label="饲料种类" prop="feedFood">
          <el-select v-model="queryParams.feedFood" placeholder="请选择饲料种类" clearable size="small" style="width: 200px">
            <el-option v-for="item in feedFoodOptions" :key="item.dictValue" :label="item.dictLabel"
              :value="parseInt(item.dictValue)" />
          </el-select>
        </el-form-item>
        <el-form-item label="投喂人员" prop="operatePeopleName">
          <el-input v-model="queryParams.operatePeopleName" placeholder="请输入投喂人员" clearable size="small"
            style="width: 200px" />
        </el-form-item>
        <el-form-item label="投喂时间" prop="dateRange">
          <el-date-picker v-model="queryParams.dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期" size="small" style="width: 240px" value-format="yyyy-MM-dd" />
        </el-form-item>
      </HeadForm>

    <el-card shadow="never">
      <el-table :data="dataList" border v-loading="loading" style="width: 100%" height="400"
        v-tableHeight="{ bottomOffset: 69 }">
        <el-table-column type="index" align="center" label="序号" fixed="left" width="60" />
        <el-table-column prop="pastureName" label="投喂圈舍" align="center" min-width="120" />
        <el-table-column prop="feedFood" label="饲料种类" align="center" min-width="100">
          <template slot-scope="scope">
            {{ getFeedFoodLabel(scope.row.feedFood) }}
          </template>
        </el-table-column>
        <el-table-column prop="feedNum" label="投喂数量 (Kg)" align="center" min-width="120" />
        <el-table-column prop="feedFrequency" label="投喂次数" align="center" min-width="100">
          <template slot-scope="scope">
            {{ getFeedFrequencyLabel(scope.row.feedFrequency) }}
          </template>
        </el-table-column>
        <el-table-column prop="manageRemark" label="备注" align="center" min-width="150" />
        <el-table-column prop="operatePeopleName" label="投喂人员" align="center" min-width="100" />
        <el-table-column prop="operateTime" label="投喂时间" align="center" min-width="180" />
      </el-table>
    </el-card>

    <!-- 分页 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
  </div>
</template>

<script>
import { feedPage } from '@/api/nmb/inventory/index.js'
import { getDicts } from '@/api/system/dict/data'
import HeadForm from '@/components/HeadForm/index'
import { tableUi } from '@/utils/mixin/tableUi.js'

export default {
  mixins: [tableUi],
  components: {
    HeadForm
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        pastureId: 0,
        penId: 0,
        feedFood: null,
        feedFrequency: null,
        startTime: '',
        endTime: '',
        operatePeopleName: '',
        pastureName: '',
        livestockManageId: '',
        dateRange: []
      },
      // 表格数据
      dataList: [],
      // 总条数
      total: 0,
      // 饲料种类字典
      feedFoodOptions: [],
      // 投喂频率字典
      feedFrequencyOptions: []
    }
  },
  created() {
    this.getDictData()
  },
  mounted() {
    this.loading = false
    this.getList()
  },
  methods: {
    // 获取字典数据
    async getDictData() {
      try {
        // 获取饲料种类字典
        const feedFoodRes = await getDicts('pasture_feed_food')
        if (feedFoodRes.code === 200) {
          this.feedFoodOptions = feedFoodRes.data || []
        }

        // 获取投喂频率字典
        const feedFrequencyRes = await getDicts('pasture_feed_frequency')
        if (feedFrequencyRes.code === 200) {
          this.feedFrequencyOptions = feedFrequencyRes.data || []
        }
      } catch (error) {
        console.error('获取字典数据失败:', error)
      }
    },

    // 获取列表数据
    getList() {
      this.loading = true

      // 处理时间范围
      const params = { ...this.queryParams }
      if (params.dateRange && params.dateRange.length === 2) {
        params.startTime = params.dateRange[0]
        params.endTime = params.dateRange[1]
      }
      delete params.dateRange

      feedPage(params).then(res => {
        if (res.code === 200) {
          this.dataList = res.result.list || []
          this.total = Number(res.result.total || 0)
        } else {
          this.$message.error(res.message || '获取数据失败')
        }
        this.loading = false
      }).catch(error => {
        console.error('获取列表数据失败:', error)
        this.$message.error('获取数据失败')
        this.loading = false
      })
    },

    // 搜索按钮操作
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },

    // 重置按钮操作
    resetQuery() {
      // 重置查询参数到初始状态
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        pastureId: 0,
        penId: 0,
        feedFood: null,
        feedFrequency: null,
        startTime: '',
        endTime: '',
        operatePeopleName: '',
        pastureName: '',
        livestockManageId: '',
        dateRange: []
      }
      this.handleQuery()
    },

    // 重置表单
    resetForm(refName) {
      if (this.$refs[refName]) {
        this.$refs[refName].resetFields()
      }
    },

    // 获取饲料种类标签
    getFeedFoodLabel(value) {
      const option = this.feedFoodOptions.find(item => parseInt(item.dictValue) === value)
      return option ? option.dictLabel : value
    },

    // 获取投喂频率标签
    getFeedFrequencyLabel(value) {
      const option = this.feedFrequencyOptions.find(item => parseInt(item.dictValue) === value)
      return option ? option.dictLabel : value
    }
  }
}
</script>

<style scoped lang='scss'></style>
