<template>
    <div>
        <el-dialog
            :title="`${dialogData.title}`"
            :visible.sync="dialogData.open"
            width="1000px"
            :close-on-click-modal="false"
            @close="close"
            append-to-body
        >
            <el-card class="mb10 form_box" shadow="never">
                <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
                    <el-form-item label="操作时间：" prop="dateRange">
                        <el-date-picker
                            v-model="queryParams.dateRange"
                            type="daterange"
                            range-separator="-"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            value-format="yyyy-MM-dd"
                        />
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-card>

            <el-table
                :data="tableData"
                border
                v-loading="loading"
                style="width: 100%"
                height="400"
            >
                <el-table-column type="index" align="center" label="序号" width="60"></el-table-column>
                <el-table-column prop="operatePeopleName" label="姓名"  align="center" />
                <el-table-column prop="phonenumber" label="联系电话" align="center" />
                <el-table-column prop="actionRemark" label="操作内容" align="center" />
                <el-table-column prop="operateTime" label="操作时间" align="center" />
            </el-table>

            <div style="margin-top: 20px; text-align: right;">
                <el-pagination
                    v-show="total > 0"
                    :total="total"
                    :page.sync="queryParams.pageNum"
                    :limit.sync="queryParams.pageSize"
                    @pagination="getList"
                    layout="total, sizes, prev, pager, next, jumper"
                    :page-sizes="[10, 20, 50, 100]"
                />
            </div>

            <span slot="footer" class="dialog-footer">
                <el-button @click="close">关 闭</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import { actionRecordPage } from "@/api/nmb/inventory/index.js";

export default {
    props: {
        dialogData: {
            type: Object,
            required: true
        }
    },
    data() {
        return {
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                xmbBreederId: null,
                startTime: '',
                endTime: '',
                dateRange: []
            },
            loading: false,
            total: 0,
            tableData: []
        };
    },
    watch: {
        'dialogData.open'(val) {
            if (val) {
                this.initData();
            }
        }
    },
    methods: {
        initData() {
            this.queryParams.xmbBreederId = this.dialogData.xmbBreederId;
            this.resetQuery();
        },

        getList() {
            this.loading = true;
            if (this.queryParams.dateRange && this.queryParams.dateRange.length === 2) {
                this.queryParams.startTime = this.queryParams.dateRange[0];
                this.queryParams.endTime = this.queryParams.dateRange[1];
            } else {
                this.queryParams.startTime = '';
                this.queryParams.endTime = '';
            }

            const params = {
                pageNum: this.queryParams.pageNum,
                pageSize: this.queryParams.pageSize,
                xmbBreederId: this.queryParams.xmbBreederId,
                startTime: this.queryParams.startTime,
                endTime: this.queryParams.endTime
            };

            actionRecordPage(params).then(res => {
                if (res.code === 200) {
                    this.tableData = res.result.list || [];
                    this.total = Number(res.result.total) || 0;
                }
                this.loading = false;
            }).catch(error => {
                console.error('获取操作记录失败:', error);
                this.loading = false;
            });
        },

        // 搜索
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },

        // 重置查询
        resetQuery() {
            this.queryParams.dateRange = [];
            this.queryParams.startTime = '';
            this.queryParams.endTime = '';
            this.queryParams.pageNum = 1;
            this.getList();
        },

        // 关闭弹窗
        close() {
            this.queryParams = {
                pageNum: 1,
                pageSize: 10,
                xmbBreederId: null,
                startTime: '',
                endTime: '',
                dateRange: []
            };
            this.tableData = [];
            this.total = 0;
            this.$emit('close');
        }
    }
};
</script>

<style lang="scss" scoped>
.mb10 {
    margin-bottom: 10px;
}

.form_box {
    padding: 15px;
}

.dialog-footer {
    text-align: right;
}
</style>
