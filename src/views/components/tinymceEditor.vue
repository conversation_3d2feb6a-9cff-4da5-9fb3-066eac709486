<!-- tinymce富文本编辑器组件 license by http://eleadmin.com -->
<template>
    <div>
      <editor ref="tinymceReft" id="tinymceReft" :init="config" :disabled="false" :value="value" @input="updateValue"/>
      <!-- <insert-video :visible.sync="selectVideo" @change="changeVideo"/> -->
    </div>
  </template>
  
  <script>
//   import InsertVideo from './insert-video'
  import tinymce from 'tinymce/tinymce'
  import 'tinymce/icons/default'
  import 'tinymce/themes/silver'
  import 'tinymce/plugins/code'
  import 'tinymce/plugins/print'
  import 'tinymce/plugins/preview'
  import 'tinymce/plugins/fullscreen'
  import 'tinymce/plugins/paste'
  import 'tinymce/plugins/searchreplace'
  import 'tinymce/plugins/save'
  import 'tinymce/plugins/autosave'
  import 'tinymce/plugins/link'
  import 'tinymce/plugins/autolink'
  import 'tinymce/plugins/image'
  import 'tinymce/plugins/imagetools'
  import 'tinymce/plugins/media'
  import 'tinymce/plugins/table'
  import 'tinymce/plugins/codesample'
  import 'tinymce/plugins/lists'
  import 'tinymce/plugins/advlist'
  import 'tinymce/plugins/hr'
  import 'tinymce/plugins/charmap'
  import 'tinymce/plugins/emoticons'
  import 'tinymce/plugins/anchor'
  import 'tinymce/plugins/directionality'
  import 'tinymce/plugins/pagebreak'
  import 'tinymce/plugins/quickbars'
  import 'tinymce/plugins/nonbreaking'
  import 'tinymce/plugins/visualblocks'
  import 'tinymce/plugins/visualchars'
  import 'tinymce/plugins/wordcount'
  import 'tinymce/plugins/emoticons/js/emojis'
  import Editor from '@tinymce/tinymce-vue'
  import request from '@/utils/request'
  import { basicPath2 } from "@/api/base.js";
  
  // 默认配置
  const defaultConfig = {
    selection: '#tinymceReft',
    height: 300,
    branding: false,
    skin_url: '/tinymce/skins/ui/oxide',
    content_css: '/tinymce/skins/content/default/content.min.css',
    language_url: '/tinymce/langs/zh_CN.js',
    language: 'zh_CN',
    menubar: false,
    plugins: [
      'code',
      'print',
      'preview',
      'fullscreen',
      'paste',
      'searchreplace',
      'save',
      'autosave',
      'link',
      'autolink',
      'image',
      'imagetools',
      // 'myvideo',
      'media',
      'table',
      'codesample',
      'lists',
      'advlist',
      'hr',
      'charmap',
      'emoticons',
      'anchor',
      'directionality',
      'pagebreak',
      'quickbars',
      'nonbreaking',
      'visualblocks',
      'visualchars',
      'wordcount'
    ].join(' '),
    toolbar: [
      'fullscreen',
      'preview',
      'code',
      '|',
      'undo',
      'redo',
      '|',
      'forecolor',
      'backcolor',
      '|',
      'bold',
      'italic',
      'underline',
      'strikethrough',
      '|',
      'alignleft',
      'aligncenter',
      'alignright',
      'alignjustify',
      '|',
      'outdent',
      'indent',
      '|',
      'numlist',
      'bullist',
      '|',
      'formatselect',
      'fontselect',
      'fontsizeselect',
      '|',
      'link',
      'image',
      'myvideo',
      // 'media',
      'emoticons',
      'charmap',
      'anchor',
      'pagebreak',
      'codesample',
      '|',
      'ltr',
      'rtl'
    ].join(' '),
    toolbar_drawer: 'sliding',
    file_picker_types: 'media',
    file_picker_callback: () => {
    },
    save_onsavecallback: function () {
      console.log('已保存')
    }
  }
  
  export default {
    name: 'TinymceEditor',
    components: { Editor },
    model: {
      prop: 'value',
      event: 'change'
    },
    props: {
      // 值
      value: String,
      // 编辑器配置
      init: Object,
      // 是否禁用
      disabled:  {
        type: Boolean,
        default: false
      },
      // 自动跟随框架暗黑主题
      autoTheme: {
        type: Boolean,
        default: true
      },
      // 图片上传地址
      imgUploadPath: {
        type: String,
        default: process.env.VUE_APP_BASE_API + `${basicPath2}files/obs/fileUpload`,
      }
    },
    data () {
      let theme
      if (this.autoTheme) {
        theme = {
          skin_url: '/tinymce/skins/ui/oxide',
          content_css:'/tinymce/skins/content/default/content.min.css'
        }
      }
      let target = {
        images_upload_handler: (blobInfo, success, error) => {
          let file = blobInfo.blob()
          // 使用axios上传
          const formData = new FormData()
          formData.append('file', file, file.name)
          request({
              url:this.imgUploadPath,
              method: 'post',
              data: formData
          }).then(res => {
            if (res.code === 200) {
              success(res.result[0].objectUrl)
            } else {
              // error(res.data.msg)
            }
          }).catch(e => {
            error(e.message || '上传异常')
          })
        },
        setup: (editor) => {
          this.editor = editor
          editor.ui.registry.addButton('myvideo', {
            icon: 'embed',
            onAction: () => {
              this.selectVideo = true
            }
          })
        }
      }
      let config = Object.assign(target, defaultConfig, this.init, theme)
      return {
        // 编辑器配置
        config: config,
        editor: null,
        selectVideo: false
      }
    },
    computed: {
      // 是否是暗黑模式
      darkMode () {
        try {
          return this.$store.state.theme.darkMode
        } catch (e) {
          return null
        }
      }
    },
    mounted () {
      tinymce.init({})
    },
    methods: {
      clearContent () {
        this.editor.setContent('')
      },
      // 选择视频
      changeVideo (row) {
        this.insertContent('<p><video src="' + row.Url + '" data-info="' + row.Vid + '-' + row.Type + '" controls controlslist="nodownload"></video></p>')
      },
      insertContent (content) {
        this.editor.insertContent(content)
      },
      /* 更新value */
      updateValue (value) {
        this.$emit('change', value)
      },
      /* 切换编辑器主题 */
      changeTheme (darkMode) {
        document.head.querySelectorAll('[id^="mce-"]').forEach(elem => {
          let href = elem.getAttribute('href')
          if (href.indexOf('/oxide-dark/') !== -1) {
            if (!darkMode) {
              href = href.replace('/oxide-dark/', '/oxide/')
              elem.setAttribute('href', href)
            }
          } else {
            if (darkMode) {
              href = href.replace('/oxide/', '/oxide-dark/')
              elem.setAttribute('href', href)
            }
          }
        })
        this.changeContentTheme(darkMode)
      },
      /* 切换编辑器内容区的主题 */
      changeContentTheme (darkMode) {
        document.body.querySelectorAll('iframe[id^="tiny-vue_"]').forEach(frame => {
          const win = frame.contentWindow
          if (win) {
            const doc = win.document
            if (doc) {
              doc.head.querySelectorAll('[id^="mce-"]').forEach(elem => {
                let href = elem.getAttribute('href')
                if (href.indexOf('/skins/ui/') !== -1) {
                  if (href.indexOf('/oxide-dark/') !== -1) {
                    if (!darkMode) {
                      href = href.replace('/oxide-dark/', '/oxide/')
                      elem.setAttribute('href', href)
                    }
                  } else {
                    if (darkMode) {
                      href = href.replace('/oxide/', '/oxide-dark/')
                      elem.setAttribute('href', href)
                    }
                  }
                } else if (href.indexOf('/skins/content/') !== -1) {
                  if (href.indexOf('/dark/') !== -1) {
                    if (!darkMode) {
                      href = href.replace('/dark/', '/default/')
                      elem.setAttribute('href', href)
                    }
                  } else {
                    if (darkMode) {
                      href = href.replace('/default/', '/dark/')
                      elem.setAttribute('href', href)
                    }
                  }
                }
              })
            }
          }
        })
      }
    },
    watch: {
      darkMode () {
        if (this.autoTheme) {
          this.changeTheme(this.darkMode)
        }
      }
    }
  }
  </script>
  
  <style>
  body .tox-tinymce-aux {
    z-index: 19892000;
  }
  </style>
  