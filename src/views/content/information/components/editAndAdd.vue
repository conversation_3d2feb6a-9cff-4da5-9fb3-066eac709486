<template>
  <div>
    <el-form ref="form" :model="form" :rules="rules" label-width="80px">
      <el-row>
        <el-col :span="12">
          <el-form-item label="产品名称" prop="product">
            <el-select
              :disabled="infoShow"
              v-model="form.product"
              placeholder="请选择产品名称"
            >
              <el-option
                v-for="dict in dict.type.banner_product_name"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="资讯模块" prop="infoModule">
            <el-select
              :disabled="infoShow"
              v-model="form.infoModule"
              placeholder="请选择资讯模块"
            >
              <el-option
                v-for="dict in dict.type.info_module"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="资讯类型" prop="infoType">
            <el-select
              :disabled="infoShow || disabled"
              v-model="form.infoType"
              placeholder="请选择资讯类型"
            >
              <el-option
                v-for="dict in dict.type.info_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="资讯标签" prop="tag">
            <el-select
              :disabled="infoShow"
              v-model="form.tag"
              placeholder="请选择资讯标签"
              @change="onchang"
            >
              <el-option
                v-for="dict in dict.type.info_tag"
                :key="dict.value"
                :label="dict.label"
                :value="dict.label"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-col :span="24">
        <el-form-item label="公告标题" prop="title">
          <el-input
            :disabled="infoShow"
            v-model="form.title"
            placeholder="请输入公告标题"
          />
        </el-form-item>
      </el-col>

      <el-col :span="24">
        <el-form-item
          :label="form.infoType != 2 ? '内容' : '视频'"
          prop="content"
        >
          <editor
            v-show="form.infoType != 2"
            :readOnly="infoShow"
            v-model="text"
            :min-height="192"
          />
          <div v-show="form.infoType == 2">
            <el-upload
              accept=".mp4"
              action=""
             :http-request="uploadObs"
              list-type="picture-card"
              :show-file-list="false"
              :disabled="infoShow"

            >
              <i class="el-icon-plus" v-show="dialogImageUrl == ''"></i>
              <video
                width="146px"
                height="146px"
                v-show="dialogImageUrl != ''"
                :src="dialogImageUrl"
                controls="controls"
              ></video>
            </el-upload>
            <span class="box-delete" v-show="!infoShow"
              ><i
                class="el-icon-delete"
                v-show="dialogImageUrl != ''"
                @click="handleRemove"
              ></i
            ></span>
          </div>
        </el-form-item>
      </el-col>

      <el-col :span="24">
        <el-form-item label="封面图片" prop="image">
          <image-upload
            :disabled="infoShow"
            v-model="form.image"
            :limit="1"
          ></image-upload>
        </el-form-item>
      </el-col>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitForm" v-show="!infoShow"
        >确 定</el-button
      >
      <el-button @click="cancel">取 消</el-button>
    </div>
  </div>
</template>

<script>
import { addInformation, updateInformation } from "@/api/content/information";
import { basicPath2 } from "@/api/base.js";
import { getToken } from "@/utils/auth";
import { selectObsData } from "@/api/system/appupgrade";

import ObsClient from 'esdk-obs-browserjs/src/obs';
// import {getPicPath} from '@/utils/getImgUrl.js'

export default {
  name: "informationIndexEditAndAdd",
  dicts: ["banner_product_name", "info_type", "info_module", "info_tag"],
  data() {
    return {
      disabled: false,
      form: {},
      text: "",
      uploadImgUrl:
        process.env.VUE_APP_BASE_API + `${basicPath2}files/obs/fileUpload`, // 上传的图片服务器地址
      headers: {
        Authorization: getToken(),
      },
      dialogImageUrl: "",
      // 表单校验
      rules: {
        title: [{ required: true, message: "标题不能为空", trigger: "blur" }],
        product: [
          { required: true, message: "产品名称不能为空", trigger: "blur" },
        ],
        infoModule: [
          { required: true, message: "资讯模块不能为空", trigger: "blur" },
        ],
        tag: [{ required: true, message: "资讯标签不能为空", trigger: "blur" }],
        infoType: [
          { required: true, message: "资讯类型不能为空", trigger: "blur" },
        ],
        content: [
          { required: true, message: "资讯内容(视频)不能为空", trigger: "blur" },
        ],
        image: [{ required: true, message: "图片不能为空", trigger: "blur" }],
      },
    };
  },
  props: {
    // 是否禁用输入，查看详情的时候禁用
    infoShow: {
      type: [Boolean],
      default: false,
    },
  },
  methods: {
    onchang() {
      this.disabled = false;
      if (this.form.tag.includes("技术")) {
        this.form.infoType = "2";
        this.disabled = true;
      }

      return;
    },
    handleRemove() {
      this.dialogImageUrl = "";
      this.form.content = this.dialogImageUrl;
    },
    uploadObs(params) {
        let that=this
        this.$modal.loading("正在上传视频，请稍候...");
      selectObsData().then((response) => {
        let obsInfo = response.result;
        // 创建ObsClient实例
        var obsClient = new ObsClient({
          access_key_id: obsInfo.ak, // 你的ak
          secret_access_key: obsInfo.sk, // 你的sk
          server: obsInfo.sslMode + "://" + obsInfo.endpoint // 你的endPoint
        });
        let date=new Date()
        that.fileName=params.file.name
        obsClient.putObject({
          Bucket: obsInfo.bucketName, // 桶名
          Key:  obsInfo.active +`/${date.getFullYear()}/${date.getMonth() +1 }/` + params.file.name, // 路径 + 文件名
          SourceFile: params.file,
          ProgressCallback: this.callback
        }, function (err, result) {
           
          if (err) {
            that.dialogImageUrl=null
            that.$message.error('文件上传失败')
           
          } else {
          
            that.dialogImageUrl = obsInfo.sslMode + "://" + obsInfo.bucketName + "." + obsInfo.endpoint + "/" + obsInfo.active +`/${date.getFullYear()}/${date.getMonth() +1 }/` + params.file.name;
          }
          that.$modal.closeLoading();
        })

      });
    },


    /** 提交按钮 */
    submitForm: function () {
      if (this.form.infoType != 2) {
           this.$set(this.form, 'content', this.text)
        console.log(this.form.content,"图文");
      } else {
        this.$set(this.form, 'content', this.dialogImageUrl)
        console.log(this.form.content, "视频");
      }
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form?.infoId) {
            updateInformation(this.form).then((response) => {
              if (response.code == 200) {
                this.$emit("close");
                this.$modal.msgSuccess("修改成功");
              }
            });
          } else {
            addInformation(this.form).then((response) => {
              if (response.code == 200) {
                this.$modal.msgSuccess("新增成功");
                this.$emit("close");
              }
            });
          }
        }
      });
    },
    clear() {
      this.$refs.form.clearValidate();
    },
    // 取消按钮
    cancel() {
      this.disabled = false;
      this.clear();
      this.$emit("cancel");
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  position: relative;
  &-delete {
    font-size: 30px;
    top: 44px;
    left: 55px;
    position: absolute;
  }
}
</style>>

