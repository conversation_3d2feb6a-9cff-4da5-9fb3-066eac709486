<template>
  <div class="app-container">
    <el-card class="mb10 form_box" shadow="never" ref="formBox">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
        <el-row class="form_row">
          <el-col class="form_col">
            <el-form-item label="选择产品" prop="product">
              <el-select v-model="queryParams.product" placeholder="产品类型" clearable>
                <el-option v-for="dict in dict.type.banner_product_name" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>

            <el-form-item label="资讯类型" prop="infoType">
              <el-select v-model="queryParams.infoType" placeholder="资讯类型" clearable>
                <el-option v-for="dict in dict.type.info_type" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>

            <el-form-item label="资讯模块" prop="infoModule">
              <el-select v-model="queryParams.infoModule" placeholder="选择模块" clearable>
                <el-option v-for="dict in dict.type.info_module" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>

            <el-form-item label="资讯标签" prop="tag">
              <el-select v-model="queryParams.tag" placeholder="选择标签" clearable>
                <el-option v-for="dict in dict.type.info_tag" :key="dict.value" :label="dict.label"
                  :value="dict.label" />
              </el-select>
            </el-form-item>

            <el-form-item label="发布时间" prop="dateRange">
              <el-date-picker v-model="queryParams.dateRange" style="width: 240px" value-format="yyyy-MM-dd hh:mm:ss"
                type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
            </el-form-item>

            <el-form-item label="资讯标题" prop="title">
              <el-input v-model="queryParams.title" placeholder="请输入资讯标题搜索" clearable
                @keyup.enter.native="handleQuery" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              <template v-if="toggleSearchDom">
                <el-button type="text" @click="packUp">
                  {{ toggleSearchStatus ? '收起' : '展开' }}
                  <i :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"></i>
                </el-button>
              </template>


            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>




    <el-card shadow="never">
      <el-row class="mb8 form_btn">
        <el-col class="form_btn_col">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
        </el-col>
      </el-row>

      <el-table v-loading="loading" style="width: 100%" :data="noticeList" stripe  :height="tableHeight" border>
        <el-table-column label="序号" align="center" type="index" min-width="100" />
        <el-table-column label="产品名称" align="center" prop="product"  min-width="130"  :show-overflow-tooltip="true">
          <template slot-scope="scope">
            {{ handProduct(scope.row.product, productType) }}
          </template>
        </el-table-column>
        <el-table-column label="资讯模块" align="center" prop="infoModule" min-width="150">
          <template slot-scope="scope">
            {{ handProduct(scope.row.infoModule, newsModule) }}
          </template>
        </el-table-column>
        <el-table-column label="资讯类型" align="center" prop="infoType" min-width="100" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            {{ handProduct(scope.row.infoType, newsType) }}
          </template>
        </el-table-column>
        <el-table-column label="标题" align="left" prop="title" :show-overflow-tooltip="true" min-width='150'/>

        <el-table-column label="资讯标签" align="center" :show-overflow-tooltip="true" min-width="150" prop="tag">
        </el-table-column>

        <el-table-column label="发布时间" align="center" prop="publishTime" min-width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="180">
          <template slot-scope="scope">
            <!-- v-hasPermi="['content:information:eait']" -->
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">编辑</el-button>
            <el-popconfirm style="margin-left: 8px" confirm-button-text="确定" cancel-button-text="取消" icon="el-icon-info"
              icon-color="red" title="确定删除吗？" @confirm="handleDelete(scope.row)">
              <el-button slot="reference" icon="el-icon-delete" size="mini" type="text">删除</el-button>
            </el-popconfirm>

            <el-button size="mini" type="text" icon="el-icon-warning-outline"
              @click="handleInfo(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" />
    </el-card>
    <!-- 列表 end -->

    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="780px" append-to-body
      @close="cancel">
      <edit-and-add ref="refModel" @cancel="cancel" @close="close" :infoShow="infoShow"></edit-and-add>
    </el-dialog>
    <!-- 添加或修改或详情公告对话框  end -->
  </div>
</template>

<script>
  import {
    listInformation,
    getInformation,
    delInformation,
  } from "@/api/content/information";
  import editAndAdd from "./components/editAndAdd.vue";
  import ImageUpload from "@/components/ImageUpload";
  import {
    repage
  } from "@/utils/repage.js"
  import {
    tableUi
  } from "@/utils/mixin/tableUi.js";
  export default {
    components: {
      ImageUpload,
      editAndAdd
    },
    name: "informationIndex",
    mixins: [tableUi],
    dicts: ["banner_product_name", "info_type", "info_module", "info_tag"],
    data() {
      return {
        // 遮罩层
        loading: true,

        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 公告表格数据
        noticeList: [],
        // 弹出层标题
        title: "",
        // 是否显示弹出层
        open: false,
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          dateRange: undefined,
          tag: undefined,
          infoModule: undefined,
          product: undefined,
          infoType: undefined,
        },
        // 表单当前操作项ID
        rowData: {},
        // 是否禁用输入，查看详情的时候禁用
        infoShow: false,
        productType: [],
        newsType: [],
        newsModule: [],
      };
    },
    computed: {
      handProduct() {
        return function(val, data) {
          let dictLabel = "";
          data.forEach((item) => {
            if (val == item.dictValue) {
              dictLabel = item.dictLabel;
            }
          });
          return dictLabel;
        };
      },
    },
    created() {
      this.getDictionary("banner_product_name", "productType");
      this.getDictionary("info_type", "newsType");
      this.getDictionary("info_module", "newsModule");
      this.getList();
    },
    methods: {
      //获取数据字典数据
      getDictionary(type, value) {
        this.getDicts(type).then((res) => {
          this[value] = res.data;
        });
      },
      /** 查询公告列表 */
      getList() {
        this.loading = true;
        let form = {
          ...this.queryParams
        };
        delete form.dateRange;
        listInformation(form).then((res) => {
          this.loading = false;
          if (res.code == 200) {
            this.noticeList = res.result.list;
            this.total = Number(res.result.total);
          }
        });
      },

      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.startTime = ''
        this.queryParams.endTime = ''
        if (this.queryParams?.dateRange) {
          this.queryParams.startTime = this.queryParams.dateRange[0];
          this.queryParams.endTime = this.queryParams.dateRange[1];
        }
        this.queryParams.pageNum = 1;
        this.getList();
      },

      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm("queryForm");
        this.handleQuery();
      },
      close() {
        this.cancel();
        this.getList();
      },
      // 关闭弹窗取消按钮
      cancel() {
        this.$refs.refModel.form = {};
        this.$refs.refModel.disabled = false
        this.$refs.refModel.text = ''
        this.$refs.refModel.dialogImageUrl = "";
        this.open = false;
        this.$refs.refModel.clear();
      },

      /** 新增按钮操作 */
      handleAdd() {
        this.infoShow = false;
        this.open = true;
        this.title = "添加资讯";
      },

      /** 详情按钮操作 */
      handleInfo(row) {
        this.infoShow = true;
        this.title = "资讯详情";
        this.getTbaleOneData(row);
      },

      /** 修改按钮操作 */
      handleUpdate(row) {
        this.infoShow = false;
        this.title = "修改资讯";
        this.getTbaleOneData(row);
      },

      /** 删除按钮操作 */
      handleDelete(row) {
        delInformation({
          infoId: `${row.infoId}`
        }).then((res) => {
          if (res.code == 200) {
            this.$modal.msgSuccess("删除成功");
            //重新分页计算
            this.queryParams.pageNum = repage(this.queryParams.pageSize, this.queryParams.pageNum, this.total)
            this.getList()
          }
        });
      },

      /** 查询按钮读取一条数据 */
      getTbaleOneData(row) {
        this.open = true;
        getInformation({
          infoId: ` ${row.infoId}`
        }).then((res) => {
          if ((res.code = 200)) {
            console.log(res.result);
            res.result.infoModule = res.result.infoModule.toString();
            res.result.infoType = res.result.infoType.toString();
            res.result.product = res.result.product.toString();
            this.$refs.refModel.form = {
              ...res.result
            };
            this.$refs.refModel.text = res.result.content;
            if (this.$refs.refModel.form.infoType == 2) {
              this.$refs.refModel.dialogImageUrl = this.$refs.refModel.form.content
            }
          }
        });
      },
    },
  };
</script>
