<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          >新增</el-button
        >
      </el-col>
    </el-row>
    <el-table v-loading="loading" :data="list" style="width: 100%">
      <el-table-column label="录入日期" align="center" prop="sortNumber"></el-table-column>
      <el-table-column label="省份" align="center" prop="bannerName" />
      <el-table-column label="活畜类别" align="center" prop="sortNumber"></el-table-column>
      <el-table-column label="今日交易价格" align="center" prop="sortNumber" />
      <el-table-column label="今日收购价格" align="center" prop="sortNumber" />
      <el-table-column label="操作" slot-scope="scope">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            >编辑</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            >删除</el-button
          >
        </template>
      </el-table-column>

    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 添加或修改banner配置对话框 -->
    <operating-form
      ref="openFrom"
      v-model="number"
      @refresh="refreshList"
    ></operating-form>
  </div>
</template>

<script>
import operatingForm from "./components/operatingForm";

export default {
  components: {
    operatingForm,
  },
  data() {
    return {
      imgList: [],
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
      disabled: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
    };
  },
  created() {
    
  },
  computed: {
   /*  coPosition() {
      return function (val) {
        let dictLabel = "";
        this.positions.forEach((item) => {
          if (val == item.dictValue) {
            dictLabel = item.dictLabel;
          }
        });
        return dictLabel;
      };
    }, */
  },
  methods: {
   
    //刷新页面
    refreshList() {
      this.getList();
    },
    handleDelete(bannerId) {
      operatingDelte({ bannerId: `${bannerId}` }).then((res) => {
        if (res.code == 200) {
            this.queryParams.pageNum = repage(this.queryParams.pageSize,this.queryParams.pageNum,this.total)
        //   this.page();
          this.getList();
          this.$message({
            message: "删除成功",
            type: "success",
          });
        }
      });
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      let form = { ...this.queryParams };
      delete form.dateRange;
      operatingList(form)
        .then((res) => {
          this.loading = false;
          if (res.code == 200) {
            this.list = res.result.list;
            this.total = Number(res.result.total);
          } else {
            this.$message.error(res.message);
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    /**新增 */
    handleAdd() {
      this.$refs.openFrom.title = "新增";
      this.$refs.openFrom.open = true;
    },
  },
};
</script>
<style scoped>
</style>

