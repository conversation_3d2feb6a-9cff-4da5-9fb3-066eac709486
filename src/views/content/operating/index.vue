<template>
  <div class="app-container">
    <el-card class="mb10 form_box" shadow="never" ref="formBox">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="100px">
        <el-row class="form_row">
          <el-col class="form_col">
            <el-form-item label="选择产品" prop="product">
              <el-select v-model="queryParams.product" placeholder="产品类型" clearable>
                <el-option v-for="dict in dict.type.banner_product_name" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="图片位置：" prop="position">
              <el-select v-model="queryParams.position" placeholder="请选择" clearable>
                <el-option v-for="dict in positions" :key="dict.dictValue" :label="dict.dictLabel"
                  :value="dict.dictValue" />
              </el-select>
            </el-form-item>
            <el-form-item label="图片状态：" prop="status">
              <el-select v-model="queryParams.status" placeholder="请选择">
                <el-option label="待上架" :value="0"></el-option>
                <el-option label="上架中" :value="1"></el-option>
                <el-option label="已上架" :value="2"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="创建时间：" prop="dateRange">
              <el-date-picker v-model="queryParams.dateRange" style="width: 240px" value-format="yyyy-MM-dd hh:mm:ss"
                type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              <template v-if="toggleSearchDom">
                <el-button type="text" @click="packUp">
                  {{ toggleSearchStatus ? '收起' : '展开' }}
                  <i :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"></i>
                </el-button>
              </template>

            </el-form-item>

          </el-col>
        </el-row>
      </el-form>
    </el-card>


    <el-card shadow="never">
      <el-row class="mb8 form_btn">
        <el-col class="form_btn_col">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
        </el-col>
      </el-row>

      <el-table v-loading="loading" :data="list" style="width: 100%" stripe  :height="tableHeight" border>
        <el-table-column label="产品名称" align="left" width="150">
          <template slot-scope="scope">
            {{ coProductName(scope.row.product) }}
          </template>
        </el-table-column>
        <el-table-column label="图片名称" align="center" prop="bannerName" min-width="150" />
        <el-table-column label="图片位置" align="center" min-width="150">
          <template slot-scope="scope">
            {{ coPosition(scope.row.position) }}
          </template>
        </el-table-column>
        <el-table-column label="点击跳转类型" align="center" width="150">
          <template slot-scope="scope">
            {{
              scope.row.linkType == 0
                ? "不跳转"
                : scope.row.linkType == 1
                ? "内部跳转"
                : "外部跳转"
            }}
          </template>
        </el-table-column>
        <el-table-column label="权重" align="right" header-align="center" prop="sortNumber" width="100" />
        <el-table-column label="图片" align="center" min-width="180" show-overflow-tooltip>
          <template slot-scope="scope">
            <div style="cursor: pointer" v-show="scope.row.image != ''" @click="bigImg(scope.row.image)">
              点击图片预览
            </div>
            <el-image-viewer v-if="showViewer" :on-close="
                () => {
                  showViewer = false;
                }
              " :url-list="imgList" />
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" width="100">
          <template slot-scope="scope">
            {{
              scope.row.status == 0
                ? "待上架"
                : scope.row.status == 1
                ? "上架中"
                : "已上架"
            }}
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="180"/>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="180">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="handDetails(scope.row.bannerId)"
              icon="el-icon-warning-outline">详情</el-button>
            <el-button size="mini" type="text" icon="el-icon-edit" @click="edit(scope.row)">编辑</el-button>
            <el-popconfirm style="margin-left: 8px" confirm-button-text="确定" cancel-button-text="取消" icon="el-icon-info"
              icon-color="red" title="确定删除吗？" @confirm="handleDelete(scope.row.bannerId)">
              <el-button slot="reference" icon="el-icon-delete" size="mini" type="text">删除</el-button>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" />
    </el-card>

    <!-- 添加或修改banner配置对话框 -->
    <operating-form ref="openFrom" v-model="number" @refresh="refreshList"></operating-form>
  </div>
</template>

<script>
  import {
    operatingList,
    operatingDelte
  } from "@/api/content/operating";
  import {
    getDicts
  } from "@/api/system/dict/data.js";
  import operatingForm from "./components/operatingForm";
  import ElImageViewer from "element-ui/packages/image/src/image-viewer";
  import {
    repage
  } from "@/utils/repage.js"
  import {
    tableUi
  } from "@/utils/mixin/tableUi.js";
  export default {
    dicts: ["banner_product_name", "banner_position"],
    components: {
      operatingForm,
      ElImageViewer,
    },
     mixins: [tableUi],
    data() {
      return {
        imgList: [],
        showViewer: false,
        number: "",
        // 遮罩层
        loading: true,
        // 总条数
        total: 0,
        // 表格数据
        list: [],
        pageNum: 1,
        pageSize: 10,
        disabled: false,
        //产品名称
        productNames: [],
        //图片位置
        positions: [],
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          product: undefined,
          position: undefined,
          status: undefined,
          dateRange: undefined,
        },
      };
    },
    created() {
      this.getList();
      this.getDict("banner_product_name").then((res) => {
        this.productNames = res || [];
      });
      this.getDict("banner_position").then((res) => {
        this.positions = res || [];
      });
    },
    computed: {
      coPosition() {
        return function(val) {
          let dictLabel = "";
          this.positions.forEach((item) => {
            if (val == item.dictValue) {
              dictLabel = item.dictLabel;
            }
          });
          return dictLabel;
        };
      },
      coProductName() {
        let dictLabel = "";
        return function(val) {
          this.productNames.forEach((item) => {
            if (item.dictValue == val) {
              dictLabel = item.dictLabel;
            }
          });
          return dictLabel;
        };
      },
    },
    methods: {
      //请求数据字典数据
      async getDict(url) {
        let res = await getDicts(url);
        return res.data || [];
      },
      bigImg(url) {
        this.imgList = [];
        this.imgList.push(url);
        this.showViewer = true;
      },
      //刷新页面
      refreshList() {
        this.getList();
      },
      handleDelete(bannerId) {
        operatingDelte({
          bannerId: `${bannerId}`
        }).then((res) => {
          if (res.code == 200) {
            this.queryParams.pageNum = repage(this.queryParams.pageSize, this.queryParams.pageNum, this.total)
            //   this.page();
            this.getList();
            this.$message({
              message: "删除成功",
              type: "success",
            });
          }
        });
      },
      //重新分页计算
      // page() {
      //   let countPage = Math.ceil((this.total - 1) / this.queryParams.pageSize);
      //   let currentPage =
      //     this.queryParams.pageNum > countPage
      //       ? countPage
      //       : this.queryParams.pageNum;
      //   this.queryParams.pageNum = currentPage < 1 ? 1 : currentPage;
      // },
      /** 查询列表 */
      getList() {
        this.loading = true;
        let form = {
          ...this.queryParams
        };
        delete form.dateRange;
        operatingList(form)
          .then((res) => {
            this.loading = false;
            if (res.code == 200) {
              this.list = res.result.list;
              this.total = Number(res.result.total);
            } else {
              this.$message.error(res.message);
            }
          })
          .catch(() => {
            this.loading = false;
          });
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.startTime = ''
        this.queryParams.endTime = ''
        if (this.queryParams?.dateRange) {
          this.queryParams.startTime = this.queryParams.dateRange[0];
          this.queryParams.endTime = this.queryParams.dateRange[1];
        }
        this.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm("queryForm");
        this.handleQuery();
      },

      /**新增 */
      handleAdd() {
        this.$refs.openFrom.title = "新增";
        this.$refs.openFrom.open = true;
      },
      //详情
      handDetails(id) {
        this.$refs.openFrom.showClose = true;
        this.$refs.openFrom.title = "详情";
        this.$refs.openFrom.disabled = true;
        this.$refs.openFrom.open = true;
        this.$refs.openFrom.details(id);
      },

      //编辑
      edit(rowData) {
        this.$refs.openFrom.disabled = false;
        rowData.product = rowData.product.toString();
        rowData.position = rowData.position.toString();
        rowData.status = rowData.status.toString();
        rowData.sortNumber = Number(rowData.sortNumber)
        this.$refs.openFrom.form = {
          ...rowData
        };
        this.$refs.openFrom.title = "编辑";
        this.$refs.openFrom.open = true;
      },
    },
  };
</script>
<style scoped>
</style>
