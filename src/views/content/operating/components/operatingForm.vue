<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="40%"
      append-to-body
      :center="true"
      @close="closeDialog"
      :close-on-click-modal="false"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-form-item label="选择产品" prop="product">
            <el-select
              :disabled="disabled"
              class="inputWidth"
              v-model="form.product"
              placeholder="产品类型"
              clearable
            >
              <el-option
                v-for="dict in dict.type.banner_product_name"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="图片位置" prop="position">
            <el-select
              :disabled="disabled"
              class="inputWidth"
              v-model="form.position"
              placeholder="图片位置"
              clearable
            >
              <el-option
                v-for="dict in dict.type.banner_position"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="图片名称：" prop="bannerName">
            <el-input
              v-model="form.bannerName"
              placeholder="请输入图片名称"
              maxlength="30"
              :disabled="disabled"
            />
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="图片权重：" prop="sortNumber">
            <el-input
              v-model.number="form.sortNumber"
              placeholder="请输入权重"
              :disabled="disabled"
            />
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="点击跳转类型：" prop="linkType">
            <el-select
              class="inputWidth"
              v-model="form.linkType"
              placeholder="请选择"
              :disabled="disabled"
            >
              <el-option label="不跳转" :value="0"></el-option>
              <el-option label="内部跳转" :value="1"></el-option>
              <el-option label="外部跳转" :value="2"></el-option>
            </el-select>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="上传图片：" prop="image">
            <image-upload
              :disabled="disabled"
              v-model="form.image"
              :limit="1"
            ></image-upload>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="跳转链接：" prop="linkAddress">
            <el-input
              v-model="form.linkAddress"
              placeholder="请输入"
              :disabled="disabled"
            />
          </el-form-item>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="状态：" prop="status">
              <el-radio-group v-model="form.status" :disabled="disabled">
                <el-radio label="1">上架中</el-radio>
                <el-radio label="0">待上架</el-radio>
                <el-radio label="2">已上架</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" v-show="!showClose">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  operatingDetails,
  operatingAdd,
  operatingEdit,
} from "@/api/content/operating";
export default {
  dicts: ["banner_product_name", "banner_position"],

  props: ["value"],
  data() {
    return {
      open: false,
      form: {},
      title: "",
      disabled: false,
      disabled: false,
      showClose: false,
       // 表单校验
      rules: {
        product: [
          { required: true, message: "产品名称不能为空", trigger: "blur" },
        ],
          position: [
          { required: true, message: "图片位置不能为空", trigger: "blur" },
        ],
          bannerName: [
          { required: true, message: "图片名称不能为空", trigger: "blur" },
        ],
         image: [
          { required: true, message: "图片不能为空", trigger: "blur" },
        ],
         status: [
          { required: true, message: "状态不能为空", trigger: "blur" },
        ],
        sortNumber: [
          { required: true, message: "图片权重不能为空", trigger: "blur" },
          { type: 'number', message: '权重必须为数字值' }
        ],
      },
    };
  },

  mounted() {},

  methods: {
    closeDialog() {
      this.showClose = false;
      this.form = {};
      this.disabled = false;
       this.$refs.form.clearValidate()
    },
    //弹框取消
    cancel() {
      this.open = false;
      this.closeDialog();
    },
    //查看详情
    details(id) {
      operatingDetails({ bannerId: `${id}` }).then((res) => {
        if (res.code == 200) {
           res.result.position = res.result.position.toString()
          res.result.product = res.result.product.toString()
          res.result.status = res.result.status.toString()
          res.result.sortNumber = Number(res.result.sortNumber)
          this.form = res.result;
        }
      });
    },
    //新增
    add(form) {
      operatingAdd(form).then((res) => {

        if (res.code == 200) {
          this.cancel();
          this.$emit("refresh");
          this.$message({
            message: "新增成功",
            type: "success",
          });
        }
      });
    },
    //编辑
    edit(form) {
      operatingEdit(form).then((res) => {
        if (res.code == 200) {
          this.cancel();
          this.$emit("refresh");
          this.$message({
            message: "修改成功",
            type: "success",
          });
        }
      });
    },
    //表单提交
    submitForm() {
        console.log(this.form,"000000");
      this.$refs["form"].validate((valid) => {
        this.form.bannerModule = 1;
        if (valid) {
          if (this.form?.bannerId) {
            this.edit(this.form);
          } else {
            this.add(this.form);
          }
        } else {
          return false;
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.inputWidth {
  width: 100% !important;
}
</style>