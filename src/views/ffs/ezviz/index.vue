<template>
    <div class="app-container">
      <el-card>
        <div slot="header" class="clearfix"></div>
        <el-row :gutter="10" class="mb8">
          <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
            <el-form-item label="所属用户姓名" prop="userName">
              <el-input v-model.trim="queryParams.userName" placeholder="请输入所属用户姓名" clearable />
            </el-form-item>
            <el-form-item label="所属用户手机号码" prop="userPhone">
              <el-input v-model.trim="queryParams.userPhone" placeholder="请输入所属用户手机号码" clearable />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-row>
        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="addFrom">新增</el-button>
        </el-col>
        <!-- 表格数据 -->
        <el-table :data="tableData" stripe style="width: 100%" v-loading="loading">
          <el-table-column type="index" label="序号"></el-table-column>
          <el-table-column prop="userPhone" label="所属用户手机号码" />
          <el-table-column prop="userName" label="所属用户姓名"></el-table-column>
          <el-table-column prop="appKey" label="appKey"></el-table-column>
          <el-table-column prop="appSecret" label="appSecret"></el-table-column>
          <el-table-column label="操作" align="center">
            <template slot-scope="scope">
              <el-button @click="goEdit(scope.row.ezvizId)" type="text">编辑</el-button>
              <el-button @click="delInfo(scope.row.ezvizId)" type="text">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
      <modelForm v-if="dialogAdd.open" :dialogAdd="dialogAdd" @close="close" @refresh="refresh" ></modelForm>
    </div>
  </template>
    
    <script>
 import {getEzvizList,deleteById} from "@/api/ffs/ezviz.js"
 import modelForm from "./components/modelForm.vue"
  export default {
    components:{
        modelForm
    },
    data() {
      return {
   
        //新增
        dialogAdd: {
          open: false,
          title: "",
          id: "",
        },
        queryParams: {
            userName:'',
            userPhone:'',
          pageNum: 1,
          pageSize: 10,
        },
        loading: true,
        total: 0,
        tableData: [],
      };
    },
   
    created() {
      this.getList();
    },
  
    methods: {
 
      //列表查询
      getList() {
        getEzvizList(this.queryParams).then((res) => {
          if (res.code == 200) {
            this.tableData = res.result.list;
            this.total = Number(res.result.total);
            this.loading = false;
          }
        });
      },
      reset() {
        this.resetForm("queryForm");
      },
      close() {
        this.dialogAdd.open = false;
        this.dialogAdd.id = "";
      },
      //重置
      resetQuery() {
        this.reset();
        this.handleQuery();
      },
      //刷新页面
      refresh() {
        
        this.getList();
      },
      //搜索
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
 
      //创建
      addFrom() {
        this.dialogAdd.open = true;
        this.dialogAdd.title = "新增";
        this.dialogAdd.id=null
      },
      //编辑
      goEdit(id) {
        this.dialogAdd.open = true;
        this.dialogAdd.title = "编辑";
        this.dialogAdd.id = id;
      },
      delInfo(id){
        this.$confirm("此操作将永久删除, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
            deleteById({ ids: [id] }).then((res) => {
            if (res.code == 200) {
              this.$message({
                type: "success",
                message: "删除成功",
              });
              this.getList();
            }
          });
        })
      }
    },
  };
  </script>
    
    <style lang="scss" scoped>
  </style>
    