<template>
  <div>
    <el-dialog
      :title="dialogAdd.title"
      :visible.sync="dialogAdd.open"
      width="600px"
      :close-on-click-modal="false"
      @close="close"
      class="fieldList"
      append-to-body
    >
      <el-form
        :model="form"
        :rules="rules"
        ref="ruleForm"
        label-width="135px"
        class="demo-ruleForm"
      >
        <el-form-item label="所属用户手机号码" prop="userPhone">
          <el-select
            class="selectWidth"
            clearable
            v-model="form.userPhone"
            allow-create
            filterable
            remote
            reserve-keyword
            placeholder="请输入所属用户手机号码"
            :remote-method="remoteMethod"
            @change="selectValue"
          >
            <el-option
              v-for="item in options"
              :key="item.phonenumber"
              :label="item.phonenumber"
              :value="item.phonenumber"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属用户姓名" prop="userName">
          <el-input v-model="form.userName" placeholder="请输入所属用户姓名" />
        </el-form-item>
        <el-form-item label="appKey" prop="appKey">
          <el-input v-model="form.appKey" placeholder="请输入appKey" />
        </el-form-item>
        <el-form-item label="appSecret" prop="appSecret">
          <el-input v-model="form.appSecret" placeholder="请输入appSecret" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="close">关闭</el-button>
        <el-button type="primary" @click="submitForm">提交</el-button>
      </span>
    </el-dialog>
  </div>
</template>
  
  <script>
import { listSimpleUser } from "@/api/system/user";
import { addEzviz, byIdInfo, ezvizUpdate } from "@/api/ffs/ezviz.js";
export default {
  props: {
    dialogAdd: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      form: {
        userPhone: "",
        userId: "",
        userName: "",
        appKey: "",
        appSecret: "",
      },

      options: [],
      rules: {
        userPhone: [
          {
            required: true,
            message: "请填写",
            trigger: "blur",
          },
        ],
        userName: [
          {
            required: true,
            message: "请填写",
            trigger: "blur",
          },
        ],
        appKey: [
          {
            required: true,
            message: "请填写",
            trigger: "blur",
          },
        ],
        appSecret: [
          {
            required: true,
            message: "请填写",
            trigger: "blur",
          },
        ],
      },
    };
  },
  created() {
    this.getInfo();
  },

  methods: {
    getInfo() {
      if (!this.dialogAdd.id) {
        return;
      } else {
        byIdInfo({ ids: [this.dialogAdd.id] }).then((res) => {
          if (res.code == 200) {
            this.form = res.result;
          }
        });
      }
    },
    remoteMethod(value) {
      listSimpleUser({ phonenumber: value }).then((res) => {
        if (res.code == 200) {
          this.options = res.data;
        }
      });
    },
    selectValue() {
      this.form.userId = "";
      this.form.userName = "";
      if (!this.form.userPhone) {
        return;
      }
      listSimpleUser({ phonenumber: this.form.userPhone }).then((res) => {
        if (res.code == 200 && res.data.length > 0) {
          this.form.userId = res.data[0].userId;
          this.form.userName = res.data[0].corprateName
            ? res.data[0].corprateName
            : res.data[0].userName;
        }
      });
    },
    close() {
      this.$emit("close");
    },
    //修改信息
    updataInfo() {
      ezvizUpdate(this.form).then((res) => {
        if (res.code == 200) {
          this.$message({
            type: "success",
            message: "修改成功",
          });
          this.$emit("refresh");
          this.close();
        }
      });
    },
    //添加信息
    addInfo() {
      addEzviz(this.form).then((res) => {
        if (res.code == 200) {
          this.$message({
            type: "success",
            message: "添加成功",
          });
          this.$emit("refresh");
          this.close();
        }
      });
    },
    submitForm() {
      this.$refs["ruleForm"].validate(async (valid) => {
        if (!valid) {
          return;
        }
        if (this.dialogAdd.id) {
          this.updataInfo();
        } else {
          this.addInfo();
        }
      });
    },
  },
};
</script>
  
  <style lang="scss">
.fieldList {
  .el-dialog__header {
    background-color: #f4f4f4;
  }
  .el-dialog__footer {
    text-align: center;
  }
  .selectWidth {
    width: 100%;
  }
}
</style>