<template>
  <div class="app-container">
    <el-card class="mb10 form_box" shadow="never" ref="formBox">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
        <el-row class="form_row">
          <el-col class="form_col">

            <el-form-item label="保险公司名称：" prop="insuranceTenant">
              <el-input v-model="queryParams.insuranceTenant" placeholder="请输入保险公司名称" clearable />
            </el-form-item>
            <el-form-item label="保单产品名称：" prop="insuranceName">
              <el-input v-model="queryParams.insuranceName" placeholder="请输入保单产品名称" clearable />
            </el-form-item>
            <el-form-item label="保单号：" prop="insuranceCode">
              <el-input v-model="queryParams.insuranceCode" placeholder="请输入保单号" clearable />
            </el-form-item>
            <el-form-item label="投保人：" prop="insuredName">
              <el-input v-model="queryParams.insuredName" placeholder="请输入投保人" clearable />
            </el-form-item>
            <el-form-item label="投保人电话：" prop="insuredPhone">
              <el-input v-model="queryParams.insuredPhone" placeholder="请输入投保人电话" clearable />
            </el-form-item>
            <el-form-item label="创建时间：">
              <el-date-picker
                v-model="dateRange"
                style="width: 215px"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </el-form-item>
            <el-form-item label="理赔状态：" prop="claimsStatus">
              <el-select v-model="queryParams.claimsStatus" clearable>
                <el-option label="全部" value />
                <el-option label="理赔审批通过" value="1" />
                <el-option label="理赔审批驳回" value="3" />
                <el-option label="理赔审批中" value="2" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row style="margin-left: 120px;">
          <el-col>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              <template v-if="toggleSearchDom">
                <el-button type="text" @click="packUp">
                  {{ toggleSearchStatus ? '收起' : '展开' }}
                  <i
                    :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"
                  ></i>
                </el-button>
              </template>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <el-card shadow="never" class="list_table">
      <el-row class="mb8 form_btn">
        <el-col class="form_btn_col">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="addData">新增</el-button>
        </el-col>
      </el-row>
      <el-table v-loading="loading" :data="list"  :height="tableHeight" border>
        <el-table-column label="序号" type="index" width="50" align="center"></el-table-column>
        <el-table-column label="理赔单号" prop="claimsCode" width="180" show-overflow-tooltip align="center"></el-table-column>
        <el-table-column label="保单号" prop="insuranceCode" width="180" show-overflow-tooltip align="center"></el-table-column>
        <el-table-column label="保险公司名称" prop="insuranceTenant" width="180" show-overflow-tooltip align="center"></el-table-column>
        <el-table-column label="保险产品名称" prop="insuranceName" width="180" show-overflow-tooltip align="center"></el-table-column>
        <el-table-column label="申请金额" prop="applyAmount" width="180" show-overflow-tooltip align="center"></el-table-column>
        <el-table-column label="状态" prop="claimsStatus"  :formatter="claimsStatus" width="180" show-overflow-tooltip align="center"></el-table-column>
        <el-table-column label="审批意见" prop="auditRemark" width="240" show-overflow-tooltip align="center"></el-table-column>
        <el-table-column label="理赔金额" prop="claimsAmount" width="100" show-overflow-tooltip align="center"></el-table-column>
        <el-table-column label="创建人" prop="createBy" width="180" show-overflow-tooltip align="center"></el-table-column>
        <el-table-column label="创建时间" prop="createTime" width="180" show-overflow-tooltip align="center"></el-table-column>
        <el-table-column label="操作" align="center" width="180" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              @click="handDetails(scope.row.insuranceClaimsId)"
              icon="el-icon-info"
            >详情</el-button>
            <el-button
              size="mini"
              type="text"
              @click="editData(scope.row.insuranceClaimsId)"
              icon="el-icon-info"
            >编辑</el-button>
            <el-button
              size="mini"
              type="text"
              @click="deleteData(scope.row.insuranceClaimsId)"
              icon="el-icon-delete"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    </el-card>

    <details-form
      :detailsFormData="detailsFormData"
      @close="close"
      v-if="detailsFormData.open"
      @refresh="refresh"
    />
    <data-info
      :detailsFormData="detailsInfo"
      @close="close"
      v-if="detailsInfo.open"
      @refresh="refresh"
    />
  </div>
</template>

<script>
import { ffsInsuranceClaimsPage, ffsInsuranceClaimsDelete } from "@/api/ffs/claim.js";
import { tableUi } from "@/utils/mixin/tableUi.js";
import detailsForm from "./components/detailsForm.vue";
import dataInfo from "./components/dataInfo.vue";

export default {
  name: "supervisePartyManageIndex",
  components: {
    detailsForm,
    dataInfo
  },
  mixins: [tableUi],
  data() {
    return {
      detailsFormData: {
        open: false,
        title: "",
      },
      detailsInfo: {
        open: false,
        title: "",
      },
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        insuranceTenant: '', //保险公司名称
        insuranceName: '',
        insuranceCode: '', //保单编号 非必填 默认自动生成
        claimsCode: '', //理赔单号
        insuredName: '', //投保人名称
        insuredPhone: '', //投保人联系电话
        insuredIdcard: '', //投保人身份证号
        claimsStatus: ''
      },
      dateRange: []
    };
  },
  async created() {
    this.getList();
  },
  computed: {
    claimsStatus() {
      return (row, com, val) => {
        if (val == 1) {
          return "理赔审批通过";
        } else if (val == 2) {
          return "理赔审批中";
        } else if (val == 3) {
          return "理赔审批驳回";
        } 
      };
    }
  },
  methods: {
    //刷新页面
    refresh() {
      this.getList();
    },

    /** 查询列表 */
    getList() {
      this.loading = true;
      ffsInsuranceClaimsPage(this.queryParams).then((res) => {
        this.loading = false;
        if (res.code == 200) {
          this.list = res.result.list || [];
          this.total = Number(res.result.total || 0);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      if (this.dateRange && this.dateRange.length > 0) {
        this.queryParams.startTime = this.dateRange[0];
        this.queryParams.endTime = this.dateRange[1];
      } else {
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
      }
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    //关闭弹框
    close() {
      this.detailsFormData.open = false;
      this.detailsInfo.open = false;
    },
    addData() {
      this.detailsFormData.insuranceClaimsId = '';
      this.detailsInfo.disable = false;
      this.detailsFormData.open = true;
      this.detailsFormData.title = '添加理赔单'
    },
    editData(insuranceClaimsId) {
      this.detailsFormData.insuranceClaimsId = insuranceClaimsId;
      this.detailsInfo.disable = false;
      this.detailsFormData.title = '编辑理赔单'
      this.detailsFormData.open = true;
    },
    //详情
    handDetails(insuranceClaimsId) {
      this.detailsInfo.insuranceClaimsId = insuranceClaimsId;
      this.detailsInfo.disable = true;
      this.detailsInfo.title = "理赔单详情";
      this.detailsInfo.open = true;
    },
    deleteData(insuranceClaimsId) {
      this.$confirm("删除后无法恢复，请确认是否删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        ffsInsuranceClaimsDelete({insuranceClaimsId}).then((res) => {
          this.$message.success("删除成功");
          this.getList();
        });
      });
    }
  },
};
</script>
<style scoped lang="scss"></style>

