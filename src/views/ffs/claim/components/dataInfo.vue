<template>
  <div>
    <el-dialog
      :title="detailsFormData.title"
      :visible.sync="detailsFormData.open"
      :close-on-click-modal="false"
      @close="close"
      class="fieldList"
    >
    <div class="dialog_title">保单信息</div>
      <el-descriptions :column="2" :contentStyle="CS" :label-style="LS">
        <el-descriptions-item label="产品名称">{{info.insuranceName}}</el-descriptions-item>
        <el-descriptions-item label="保单号">{{info.insuranceCode}}</el-descriptions-item>
        <el-descriptions-item label="保险公司">{{info.insuranceTenant}}</el-descriptions-item>
        <el-descriptions-item label="保障起期:">{{info.policyStartTime}}</el-descriptions-item>
        <el-descriptions-item label="保障止期">{{info.policyEndTime}}</el-descriptions-item>
        <el-descriptions-item label="保费(元)">{{info.sumInsured}}</el-descriptions-item>
      </el-descriptions>
      <div class="dialog_title">理赔日志</div>
      <div>
        <p v-if="info.claimsStatus == 1 || info.claimsStatus == 3">{{ info.auditTime }} {{ info.auditBy }}{{info.claimsStatus == 1 ? '通过' : '驳回'}}理赔申请</p>
        <p>{{ info.createTime }} {{ info.createBy }}提交理赔申请</p>
      </div>
      <div class="dialog_title">理赔单信息</div>
      <el-descriptions :column="2" :contentStyle="CS" :label-style="LS">
        <el-descriptions-item label="理赔单号">{{info.claimsCode}}({{ claimsStatus(info.claimsStatus) }})</el-descriptions-item>
        <el-descriptions-item label="申请时间">{{info.createTime}}</el-descriptions-item>
        <el-descriptions-item label="保险责任类型">{{ coverageType(info.coverageType) }}</el-descriptions-item>
        <el-descriptions-item label="申请说明">{{info.claimsDescription}}</el-descriptions-item>
        <el-descriptions-item label="申请材料">
          <el-image
            class="qianmad"
            :src="picPath(info.claimsImageUrl)"
            :preview-src-list="srcList"
            @click="bigImage(picPath(info.claimsImageUrl))"
          ></el-image>
        </el-descriptions-item>
        <el-descriptions-item label="申请金额（元）">{{info.applyAmount}}</el-descriptions-item>
        <el-descriptions-item label="理赔金额（元）" v-if="info.claimsStatus == 1">{{info.claimsAmount}}</el-descriptions-item>
      </el-descriptions>
      <div class="dialog_title">投保人信息</div>
      <el-descriptions :column="2" :contentStyle="CS" :label-style="LS">
        <el-descriptions-item label="投保人">{{info.insuredName}}</el-descriptions-item>
        <el-descriptions-item label="投保人电话">{{info.insuredPhone}}</el-descriptions-item>
        <!-- <el-descriptions-item label="保险责任类型">{{coverageType(info.coverageType)}}</el-descriptions-item> -->
        <el-descriptions-item label="身份证照片">
          <el-image
            class="qianmad"
            :src="picPath(info.insuredIdcardFrontUrl)"
            :preview-src-list="srcList"
            @click="bigImage(picPath(info.insuredIdcardFrontUrl))"
          ></el-image>
          <el-image
            class="qianmad"
            :src="picPath(info.insuredIdcardBackUrl)"
            :preview-src-list="srcList"
            @click="bigImage(picPath(info.insuredIdcardBackUrl))"
          ></el-image>
        </el-descriptions-item>
      </el-descriptions>
      <div class="dialog_title">被保险活畜</div>
      <el-table border :data="info.insuranceLivestockList" style="width: 100%; margin: 10px 0 20px 0">
        <el-table-column prop="earTagNo" label="耳标编号" align="center"></el-table-column>
        <el-table-column prop="varietiesName" label="活畜品种" align="center"></el-table-column>
        <el-table-column prop="categoryName" label="活畜类型" align="center"></el-table-column>
      </el-table>
      <div class="dialog_title">收款信息</div>
      <el-descriptions :column="2" :contentStyle="CS" :label-style="LS">
        <el-descriptions-item label="收款账户">{{info.bankAccountName}}</el-descriptions-item>
        <el-descriptions-item label="开户行">{{info.openingBank}}</el-descriptions-item>
        <el-descriptions-item label="银行卡号">{{info.bankAccountNo}}</el-descriptions-item>
        <el-descriptions-item label="银行卡照片">
          <el-image
            class="qianmad"
            :src="picPath(info.bankCardUrl)"
            :preview-src-list="srcList"
            @click="bigImage(picPath(info.bankCardUrl))"
          ></el-image>
        </el-descriptions-item>
      </el-descriptions>
      <span slot="footer" class="dialog-footer" v-if="isDisabled">
        <el-button type="info" @click="close">关闭</el-button>
      </span>
      <el-form :model="form" status-icon :rules="rules" ref="ruleForm" label-width="150px" v-if="!isDisabled">
        <el-form-item label="审核状态：" prop="claimsStatus">
          <el-radio-group v-model="form.claimsStatus">
              <el-radio :label="1">通 过</el-radio>
              <el-radio :label="3">驳 回</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item prop="auditRemark" label="审核意见：">
          <el-input v-model.trim="form.auditRemark" maxlength="150" placeholder="请输入审核意见" :disabled="false" />
        </el-form-item>
        <el-form-item prop="claimsAmount" label="理赔金额（元）">
          <el-input v-model.trim="form.claimsAmount" maxlength="10" placeholder="请输入理赔金额" :disabled="false" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer" v-if="!isDisabled">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="submitForm">提交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { ffsInsuranceClaimsInfo, ffsInsuranceClaimsAudit } from "@/api/ffs/claim.js";
export default {
  name: "inspectionInfodetailsForm",
  props: {
    detailsFormData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      CS: {
        "max-width": "300px", //最小宽度
        "word-break": "break-all", //过长时自动换行
      },
      LS: {
        "word-break": "keep-all",
        color: "#000",
      },
      form: {
          auditRemark: '',
          claimsStatus: '',
          claimsAmount: ''
      },
      rules: {
        claimsStatus: [{ required: true, message: "请选择审核状态", trigger: "change" }],
        auditRemark: [{ required: true, message: "请输入审核意见", trigger: "blur" }],
        claimsAmount: [{ required: true, message: "请输入理赔金额", trigger: "blur" }],
      },
      info: {},
      srcList: [],
      isDisabled: false
    };
  },
  created() {
    this.getDetails();
    if (this.detailsFormData.disable) { this.isDisabled = true; } else { this.isDisabled = false; }
  },
  methods: {
    bigImage(url) {
      this.srcList = [];
      this.srcList.push(url);
    },
    /** 查询巡检记录详情 */
    getDetails() {
      if (!this.detailsFormData.insuranceClaimsId) return;
      ffsInsuranceClaimsInfo({ insuranceClaimsId: this.detailsFormData.insuranceClaimsId }).then((res) => {
        if (res.code == 200) {
          this.info = res.result;
        }
      });
    },
    coverageType(val) {
      if (val == 1) {
        return "疾病死亡责任";
      } else if (val == 2) {
        return "自然灾害责任";
      } else if (val == 3) {
        return "意外事故责任";
      } 
    },
    claimsStatus(val) {
      if (val == 1) {
        return "理赔审批通过";
      } else if (val == 2) {
        return "理赔审批中";
      } else if (val == 3) {
        return "理赔审批驳回";
      } 
    },
    close() {
      this.$emit("close");
    },
    submitForm() {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          ffsInsuranceClaimsAudit({ ...this.form, insuranceClaimsId: this.detailsFormData.insuranceClaimsId }).then(res => {
            if (res.code == 200) {
              this.$message.success("提交成功");
              this.close();
              this.$emit("refresh");
            }
          })
        }
      })
    }
  },
};
</script>

<style lang="scss">
.fieldList {
  .el-dialog__header {
    background-color: #f4f4f4;
  }

  .el-dialog__footer {
    text-align: center;
  }

  .selectWidth {
    width: 100%;
  }
}
.head-title {
  font-size: 16px;
  color: #999;
  margin-bottom: 20px;
}
.qihsd {
  color: red;
}
.qianmad {
  height: 35px;
  margin-top: 0px;
}
.mlt20 {
  margin-left: 20px;
}
.f {
  display: flex;
}
.dialog_title{
  color: #5672FA;
  font-size: 16px;
  font-weight: bold;
  position: relative;
}
.dialog_title::before{
  content: '';
  position: absolute;
  left: -10px;
  top: 3px;
  width: 4px;
  height: 16px;
  background-color: #5672FA;
}
</style>
