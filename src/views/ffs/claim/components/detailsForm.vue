<template>
  <div>
    <el-dialog
      :title="detailsFormData.title"
      :visible.sync="detailsFormData.open"
      width="900px"
      :close-on-click-modal="false"
      @close="close"
      class="fieldList"
    >
      <el-form :model="form" status-icon :rules="rules" ref="ruleForm" label-width="130px">
        <div class="dialog_title">保单信息</div>
        <el-form-item label="保单号：" prop="status">
          <el-select v-model="form.insuranceId" clearable class="selectWidth" :disabled="false" placeholder="请输入保单号"  @change="changeInsurance">
            <el-option v-for="item in insuranceList"  :label="item.insuranceCode" :value="item.insuranceId" :key="item.insuranceId" >
              <div class="flex">
                <span>{{item.insuranceCode}}</span>
                <span>{{item.insuranceTenant}}</span>
                <span>{{item.insuranceName}}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="sumInsured" label="保额（元）：">
          <el-input v-model.trim="form.sumInsured" maxlength="150" placeholder="请输入保额（元）" disabled />
        </el-form-item>
        <el-form-item prop="policyPeriod" label="保障期限（月）：">
          <el-input v-model.trim="form.policyPeriod" maxlength="150" placeholder="请输入保障期限" disabled />
        </el-form-item>
        <div class="dialog_title">理赔申请</div>
        <el-form-item label="被保险活畜：" prop="insuranceLivestockList">
          <el-select v-model="form.insuranceLivestockList" clearable class="selectWidth" multiple :disabled="false">
            <el-option v-for="item in livestockList"  :label="item.earTagNo" :value="item.insuranceLivestockId" :key="item.insuranceLivestockId" >
              <div class="flex">
                <span>{{item.earTagNo}}</span>
                <span>{{item.varietiesName}}</span>
                <span>{{item.categoryName}}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="coverageType" label="保险责任类型：">
          <el-select v-model="form.coverageType" clearable class="selectWidth" :disabled="false" placeholder="请选择保险责任类型">
            <el-option v-for="item in coverageTypeList"  :label="item.label" :value="item.value" :key="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item prop="claimsDescription" label="申请说明：">
          <el-input v-model.trim="form.claimsDescription" maxlength="150" placeholder="请输入申请说明" :disabled="false" />
        </el-form-item>
        <el-form-item prop="applyAmount" label="申请金额：">
          <el-input v-model.trim="form.applyAmount" maxlength="150" placeholder="请输入申请金额" :disabled="false" />
        </el-form-item>
        <el-form-item prop="claimsImageUrl" label="申请材料：">
          <image-upload :disabled="false" v-model="form.claimsImageUrl" :limit="1" :isShowTip="false"></image-upload>
        </el-form-item>
        <div class="dialog_title">理赔基本信息</div>
        <el-form-item prop="bankAccountName" label="收款账户：">
          <el-input v-model.trim="form.bankAccountName" maxlength="20" placeholder="请输入收款账户" disabled />
        </el-form-item>
        <el-form-item prop="openingBank" label="开户行：">
          <el-input v-model.trim="form.openingBank" maxlength="20" placeholder="请输入开户行" disabled />
        </el-form-item>
        <el-form-item prop="bankAccountNo" label="银行卡号：">
          <el-input v-model.trim="form.bankAccountNo" maxlength="20" placeholder="请输入银行卡号" disabled />
        </el-form-item>
        <el-form-item prop="bankCardUrl" label="银行卡照片：">
          <image-upload disabled v-model="form.bankCardUrl" :limit="1"></image-upload>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="submitForm">提交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { ffsInsuranceClaimsAdd, ffsInsuranceClaimsInfo, ffsInsuranceClaimsUpdate } from "@/api/ffs/claim.js";
import { ffsInsuranceList } from "@/api/ffs/insurance.js";
export default {
  name: "addSupplier",
  props: {
    detailsFormData: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      form: {
        coverageType: '', //保险责任类型 1疾病死亡责任 2自然灾害责任 3意外事故责任
        claimsDescription: '', //申请说明
        applyAmount: '', //申请金额（元）
        claimsImageUrl: '', //申请材料url
        insuranceLivestockList: []
      },
      coverageTypeList: [
        { label: "疾病死亡责任", value: 1 },
        { label: "自然灾害责任", value: 2 },
        { label: "意外事故责任", value: 3 },
      ],
      disabled:false,
      rules: {
        coverageType: [{ required: true, message: "请选择保险责任类型", trigger: "change" }],
        claimsDescription: [
          { required: true, message: "请输入申请说明", trigger: "blur" },
        ],
        applyAmount: [
          { required: true, message: "请输入申请金额", trigger: "blur" },
        ],
        claimsImageUrl: [
          { required: true, message: "请上传申请材料", trigger: "change" },
        ],
        insuranceLivestockList: [
          { required: true, message: "请选择被保险活畜", trigger: "change" },
        ],
      }, 
      livestockList: [],
      insuranceList: []
    };
  },
  created() {
    this.getffsInsuranceList()
  },

  methods: {
    close() {
      this.$emit("close");
    },
    getInfo() {
      if (!this.detailsFormData.insuranceClaimsId) return;
      ffsInsuranceClaimsInfo({ insuranceClaimsId: this.detailsFormData.insuranceClaimsId }).then((res) => {
        if (res.code == 200) {
          this.form = res.result;
          this.form.insuranceLivestockList = res.result?.insuranceLivestockList?.map((item) => item.insuranceLivestockId) || [];
          this.changeInsurance(this.form.insuranceId)
        }
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          this.$modal.loading("提交中...");
          const params = {
            insuranceId: this.form.insuranceId, //保单ID
            coverageType: this.form.coverageType, //保险责任类型 1疾病死亡责任 2自然灾害责任 3意外事故责任
            claimsDescription: this.form.claimsDescription, //申请说明
            applyAmount: this.form.applyAmount, //申请金额（元）
            claimsImageUrl: this.form.claimsImageUrl, //申请材料url
          }
          const insuranceLivestockList = []
          this.form.insuranceLivestockList.forEach((item) => {
            insuranceLivestockList.push({
              insuranceLivestockId: item
            })
          })
          if (!this.detailsFormData.insuranceClaimsId) {
            ffsInsuranceClaimsAdd({
              ...params,
              insuranceLivestockList
            })
              .then((response) => {
                this.$modal.closeLoading();
                if (response.code == 200) {
                  this.$modal.msgSuccess("提交成功");
                  this.$emit("refresh");
                  this.$emit("close");
                }
              })
              .catch(() => {
                this.$modal.closeLoading();
              });
          } else {
            ffsInsuranceClaimsUpdate({
              ...params,
              insuranceClaimsId: this.detailsFormData.insuranceClaimsId,
              insuranceLivestockList
            })
              .then((response) => {
                this.$modal.closeLoading();
                if (response.code == 200) {
                  this.$modal.msgSuccess("提交成功");
                  this.$emit("refresh");
                  this.$emit("close");
                }
              })
              .catch(() => {
                this.$modal.closeLoading();
              });
          }
        }
      });
    },
    getffsInsuranceList() {
      ffsInsuranceList({}).then((res) => {
        if (res.code == 200) {
          this.insuranceList = res.result;
          this.getInfo()
        }
      });
    },
    changeInsurance(val) {
      this.insuranceList.forEach((item) => {
        if (item.insuranceId == val) {
          this.form = {
            ...item,
            ...this.form
          }
          this.livestockList = item.insuranceLivestockList;
          item.insuranceLivestockList.forEach((i) => {
            if (i.livestockStatus == 1) {
              this.livestockList.push(i);
            }
          });
        }
      });
    },
  },
};
</script>

<style lang="scss">
.fieldList {
  .el-dialog__header {
    background-color: #f4f4f4;
  }

  .el-dialog__footer {
    text-align: center;
  }

  .selectWidth {
    width: 100%;
  }
}
.flex{
  display: flex;
  span{
    flex: 1;
  }
}
.dialog_title{
  color: #5672FA;
  font-size: 16px;
  font-weight: bold;
  position: relative;
}
.dialog_title::before{
  content: '';
  position: absolute;
  left: -10px;
  top: 3px;
  width: 4px;
  height: 16px;
  background-color: #5672FA;
}
</style>
