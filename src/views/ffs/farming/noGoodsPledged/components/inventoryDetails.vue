<template>
    <div class="app-container">
        <el-card shadow="never">
            <!-- <el-row style="margin-bottom: 20px;">当前库存：{{ info.inventoryWeight || 0 }}kg</el-row> -->
            <el-row :gutter="10">
                <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="90px">

                      <el-form-item label="仓库名称" prop="warehouseId">
                          <el-select v-model="queryParams.warehouseId" placeholder='请选择' clearable  @change="changeWarehouse">
                              <el-option v-for="(item, index) in acidList" :label="item.warehouseName" :value="item.warehouseId" :key="index" />
                          </el-select>
                      </el-form-item>


                      <el-form-item label="库位编号" prop="warehouseLocationId">
                          <el-select v-model="queryParams.warehouseLocationId" placeholder='请选择' clearable no-data-text='请先选择仓库'>
                              <el-option v-for="(item, index) in warehouseLocationList"  :label="item.warehouseLocationCode" :value="item.warehouseLocationId" :key="index" />
                          </el-select>
                      </el-form-item>
                      <el-form-item label="物料名称" prop="materialsTypeId">
                          <el-select v-model="queryParams.materialsTypeId" placeholder='请选择' clearable  @change="changeMaterialsType">
                              <el-option v-for="(item, index) in materialsTypeList" :label="item.materialsTypeName" :value="item.materialsTypeId" :key="index" />
                          </el-select>
                      </el-form-item>
                      <el-form-item label="品种" prop="materialsVarietyId" >
                          <el-select v-model="queryParams.materialsVarietyId" placeholder='请选择' clearable no-data-text='请先选择物料名称'>
                              <el-option v-for="(item, index) in materialsVarietyList" :label="item.materialsVarietyName" :value="item.materialsVarietyId" :key="index" />
                          </el-select>
                      </el-form-item>
                      <el-form-item label="等级" prop="materialsLevelId">
                          <el-select v-model="queryParams.materialsLevelId" placeholder='请选择' clearable>
                              <el-option v-for="(item, index) in materialsLevelList" :label="item.materialsLevelName" :value="item.materialsLevelId" :key="index" />
                          </el-select>
                      </el-form-item>






                    <el-form-item label=" ">
                        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-row>


            <div style="display: flex; justify-content: end; margin-bottom: 10px;">
                <el-button plain icon="el-icon-download" size="mini" @click="handleExportQuot" v-show="total > 0">导出</el-button>
            </div>
            <!-- 表格数据 -->
            <el-table :data="tableData" stripe style="width: 100%" v-loading="loading" :show-summary="true" :summary-method="getSummaries" v-show="activeName != 1" border>
                <el-table-column show-overflow-tooltip type="index" align="center" label="序号" width="60px"></el-table-column>
                <el-table-column show-overflow-tooltip align="center" width="120px" prop="productName" label="产品名称"></el-table-column>
                <el-table-column prop="productCode" align="center" label="产品编码"></el-table-column>
                <el-table-column width="120px" prop="productTypeName" align="center" label="产品类型"></el-table-column>
                <el-table-column width="120px" prop="specification" align="center" label="规格单位"></el-table-column>
                <el-table-column width="120px" prop="inventoryNum" align="right" label="库存数量"></el-table-column>
                <el-table-column width="130px" prop="inventoryWeight" align="right" label="库存重量（kg）"></el-table-column>
                <el-table-column show-overflow-tooltip prop="updateTime" align="center" label="更新时间"></el-table-column>
            </el-table>
            <el-table :data="tableData" stripe style="width: 100%" v-loading="loading" :show-summary="true" :summary-method="getSummaries" v-show="activeName == 1" border>
                 <el-table-column type="index" align="center" width="55" label="序号"></el-table-column>
                                    <el-table-column prop="warehouseName" show-overflow-tooltip min-width="130" align="center" label="仓库名称" />
                                    <el-table-column prop="warehouseLocationCode" show-overflow-tooltip min-width="130" align="center" label="库位编号" />
                                    <el-table-column prop="materialsTypeName" show-overflow-tooltip min-width="130" align="center" label="物料名称" />
                                    <el-table-column prop="materialsVarietyName" show-overflow-tooltip min-width="130" align="center" label="品种" />
                                    <el-table-column prop="materialsLevelName" show-overflow-tooltip min-width="130" align="center" label="等级" />
                                    <el-table-column prop="inventoryWeight" show-overflow-tooltip min-width="180" :sort-method="(a, b) => { return a.inventoryWeight - b.inventoryWeight}" align="right" sortable label="当前库存重量（Kg）" />
                                    <el-table-column prop="inventoryAmount" show-overflow-tooltip min-width="180" :sort-method="(a, b) => { return a.inventoryAmount - b.inventoryAmount}" align="right" sortable label="入库时货值（元）" />

            </el-table>
            <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getPageList" class="pagination-box" />
        </el-card>
    </div>
</template>

<script>
import {
    warehouseListWms,
    productTypeList,
    groupByProduct,
    acidTaskPage,
    groupByProductExport,
    acidTaskExport,
    warehouseExport,
    inventoryList,
    materialsTypeList,
    materialsVarietyList,
    materialsLevelList
} from "@/api/ffs/noGoodsPledged.js";
import { exportExcel } from "@/utils/east";
export default {
    props: {
        info: {
            type: Object,
            default: {}
        },

    },
    data() {
        return {
            acidList: [],
            warehouseLocationList:[],
            materialsTypeList:[],
            materialsVarietyList:[],
            materialsLevelList:[],

            activeName: '1',
            queryParams: {
                warehouseId: "",
                warehouseLocationId: "",
                materialsTypeId: "",
                materialsVarietyId: '',
                materialsLevelId: '',

                pageNum: 1,
                pageSize: 10,
            },

            tableData: [],
            loading: true,
            dateEnter: [],
            dataInfo: {},
            total: 0,
        };
    },
    created() {
        console.log('^^^^^',this.info)
        // this.info.tenantId='1721458312003383297'
        this.handleClick()
        this.getMaterialsTypeList()
        // this.getMaterialsVarietyList()
        this.getMaterialsLevelList()
    },
    methods: {
      changeWarehouse() {
        if(this.queryParams.warehouseId){
          this.acidList.forEach(item => {
              if (this.queryParams.warehouseId === item.warehouseId) {
                  this.warehouseLocationList = item.warehouseLocationList

                  this.queryParams.warehouseLocationId=''
              }
          })
        }else{
          this.warehouseLocationList = []

          this.queryParams.warehouseLocationId=''
        }

      },
      changeMaterialsType() {
        if(this.queryParams.materialsTypeId){
          materialsVarietyList({materialsTypeId:this.queryParams.materialsTypeId,tenantId:this.info.tenantId}).then(res=>{
            if(res.code==200){
              this.materialsVarietyList=res.result
              this.queryParams.materialsVarietyId=''
            }
          })
        }else{
          this.materialsVarietyList=[]
          this.queryParams.materialsVarietyId=''
        }

      },

      getMaterialsTypeList(){
        materialsTypeList({tenantId:this.info.tenantId}).then(res=>{
          if(res.code==200){
            this.materialsTypeList=res.result
          }
        })
      },
      // getMaterialsVarietyList(){
      //   materialsVarietyList({materialsTypeId:this.queryParams.materialsTypeId,tenantId:this.info.tenantId}).then(res=>{
      //     if(res.code==200){
      //       this.materialsVarietyList=res.result
      //     }
      //   })
      // },
      getMaterialsLevelList(){
        materialsLevelList({tenantId:this.info.tenantId}).then(res=>{
          if(res.code==200){
            this.materialsLevelList=res.result
          }
        })
      },
        //导出
        handleExportQuot() {
          exportExcel(warehouseExport,{
                              ...this.queryParams,tenantId:this.info.tenantId
                          },'库存明细')

        },

        getWarehouse(type) {
            warehouseListWms({tenantId:this.info.tenantId  }).then(res => {
                if (res.code == 200) {
                    this.acidList = res.result || []
                }
            })
        },
        // tab切换
        handleClick() {
            this.resetQuery()
                this.getWarehouse(this.activeName)
        },
        //列表查询
        getList() {
            let obj = {
                materialsLevelId: this.queryParams.materialsLevelId,
                materialsTypeId: this.queryParams.materialsTypeId,
                materialsVarietyId: this.queryParams.materialsVarietyId,
                warehouseId: this.queryParams.warehouseId,
                warehouseLocationId: this.queryParams.warehouseLocationId,
                pageNum: this.queryParams.pageNum,
                pageSize: this.queryParams.pageSize,

                tenantId: this.info.tenantId
            }

            inventoryList(obj).then((res) => {
                if (res.code == 200) {
                    this.tableData = res.result.list;
                    this.total = Number(res.result.total);
                    this.loading = false;
                }
            });
        },
        getTaskPage() {
            let obj = {
                butcherCode: this.queryParams.butcherCode,
                warehouseId: this.queryParams.warehouseIdF,
                pageNum: this.queryParams.pageNum,
                pageSize: this.queryParams.pageSize,
                tenantId: this.info.tenantId,
                acidStatus: -4
            }
            if (this.queryParams?.startTime) {
                obj.startTime = this.queryParams.startTime
            }
            if (this.queryParams?.endTime) {
                obj.endTime = this.queryParams.endTime
            }
            acidTaskPage(obj).then(res => {
                if (res.code == 200) {
                    this.tableData = res.result.list;
                    this.total = Number(res.result.total);
                    this.loading = false;
                }
            })
        },
        reset() {
            this.resetForm("queryForm");
        },
        //重置
        resetQuery() {
            this.dateEnter = [];
            this.materialsVarietyList=[]
            this.warehouseLocationList = []
            this.reset();
            this.handleQuery();
        },
        handelData(startTime, endTime, list) {
            if (list?.length > 0) {
                this.queryParams[startTime] = list[0];
                this.queryParams[endTime] = list[1];
            } else {
                delete this.queryParams[startTime];
                delete this.queryParams[endTime];
            }
        },
        getPageList() {
            if (this.activeName == 1) {
                this.getTaskPage()
            } else {
                this.getList();
            }
        },
        //搜索
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.handelData("startTime", "endTime", this.dateEnter);
            this.getList();

        },
        getSummaries(param) {
            const { columns, data } = param;
            const sums = [];
            columns.forEach((column, index) => {
                if (index === 0) {
                    sums[index] = '合计';
                    return;
                }
                const values = data.map(item => Number(item[column.property]));
                if (column.property == 'inWarehouseWeight' || column.property == 'inventoryWeight'||column.property=='outWarehouseWeight') {
                    console.log('zhzihzi：',"zhzihzi");

                    if (!values.every(value => isNaN(value))) {
                        sums[index] = values.reduce((prev, curr) => {
                            const value = Number(curr);
                            if (!isNaN(value)) {
                                return prev + curr;
                            } else {
                                return prev;
                            }
                        }, 0);
                        sums[index] = parseFloat(sums[index]).toFixed(2)
                        sums[index];
                    } else {
                        sums[index] = '';
                    }
                }else{
                    sums[index] = '';
                }
            });

            return sums;
        }
    },
};
</script>

<style lang="scss" scoped></style>
