<template>
    <div class="purveyor_main pb_80">
        <div>
            <div class="fc mb20">
                <span class="point_icon">基础信息</span>
            </div>
            <el-row>
                <el-col :span="20">
                    <el-descriptions :labelStyle='{width: "250px"}' :column="2" size="medium" border>
                        <el-descriptions-item label='出库类型：'>{{ checkOutMethods[dataInfo.checkOutMethod] }}</el-descriptions-item>
                        <el-descriptions-item label='出库人类型：'>{{ supplierTypes[dataInfo.supplierType] }}</el-descriptions-item>
                        <el-descriptions-item label='出库人名称：'>{{ dataInfo.supplierName }}</el-descriptions-item>
                        <el-descriptions-item label='出库人联系电话：'>{{ dataInfo.contactPhone }}</el-descriptions-item>
                    </el-descriptions>
                </el-col>
                <el-col :span="4" class="fcc">
                    <img class="img" v-if="dataInfo.checkOutStatus == 1" src="@/assets/images/daishenhe.png" alt="" srcset="">
                    <img class="img" v-if="dataInfo.checkOutStatus >= 2" src="@/assets/images/yitongguo_icon.png" alt="" srcset="">
                    <img class="img" v-if="dataInfo.checkOutStatus == 0" src="@/assets/images/yibohui.png" alt="" srcset="">
                </el-col>
            </el-row>
            <div class="time-line-main">
                <div class="time-line">
                    <div class="timeline-item currentTime" :class="{
                        currentTime: dataInfo.checkOutStatus >= 1
                    }">
                        <div class="timeline-item-content">
                            <p>提交申请</p>
                            <p>{{dataInfo.createTime}}</p>
                            <p>申请人：{{ dataInfo.createBy }}</p>
                        </div>
                    </div>
                    <div class="timeline" :class="{
                        currentTime: dataInfo.checkOutStatus >= 1
                    }"></div>
                    <div class="timeline-item" :class="{
                        currentTime: dataInfo.checkOutStatus >= 2
                    }">
                        <div class="timeline-item-content">
                            <p>银行审核通过</p>
                            <p>{{ dataInfo.auditBy }}</p>
                            <p>操作人：{{ dataInfo.auditTime }}</p>
                        </div>
                    </div>
                    <div class="timeline" :class="{
                        currentTime: dataInfo.checkOutStatus >= 2
                    }"></div>
                    <div class="timeline-item" :class="{
                        currentTime: dataInfo.checkOutStatus >= 3
                    }">
                        <div class="timeline-item-content">
                            <p>发起出库</p>
                            <p>{{ dataInfo.checkOutTime }}</p>
                            <p>操作人：{{ dataInfo.checkOutBy}}</p>
                        </div>
                    </div>
                    <div class="timeline" :class="{
                        currentTime: dataInfo.checkOutStatus >= 3
                    }"></div>
                    <div class="timeline-item" :class="{
                        currentTime: dataInfo.checkOutStatus == 4
                    }">
                        <div class="timeline-item-content">
                            <p>完成出库</p>
                        </div>
                    </div>
                    <div class="timeline" :class="{
                        currentTime: dataInfo.checkOutStatus == 4
                    }"></div>
                    <div class="timeline-item">
                        <div class="timeline-item-content">
                            <p>打印出库单</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="fc mb20 mt20">
                <span class="point_icon">出库信息</span>
            </div>
            <el-descriptions :contentStyle='{width: "250px"}' :column="3" size="medium" border>
                <el-descriptions-item label='出库申请编号：'>{{ dataInfo.checkOutCode }}</el-descriptions-item>
                <el-descriptions-item label='申请出库重量（Kg）：'>{{ dataInfo.applyWeight }}</el-descriptions-item>
                <el-descriptions-item label='审核出库重量（Kg）：'>{{ dataInfo.auditWeight }}</el-descriptions-item>
            </el-descriptions>

        </div>
    </div>
</template>

<script>


import { inventoryRecordPage } from "@/api/ffs/noGoodsPledged.js";;
export default {
    data() {
        return {
            dialogImageUrl: '',
            dialogVisible: false,
            tableData: [],
            supplierTypes: { 1: '企业', 3 : '个人' },
            checkOutMethods: { 1: '自主出库', 2 : '申请出库' },
        }
    },

    props: {
        dataInfo: Object
    },
    watch: {
        // dataInfo() {
        //     if (this.dataInfo) {
        //         this.getInfo()
        //     }
        // }
    },
    created() {
        // if (this.dataInfo) {
        //     this.getInfo()
        // }
    },
    methods: {
        // getInfo() {
        //     inventoryRecordPage({
        //         pageNum: 1,
        //         pageSize: 1000,
        //         checkOutId: this.dataInfo.checkOutId
        //     }).then(res => {
        //         this.tableData = res.result.list
        //     })
        // },
        resetForm() {
            this.$emit('colse')
        }
    }
}
</script>

<style lang="scss" scoped>
.title{
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
}
.title div {
    font-size: 18px;
}
.el-row {
    font-size: 14px !important;
    .el-col-7{
        margin-top: 20px;
    }
}
.card-title {
  margin-bottom: 15px;
}
.fast {
  width: 8px;
  height: 18px;
  background: #409eff;
  margin-right: 10px;
}
.model {
  width: 100%;
  height: 100%;
  padding: 0 30px;
  &-text {
    color: rgb(102, 102, 102);
  }
  .header {
    &-title {
      font-size: 18px;
    }
  }
}
.greenColor{
    color: rgb(19, 198, 82);
}
.redColor{
    color: rgb(223, 55, 32);
}
.footer_btn{
    width: 80%;
}
.img{
    width: 79px;
    height: 77px;
}
.el-descriptions .el-descriptions__header{
    margin: 0;
}
:deep(.el-descriptions__header) {
    margin-bottom: 0px;
}
img{
    cursor: pointer;
}
.time-line-main{
    height: 150px;
    background: #F5F7FA;
    margin-top: 20px;
    padding-left: 100px;
    padding-top: 37px;
}
.time-line{
    display: flex;
    align-items: center;
    .timeline{
        width: 170px;
        height: 1px;
        background: #C9CDD4;
        margin: 0 3px;
    }
    .timeline-item{
        width: 8px;
        height: 8px;
        background: #C9CDD4;
        border-radius: 100px 100px 100px 100px;
        position: relative;
        .timeline-item-content{
            position: absolute;
            left: 0;
            top: 12px;
            transform: translate(-50%, -0%);
            p{
                width: 140px;
                text-align: center;
                font-size: 14px;
                font-family: Source Han Sans-Regular, Source Han Sans;
                font-weight: 400;
                color: #1F2026;
                line-height: 24px;
                margin: 0;
            }
        }
    }
    .currentTime{
        background: #5672FA;
    }
}
.purveyor_main{
    .el-table__row{
        .el-form-item__content{
            margin: 0 !important;
        }
    }
}

</style>
