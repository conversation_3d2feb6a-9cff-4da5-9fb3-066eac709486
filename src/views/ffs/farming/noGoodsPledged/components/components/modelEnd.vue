<template>
    <div>
      <!--  结算单-->
      <div class="box-card" v-show="activeName==1">
        <div id="print">
          <div class="point_icon">
            <span>结算信息</span>
          </div>
          <el-table
            :data="settleTableData"
            stripe
            style="width: 100%"
            show-summary
            :summary-method="getSummaries"
            border
          >
            <el-table-column type="index" label="序号" align="center" width="60"></el-table-column>
            <el-table-column prop="materialsName" label="原料名称"></el-table-column>
            <el-table-column prop="levelName" label="等级" align="center"></el-table-column>
            <el-table-column prop="weightSection" label="单只重量区间(kg)" align="center" min-width="110px"></el-table-column>
            <el-table-column prop="weightNum" label="数量(只)" align="right"></el-table-column>
            <el-table-column prop="netWeight" label="胴体重量(kg)" align="right" min-width="100px"></el-table-column>
            <el-table-column prop="averageWeight" label="均重(kg)" align="right"></el-table-column>
            <el-table-column prop="unitPrice" label="价格(元/kg)" align="right"></el-table-column>
            <el-table-column prop="weightAmount" label="金额(元)" align="right"></el-table-column>
          </el-table>
          <el-row :gutter="20">
            <el-col :span="8">
              <span>补贴金额(元)：</span>
              <span class="model-text">{{ settleInfo.subsidyAmount }}</span>
            </el-col>
            <el-col :span="8">
              <span>扣款金额(元)：</span>
              <span class="model-text">{{ settleInfo.deductAmount }}</span>
            </el-col>
            <el-col :span="8">
              <span>总计金额(元)：</span>
              <span class="model-text">{{ settleInfo.finalAmount }}</span>
            </el-col>
          </el-row>
          <el-row :gutter="20"></el-row>
          <div class="point_icon">
            <span>账户信息</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="8">
              <span>收款账户：</span>
              <span class="model-text">{{ settleInfo.bankAccountName }}</span>
            </el-col>
            <el-col :span="8">
              <span>开户行：</span>
              <span class="model-text">{{ settleInfo.openingBank }}</span>
            </el-col>
            <el-col :span="8">
              <span>银行卡号：</span>
              <span class="model-text">{{ settleInfo.bankAccountNo }}</span>
            </el-col>
          </el-row>
        </div>
      </div>
      <!-- 检斤单 -->
      <div class="box-card" v-show="activeName==2">
        <div id="printWeigth">
          <div class="point_icon">
            <span>检斤单</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="8">
              <span>屠宰任务编号：</span>
              <span class="model-text">{{ weightInfo.butcherCode }}</span>
            </el-col>
            <el-col :span="8">
              <span>日期：</span>
              <span class="model-text">{{ weightInfo.butcherTime }}</span>
            </el-col>
            <el-col :span="8">
              <span>收购计划编号：</span>
              <span class="model-text">{{ weightInfo.purchasePlanCode }}</span>
            </el-col>
          </el-row>
          <el-row :gutter="20"></el-row>
          <el-table :data="tableData" stripe style="width: 100%" :summary-method="getSummaries">
            <el-table-column label="序号" width="60" align="center">
              <template slot-scope="{row,$index}">{{ row.index=='index'?'合计':$index+1}}</template>
            </el-table-column>
            <el-table-column show-overflow-tooltip prop="materialsName" label="原料名称"></el-table-column>
            <el-table-column show-overflow-tooltip prop="levelName" label="等级"></el-table-column>
            <el-table-column show-overflow-tooltip prop="weightNum" align="right" label="数量"></el-table-column>
            <el-table-column show-overflow-tooltip prop="grossWeight" align="right" label="毛重(kg)"></el-table-column>
            <el-table-column show-overflow-tooltip prop="tareWeight" align="right" label="皮重(kg)"></el-table-column>
            <el-table-column show-overflow-tooltip prop="netWeight" align="right" label="净重(kg)"></el-table-column>
            <el-table-column show-overflow-tooltip prop="averageWeight" align="right" label="均重(kg)"></el-table-column>
            <el-table-column show-overflow-tooltip prop="unitPrice" align="right" label="单价(元)"></el-table-column>
            <el-table-column show-overflow-tooltip prop="weightAmount" align="right" label="金额(元)"></el-table-column>
            <el-table-column show-overflow-tooltip prop="createTime" align="center" label="检斤时间"></el-table-column>
            <el-table-column prop="name" label="检斤图片" align="center">
              <template slot-scope="scope">
                <img v-if="scope.row.weightDetailUrl" style="width: 60px; height: 40px" @click="showImg(scope.row)" :src="scope.row.weightDetailUrl" alt="">
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
  
      <!-- 补扣明细 -->
      <div class="box-card" v-show="activeName==3">
        <div id="printButcherFee">
          <div class="point_icon">
            <span>补扣明细</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="8">
              <span>补扣总额(元)：</span>
              <span class="model-text">{{ butcherFeeInfo.subsidyDeductAmount }}</span>
            </el-col>
            <el-col :span="8">
              <span>补贴金额(元)：</span>
              <span class="model-text">{{ butcherFeeInfo.subsidyAmount }}</span>
            </el-col>
            <el-col :span="8">
              <span>扣款金额(元)：</span>
              <span class="model-text">{{ butcherFeeInfo.deductAmount }}</span>
            </el-col>
          </el-row>
          <el-row :gutter="20"></el-row>
          <el-table :data="butcherFeeData" stripe style="width: 100%">
            <el-table-column type="index" align="center" label="序号"></el-table-column>
            <el-table-column label="补扣类型" align="center">
              <template slot-scope="scope">{{ scope.row.feeType==1?'补款':'扣款' }}</template>
            </el-table-column>
            <el-table-column prop="feeOptions" label="项目" align="center">
              <template slot-scope="scope">
                  {{ handelOptions(dict.type.supplementary_payment,dict.type.deduction_items,scope.row.feeType,scope.row.feeOptions) }}  
              </template>
            </el-table-column>
            <el-table-column label="单位" align="center">
              <template slot-scope="scope">{{ handelstatus(unitList,scope.row.mathType) }}</template>
            </el-table-column>
            <el-table-column label="计算说明">
              <template
                slot-scope="scope"
              >{{ scope.row.mathType==3?'-':handelDirections(scope.row.mathType,scope.row.mathAmount,scope.row.mathNum) }}</template>
            </el-table-column>
            <el-table-column prop="totalAmount" label="金额(元)" align="right"></el-table-column>
          </el-table>
        </div>
      </div>
      <!-- 结算记录 -->
      <div class="box-card" v-show="activeName==4">
        <div class="point_icon">
          <span>结算信息</span>
        </div>
        <el-row :gutter="20">
          <el-col :span="8">
            <span>结算单号：</span>
            <span class="model-text">{{settleInfo.settlementCode}}</span>
          </el-col>
          <el-col :span="8">
            <span>结算金额(元)：</span>
            <span class="model-text">{{settleInfo.finalAmount}}</span>
          </el-col>
          <el-col :span="8">
            <span>结算时间：</span>
            <span class="model-text">{{settleInfo.settlementTime}}</span>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <span>支付方式：</span>
            <span class="model-text">{{settleInfo.paymentType == 0 ? '线下支付' : '我的额度'}}</span>
          </el-col>
          <el-col :span="8">
            <span>结算状态：</span>
            <span class="model-text">{{handelstatus(statusList,settleInfo.settlementStatus)}}</span>
          </el-col>
          <el-col :span="8">
            <span>交易流水号：</span>
            <span class="model-text">{{settleInfo.transactionCode}}</span>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <span>合同编号：</span>
            <span class="model-text">{{ settleInfo.contractCode }}</span>
          </el-col>
          <el-col :span="8">
            <span>负责人：</span>
            <span class="model-text">{{ settleInfo.updateUserName }}</span>
          </el-col>
        </el-row>
      </div>
      <el-dialog  width="30%" :modal="false" :visible.sync="dialogVisible">
        <img width="100%" style="height: 500px;" :src="currentImgUrl" alt="">
      </el-dialog>
    </div>
  </template>
    
    <script>
  import {
    settlementdetail,
    settlementWeigthDetail,
    settlementButcherDetail,
  } from "@/api/ffs/noGoodsPledged.js";
  export default {
    dicts: ["supplementary_payment", "deduction_items"],
    props: {
      activeName: {
        type: String,
        default: "1",
      },
    },
    data() {
      return {
        statusList: [
          { label: "取消", value: "0" },
          { label: "已结算", value: "1" },
          { label: "待结算", value: "2" },
        ],
        unitList: [
          { label: "kg", value: "1" },
          { label: "只", value: "2" },
          { label: "元", value: "3" },
        ],
        settleTableData: [],
        butcherFeeData: [],
        tableData: [],
        settlementId: "",
        settleInfo: {}, //结算单数据
        weightInfo: {}, //检斤数据
        butcherFeeInfo: {}, //补扣明细
        dialogVisible: false,
        currentImgUrl: ''
      };
    },
    computed: {
      handelstatus() {
        return (list, value) => {
          let name = "";
          list.forEach((item) => {
            if (item.value == value) {
              name = item.label;
            }
          });
          return name;
        };
      },
      handelDirections() {
        return (type, amount, num) => {
          return `每${type == 1 ? "kg" : "只"}${amount}元,共有${num}${
            type == 1 ? "kg" : "只"
          }`;
        };
      },
      handelOptions(){
          return (listF,listT,type,value)=>{
             let list=type==1?[...listF]:[...listT]
             let name=''
             list.forEach(item=>{
                  if(item.value==value){
                      name=item.label
                  }
             })
             return name
          }
      }
    },
    methods: {
      //结算单
      getInfo() {
        settlementdetail({ settlementId: this.settlementId }).then((res) => {
          if (res.code == 200) {
            this.settleTableData = res.result.butcherWeightList || [];
            this.settleInfo = res.result || {};
          }
        });
      },
      // 检斤单
      getWeigth() {
        settlementWeigthDetail({ settlementId: this.settlementId }).then((res) => {
          if (res.code == 200) {
            this.weightInfo = res.result || {};
            this.tableData = res.result.list || [];
            this.tableData.push({
              index: "index",
              weightNum: this.weightInfo.finalButcherNum,
              grossWeight: this.weightInfo.finalGrossWeight,
              tareWeight: this.weightInfo.finalTareWeight,
              netWeight: this.weightInfo.finalNetWeight,
              weightAmount: this.weightInfo.finalWeightPrice,
            });
          }
        });
      },
      //补扣明细
      getButcherFee() {
        settlementButcherDetail({ settlementId: this.settlementId }).then((res) => {
          if (res.code == 200) {
            this.butcherFeeInfo = res.result || {};
            this.butcherFeeData = res.result.list || [];
          }
        });
      },
      showImg(row) {
        this.currentImgUrl = row.weightDetailUrl
        this.dialogVisible = true
      },
  
      //统计
      getSummaries(param) {
        const { columns, data } = param;
        const sums = [];
        columns.forEach((column, index) => {
          if (index === 0) {
            sums[index] = "合计";
            return;
          }
          if (index === 5) {
            sums[index] = this.settleInfo.finalNetWeight;
          }
          if (index === 8) {
            sums[index] = this.settleInfo.payableAmount;
          }
          if (index === 4) {
            sums[index] = this.settleInfo.finalButcherNum;
          }
        });
        return sums;
      },
    },
  };
  </script>
    
    <style lang="scss" scoped>
  .el-row {
    font-size: 14px !important;
    margin-top: 20px;
    &:last-child {
      margin-bottom: 20px;
    }
  }
  .card-title {
    margin-bottom: 15px;
  }
  .fast {
    width: 8px;
    height: 18px;
    background: #409eff;
    margin-right: 10px;
  }
  
  .header {
    &-title {
      font-size: 20px;
    }
  }
  </style>