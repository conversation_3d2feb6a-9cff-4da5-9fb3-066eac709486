<template>
    <div class="drawer_box">
        <div id="print">
            <div class="fc mt20 point_icon">
                <span>入库信息</span>
            </div>
              <el-descriptions class="mt20" :column="2" size="medium" border>

                <el-descriptions-item label='供货商类型：'>{{ supplierTypes[dataInfo.supplierType] }}</el-descriptions-item>
                <el-descriptions-item label='供货商名称：'>{{ dataInfo.supplierName}}</el-descriptions-item>
                <el-descriptions-item label='联系电话：'>{{ dataInfo.contactPhone }}</el-descriptions-item>
                <el-descriptions-item label='入库申请编号：'>{{ dataInfo.checkInCode }}</el-descriptions-item>

            </el-descriptions>
            <div class="fc mt20 point_icon">
                <span>入库明细</span>
            </div>
            <el-table class="mt20" :data="tableData" max-height="500" >
                 <el-table-column type="index" align="center" width="55" label="序号"></el-table-column>
                                    <el-table-column prop="inventoryRecordCode" show-overflow-tooltip min-width="150" align="center" label="入库编号" />
                                    <el-table-column prop="inventoryBatchCode" align="center" min-width='150' label="批次号"></el-table-column>
                                    <el-table-column prop="licensePlateNumber" align="center" min-width='150' label="车牌号"></el-table-column>
                                    <el-table-column prop="supplierName" show-overflow-tooltip min-width="130" align="center" label="供货商名称" />
                                    <el-table-column prop="serialCode" show-overflow-tooltip min-width="130" align="center" label="供货商类型" >
                                        <template slot-scope="scope">{{ supplierTypes[scope.row.supplierType] }}</template>
                                    </el-table-column>
                                    <el-table-column prop="contactPhone" show-overflow-tooltip min-width="130" align="center" label="联系电话">
                                        <template slot-scope="scope"><span>{{ scope.row.contactPhone }}</span></template>
                                    </el-table-column>
                                    <el-table-column prop="warehouseName" show-overflow-tooltip min-width="130" align="center" label="仓库名称" />
                                    <el-table-column prop="warehouseLocationCode" show-overflow-tooltip min-width="130" align="center" label="库位编号" />
                                    <el-table-column prop="materialsTypeName" show-overflow-tooltip min-width="130" align="center" label="物料名称" />
                                    <el-table-column prop="materialsVarietyName" show-overflow-tooltip min-width="130" align="center" label="品种" />
                                    <el-table-column prop="materialsLevelName" show-overflow-tooltip min-width="130" align="center" label="等级" />
                                    <el-table-column prop="inventoryWeight" show-overflow-tooltip min-width="150" :sort-method="(a, b) => { return a.inventoryWeight - b.inventoryWeight}" align="right" sortable label="净重（Kg）" />
                                    <el-table-column prop="baseUnitPrice" show-overflow-tooltip min-width="180" :sort-method="(a, b) => { return a.baseUnitPrice - b.baseUnitPrice}" align="right" sortable label="收购单价（kg/元）" />
                                    <el-table-column prop="inventoryAmount" show-overflow-tooltip min-width="150" :sort-method="(a, b) => { return a.inventoryAmount - b.inventoryAmount}" align="right" sortable label="收购金额（元）" />
                                    <el-table-column prop="createBy" show-overflow-tooltip min-width="130" align="center" label="操作人" />
                                    <el-table-column prop="createTime" show-overflow-tooltip min-width="180" sortable align="center" label="操作时间" />

            </el-table>
        </div>
    </div>
</template>
<script>
import { checkInRecordList } from "@/api/ffs/noGoodsPledged.js";;
export default {
    data() {
        return {
            tableData: [],
            tableMaterialsData: [],

            supplierTypes: { 1: '企业', 3 : '个人' },
            currentItem: {},
            handeltext: {
                1: '定重',
                2: '抄码'
            },
            dataDetail: {}
        }
    },
    props: {
        dataInfo: Object,
        isShowInfo: String,
        tenantId:String
    },
    watch: {
        dataInfo() {
            this.getList();
        }
    },
    created() {
      console.log('111')
        // if (this.dataInfo.inventoryId) {

            this.getList()
        // }
    },
    methods: {
        getList() {
          console.log(this.dataInfo,'****')
            checkInRecordList({
                pageNum : 1,
                pageSize:10000,
                checkInId: this.dataInfo.checkInId,
                tenantId:this.tenantId
            }).then(res => {
                if (res.code == 200) {
                    this.tableData = res.result.list;

                }
            })
        },
    }
}
</script>


<style lang="scss" scoped>
.el-row {
    font-size: 14px !important;
    margin-top: 20px;
    &:last-child {
        margin-bottom: 20px;
    }
    .el-col-8{
        margin-top: 20px;
    }
}
.card-title {
    margin-bottom: 15px;
    margin-top: 20px;
}
.fast {
    width: 8px;
    height: 18px;
    background: #409eff;
    margin-right: 10px;
}

.header {
    &-title {
        font-size: 20px;
    }
}
</style>
