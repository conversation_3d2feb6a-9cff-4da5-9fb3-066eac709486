<template>
    <div>
      <el-dialog
        title="无货质押监管详情"
        :visible.sync="detailsFormData.open"
        width="1200px"
        :close-on-click-modal="false"
        @close="close"
        class="noGoodsPledged mortgagesp"
         append-to-body
      >
        <div class="syaddsya">
          <el-tag type="danger" effect="dark">{{superviseStatusName(info.superviseStatus)}}</el-tag>
        </div>
        <el-descriptions :title="'被监管方：'+info.applyName" :column="3">
          <el-descriptions-item label="手机号">{{info.applyPhone}}</el-descriptions-item>
          <el-descriptions-item label="合同编号">{{info.contractNo}}</el-descriptions-item>
          <el-descriptions-item label="委托方">{{info.bankInfo?.companyName}}</el-descriptions-item>
        </el-descriptions>
        <el-descriptions title :column="3" v-show="info.superviseStatus==3">
          <el-descriptions-item label="关闭人">{{info.closeUserName}}</el-descriptions-item>
          <el-descriptions-item label="关闭时间">{{info.closeTime}}</el-descriptions-item>
          <el-descriptions-item label="关闭原因">{{info.closeDesc}}</el-descriptions-item>
        </el-descriptions>
        <el-row :gutter="20" type="flex" justify="space-between" class="mbt20">
          <el-col :span="5">
            <el-card shadow="hover" class="carbix">
              <div class="carbixtx">授信金额（万）</div>
              <div class="carbixmys">{{actualAmt}}</div>
            </el-card>
          </el-col>
          <el-col :span="5">
            <el-card shadow="hover" class="carbix">
              <div class="carbixtx">可用金额（万）</div>
              <div class="carbixmys">{{avlAmt}}</div>
            </el-card>
          </el-col>
          <el-col :span="5">
            <el-card shadow="hover" class="carbix">
              <div class="carbixtx">已用金额（万）</div>
              <div class="yxjebx">
                <div class="carbixmys">{{avlAmtEnd}}</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="5">
            <el-card shadow="hover" class="carbix">
              <div class="carbixtx">利率（年化）</div>
              <div class="yxjebx">
                <div class="carbixmys">{{info.superviseServiceRate}}%</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="5">
            <el-card shadow="hover" class="carbix">
              <div class="carbixtx">监管周期（月）</div>
              <div class="carbixmys">{{info.superviseLimit||'--'}}</div>
            </el-card>
          </el-col>
        </el-row>
        <div class="msabxs">
          <div v-if="info.superviseStart">
            <span class="msaitirts">监管时间：</span>
            <span class="msaitimss">{{info.superviseStart}}至{{info.superviseEnd}}</span>
          </div>

          <div>
            <span class="msaitirts">监管员：</span>
            <span class="msaitimss">{{info.supervisorName||'--'}}</span>
          </div>
          <div>
            <span class="msaitirts">客户经理：</span>
            <span class="msaitimss">{{info.loanOfficerName}}</span>
          </div>
          <div v-if="info.pastorName">
            <span class="msaitirts">畜牧师：</span>
            <span class="msaitimss">{{info.pastorName}}</span>
          </div>
          <div>
            <span class="msaitirts">创建时间：</span>
            <span class="msaitimss">{{info.createTime}}</span>
          </div>
        </div>
        <div>
        <el-tabs v-model="stepsActive" type="card" stretch>
          <el-tab-pane v-for="(tab,index) in tbasList" :key="index" :label="tab" :name="index.toString()"></el-tab-pane>
        </el-tabs>
        <baseInfo v-show="stepsActive==0" :info="info"></baseInfo>
        <paymenHistory v-show="stepsActive==1" :info="info"></paymenHistory>
        <consumptionHistory v-show="stepsActive==2" :info="info"></consumptionHistory>
        <fundDetail v-show="stepsActive==3" :info="info"></fundDetail>
        <inventoryDetails v-if="stepsActive==4" :info="info" ></inventoryDetails>
        <warehousing v-if="stepsActive==5" :info="info" ></warehousing>
        <outBound v-if="stepsActive==6" :info="info"  ></outBound>
        <emergencyContact  v-show="stepsActive==7" :info="info"></emergencyContact>
        <supervisorSet v-show="stepsActive==8" :info="info"></supervisorSet>
        <apply-info v-if="stepsActive==11" :dataInfo="info" />
        <regulatory-log v-show="stepsActive==10" :info="info" />
        <inspection-records v-show="stepsActive==9" :info="info" />
      </div>


        <span slot="footer" class="dialog-footer" v-show="!detailsFormData.disable">
          <el-button type="info" @click="close">取消</el-button>
        </span>
      </el-dialog>
    </div>
  </template>
  <script>
import baseInfo from './baseInfo.vue'
import paymenHistory from './paymenHistory.vue'
import consumptionHistory from './consumptionHistory.vue'
import fundDetail from './fundDetail.vue'
import  inventoryDetails from './inventoryDetails.vue'
import  warehousing from './warehousing.vue'
import emergencyContact from './emergencyContact.vue'
import  outBound from  './outBound.vue'
import supervisorSet from './supervisorSet.vue'
import applyInfo from './applyInfo.vue'
import { warehouseList } from "@/api/ffs/noGoodsPledged.js";
import regulatoryLog from "./regulatoryLog"; //监管日志
import inspectionRecords from "@/views/ffs/mortgage/supervision/components/detailsForm/inspectionRecords"; //巡检记录
import { fundAplyDetail } from "@/api/ffs/superviseLhApi.js";
  export default {
    props: {
      detailsFormData: {
        type: Object,
        default: () => {},
      },
    },
    components:{
        baseInfo,
        paymenHistory,
        consumptionHistory,
        fundDetail,
        inventoryDetails,
        warehousing,
        emergencyContact,
        supervisorSet,
        outBound,
        applyInfo,
        regulatoryLog,
        inspectionRecords
    },
    data() {
      return {
        loading: false,
        wareList:[],
        tbasList: [
          "基本信息",
          "还款记录",
          "交易记录",
          "资金明细",
          "库存明细",
          "入库明细",
          "出库明细",
          "紧急联系人",
          "监管员设置",
          "巡检记录",
          "监管日志",
          "申请信息",
        ],
        stepsActive:"0",

        info: {
          bankInfo: {},
        },
        actualAmt: 0,
        avlAmt: 0,
        avlAmtEnd: 0,
      };
    },
    created() {
      this.info = this.detailsFormData.info || {};
      this.getWarehouse()
      this.getDetails()
    },
    computed: {
      superviseStatusName() {
        return (val) => {
          if (val == 1) {
            return "待监管";
          } else if (val == 2) {
            return "监管中";
          } else if (val == 3) {
            return "已结束";
          }
        };
      },
    },
    methods: {
      getWarehouse(){
        warehouseList({requestRole:2,warehouseType:-1,pageNum:1,pageSize:99999,tenantId:this.info.tenantId}).then(res=>{
            if(res.code==200){
                this.wareList=res.result.list||[]
            }
        })
      },
      getDetails() {
        fundAplyDetail({quotaCode: this.info.contractNo})
          .then((res) => {
            this.loading = false;
            if (res.stautscode == 200) {
              const totalData = res.data
              this.actualAmt = (totalData.actualAmt / 10000).toFixed(2)
              this.avlAmt = (totalData.avlAmt / 10000).toFixed(2)
              this.avlAmtEnd = ((totalData.actualAmt - totalData.avlAmt) / 10000).toFixed(2)
            } else {
              this.$message.error(res.message);
            }
          })
          .catch((err) => {
            this.info = null;
            console.log("err", err);
          });
      },
      close() {
        this.$emit("close");
      },
      refresh() {
        console.log("刷新");
      },
    },
  };
  </script>

  <style lang="scss" >
  .noGoodsPledged {
    .el-dialog__header {
      background-color: #f4f4f4;
    }

    .el-dialog__footer {
      text-align: center;
    }

    .selectWidth {
      width: 100%;
    }
  }
  .mortgagesp {
    .el-card__body {
      padding-left: 8px;
      padding-right: 8px;
    }
  }
  .carbix {
    text-align: center;
  }
  .carbixtx {
    color: #999;
    margin-bottom: 14px;
  }
  .carbixmys {
    color: #333;
    font-weight: 600;
    line-height: 30px;
  }
  .msabxs {
    display: flex;
    justify-content:space-between;
    margin-top: 30px;
    border-bottom: 1px solid #eee;
    padding-bottom: 20px;
  }

  .msaitirts {
    color: #333;
  }
  .msaitimss {
    color: #999;
  }
  .mbt20 {
    margin-top: 20px;
  }
  .syaddsya {
    position: absolute;
    right: 20px;
  }
  </style>
