<template>
  <div class="app-container tabs_box">
      <el-row >
        <el-form
          :model="queryParams"
          ref="queryForm"
          size="small"
          :inline="true"
          label-width="120px"
        >
          <el-form-item label="" prop="checkOutCode">
              <el-input
                  v-model="queryParams.checkOutCode"
                  placeholder="出库申请编号"
              />
          </el-form-item>
          <el-form-item label="" prop="superviseId">
              <el-input
                  v-model="queryParams.superviseId"
                  placeholder="监管单号"
              />
          </el-form-item>
          <el-form-item prop="supplierType">
              <el-select v-model="queryParams.supplierType" placeholder='出库人类型' clearable>
                  <el-option
                      v-for="(item,index) in supplierTypes"
                      :label="item.text"
                      :value="item.value"
                      :key="index"
                  />
              </el-select>
          </el-form-item>
          <el-form-item label="" prop="supplierName">
              <el-input
                  v-model="queryParams.supplierName"
                  placeholder="出库人姓名"
              />
          </el-form-item>
          <el-form-item label="" prop="contactPhone">
              <el-input
                  v-model="queryParams.contactPhone"
                  placeholder="出库人电话"
              />
          </el-form-item>
          <el-form-item prop="materialsTypeId">
              <el-select v-model="queryParams.materialsTypeId" placeholder='物料名称' @change="changeMaterialsType">
                  <el-option
                      v-for="(item,index) in materialsTypeList"
                      :label="item.materialsTypeName"
                      :value="item.materialsTypeId"
                      :key="index"
                  />
              </el-select>
          </el-form-item>
          <el-form-item prop="materialsVarietyId">
              <el-select v-model="queryParams.materialsVarietyId" placeholder='品种' no-data-text='请先选择物料名称'>
                  <el-option
                      v-for="(item,index) in materialsVarietyList"
                      :label="item.materialsVarietyName"
                      :value="item.materialsVarietyId"
                      :key="index"
                  />
              </el-select>
          </el-form-item>
          <el-form-item prop="materialsLevelId">
              <el-select v-model="queryParams.materialsLevelId" placeholder='等级' >
                  <el-option
                      v-for="(item,index) in materialsLevelList"
                      :label="item.materialsLevelName"
                      :value="item.materialsLevelId"
                      :key="index"
                  />
              </el-select>
          </el-form-item>

          <!-- <el-form-item label="出库单号" prop="inventoryCode">
            <el-input v-model="queryParams.inventoryCode" placeholder="请输入出库单号" clearable @keyup.enter.native="handleQuery"/>
          </el-form-item>
          <el-form-item label="出库类型" prop="inventoryType">
            <el-select v-model="queryParams.inventoryType" clearable>
              <el-option
                v-for="(item,index) in warehouseTypeList"
                :label="item.text"
                :value="item.value"
                :key="index"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="出库仓库" prop="outWarehouseId">
          <el-select v-model="queryParams.outWarehouseId" clearable>
            <el-option
              v-for="(item, index) in warehouseList"
              :label="item.warehouseName"
              :value="item.warehouseId"
              :key="index"
            />
          </el-select>
        </el-form-item>
          <el-form-item label="单据状态" prop="inventoryStatus">
            <el-select v-model="queryParams.inventoryStatus" clearable>
              <el-option
                v-for="(item,index) in inventoryStatusList"
                :label="item.text"
                :value="item.value"
                :key="index"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="出库时间" >
            <el-date-picker
              v-model="dateEnter"
              style="width: 240px"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item> -->

            <el-form-item label=" ">
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>
      </el-row>
    <el-card class="table_box" shadow="never">
        <el-row class="mb20">
        <div style="display: flex;justify-content: end;">
          <el-button
            class="default_btn"
            icon="el-icon-download"
            size="mini"
            @click="exportList"
          >导出数据</el-button>
        </div>
      </el-row>
      <!-- 表格数据 -->
      <el-table :data="tableData" ref="myTable" style="width: 100%" v-loading="loading" border>
        <el-table-column type="index" align="center" width="55" label="序号"></el-table-column>
                            <el-table-column prop="checkOutCode" show-overflow-tooltip min-width="180" align="center" label="出库申请编号" />
                            <el-table-column prop="superviseId" show-overflow-tooltip min-width="130" align="center" label="监管单号" />
                            <el-table-column prop="serialCode" show-overflow-tooltip min-width="130" align="center" label="出库类型"  >
                                <template slot-scope="scope">{{ handeltext(checkOutMethods, scope.row.checkOutMethod) }}</template>
                            </el-table-column>
                            <el-table-column prop="serialCode" show-overflow-tooltip min-width="130" align="center" label="出库人类型"  >
                                <template slot-scope="scope">{{ handeltext(supplierTypes, scope.row.supplierType) }}</template>
                            </el-table-column>
                            <el-table-column prop="supplierName" show-overflow-tooltip min-width="130" align="center" label="申请人姓名" />
                            <el-table-column prop="contactPhone" show-overflow-tooltip min-width="130" align="center" label="申请人联系电话" />
                            <el-table-column prop="materialsNames" show-overflow-tooltip min-width="130" align="center" label="物料信息" />
                            <el-table-column prop="applyWeight" show-overflow-tooltip min-width="180" :sort-method="(a, b) => { return a.applyWeight - b.applyWeight}" align="center" sortable label="申请出库重量（Kg）" />
                            <el-table-column prop="checkOutWeight" show-overflow-tooltip min-width="180" :sort-method="(a, b) => { return a.checkOutWeight - b.checkOutWeight}" align="center" sortable label="已出重量（kg）" />
                            <el-table-column prop="checkOutBy" show-overflow-tooltip min-width="130" align="center" label="操作人" />
                            <el-table-column prop="checkOutTime" show-overflow-tooltip min-width="180" sortable align="center" label="操作时间" />
                            <el-table-column prop="" show-overflow-tooltip min-width="130" align="center" label="审核状态"  >
                                <template slot-scope="scope">
                                    <span v-if="scope.row.checkOutStatus == 0">已驳回</span>
                                    <span v-if="scope.row.checkOutStatus == 1">待审核</span>
                                    <span v-if="scope.row.checkOutStatus >= 2">审核通过</span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="checkOutStatus" show-overflow-tooltip min-width="130" align="center" label="出库状态" >
                                <template slot-scope="scope">
                                    <span v-if="scope.row.checkOutStatus <= 1">--</span>
                                    <span v-if="scope.row.checkOutStatus == 2">待出库</span>
                                    <span v-if="scope.row.checkOutStatus == 3">出库中</span>
                                    <span v-if="scope.row.checkOutStatus == 4">已出库</span>
                                </template>
                            </el-table-column>
                            <el-table-column label="操作" align="center" fixed="right" width="180">
                                <template slot-scope="scope">
                                    <el-button
                                      icon="el-icon-info"
                                      size="mini"
                                      class="text_btn"
                                      @click="stockInfo(scope.row)"
                                      type="text" >详情</el-button>

                                    <el-button icon="el-icon-download" size="mini" v-if="scope.row.checkOutStatus == 4" class="text_btn" @click="printButton(scope.row)" type="text" >下载出库单</el-button>
                                    <!-- <el-button size="mini" class="text_btn" v-if="scope.row.checkOutStatus == 1 && !scope.row.auditFlowId" @click="checkOutShow(scope.row)" type="text" >审批</el-button> -->
                                </template>
                            </el-table-column>

        <!-- <el-table-column show-overflow-tooltip align="center" type="index" label="序号" width="50"></el-table-column>
        <el-table-column show-overflow-tooltip align="center" prop="inventoryCode" label="出库单号">
          <template slot-scope="scope">{{ scope.row.inventoryCode }}</template>
        </el-table-column>
        <el-table-column show-overflow-tooltip align="center" label="出库类型">
          <template slot-scope="scope">{{ handeltext(scope.row.inventoryType) }}</template>
        </el-table-column>
        <el-table-column show-overflow-tooltip align="center" prop="warehouseName" label="出库仓库">
          <template slot-scope="scope">{{ scope.row.warehouseName }}</template>
        </el-table-column>
        <el-table-column show-overflow-tooltip align="right" prop="inventoryNum" label="出库数量">
          <template slot-scope="scope">{{ scope.row.inventoryNum }}</template>
        </el-table-column>
        <el-table-column
          show-overflow-tooltip
          align="right"
          prop="inventoryWeight"
          label="出库重量（kg）"
          width="130"
        >
          <template slot-scope="scope">{{ scope.row.inventoryWeight }}</template>
        </el-table-column>
        <el-table-column show-overflow-tooltip align="center" label="单据状态">
          <template slot-scope="scope">
            <span
              :class="{
								orange: scope.row.inventoryStatus == 2,
								blue: scope.row.inventoryStatus == 3,
								green: scope.row.inventoryStatus == 1,
							}"
            >{{ handelInventoryStatustext(scope.row.inventoryStatus) }}</span>
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip align="center" prop="createUserName" label="操作人">
          <template slot-scope="scope">{{ scope.row.createUserName }}</template>
        </el-table-column>
        <el-table-column show-overflow-tooltip align="center" prop="createTime" label="出库日期">
          <template slot-scope="scope">{{ scope.row.createTime }}</template>
        </el-table-column>
        <el-table-column show-overflow-tooltip align="center" label="操作" >
          <template slot-scope="scope">
            <el-button
              class="text_btn"
              size="mini"
              icon="el-icon-warning-outline"
              @click="stockInfo(scope.row)"
              type="text"
            >查看</el-button>
          </template>
        </el-table-column> -->
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
        class="pagination-box"
      />
    </el-card>
    <el-dialog title="详情" :visible.sync="modelShow" width="1400px" :before-close="handleClose" :append-to-body="true">
        <outBoundInfo ref="model" :dataInfo="currentItem" :tenantId="queryParams.tenantId"></outBoundInfo>
    </el-dialog>
  </div>
</template>
<script>
import outBoundInfo from './components/outBoundInfo.vue'
import {
  checkOutPage ,
  checkOutExport,

  materialsTypeList,
  materialsVarietyList,
  materialsLevelList,
  checkOutDocumentsExport
  } from "@/api/ffs/noGoodsPledged.js";
import { exportExcel } from "@/utils/east";
export default {
    components:{
        outBoundInfo
    },
    props:{
        info:{
            type:Object,
            default:{}
        },

    },
  data() {
    return {
      warehouseLocationList:[],
      materialsTypeList:[],
      materialsVarietyList:[],
      materialsLevelList:[],

      modelShow:false,
      currentItem:{},
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        materialsTypeId: '', //物料类型ID
        materialsVarietyId: '', //物料品种ID
        materialsLevelId: '', //物料等级ID
        checkOutCode: '', //入库出库编号
        supplierType: '', //供应商类型 1企业 3个人
        supplierName: '', //供应商名称
        contactPhone: '', //供应商手机号
        superviseId: '',
        supplierType:''
      },
      supplierTypes: [
          { text: '企业', value: 1 },
          { text: '个人', value: 3 }
      ],
      checkOutMethods: [
          { text: '自主出库', value: 1 },
          { text: '申请出库', value: 2 }
      ],

      tableData: [{}],
      loading: true,

      total: 0,

    };
  },

  computed: {
    handeltext(){
        return (list, value)=>{
            let name=''
            list.forEach(item=>{
                if(item.value==value){
                    name=item.text
                }
            })
            return name
        }
    }

  },

  created() {
    this.queryParams.tenantId=this.info.tenantId
    this.getList();
    this.getMaterialsTypeList()

    this.getMaterialsLevelList()
  },
  methods: {

    changeMaterialsType() {
      if(this.queryParams.materialsTypeId){
        materialsVarietyList({materialsTypeId:this.queryParams.materialsTypeId,tenantId:this.info.tenantId}).then(res=>{
          if(res.code==200){
            this.materialsVarietyList=res.result
            this.queryParams.materialsVarietyId=''
          }
        })
      }else{
        this.materialsVarietyList=[]
        this.queryParams.materialsVarietyId=''
      }

    },

    getMaterialsTypeList(){
      materialsTypeList({tenantId:this.info.tenantId}).then(res=>{
        if(res.code==200){
          this.materialsTypeList=res.result
        }
      })
    },
    getMaterialsLevelList(){
      materialsLevelList({tenantId:this.info.tenantId}).then(res=>{
        if(res.code==200){
          this.materialsLevelList=res.result
        }
      })
    },
    refresh() {
      this.getList();
    },
    //列表查询
    getList() {
      checkOutPage({
          ...this.queryParams
      }).then(res => {
          this.tableData = res.result.list
          this.total = Number(res.result.total);
          this.loading = false;
      })


    },
    reset() {
      this.resetForm("queryForm");
    },
    //重置
    resetQuery() {
      this.materialsVarietyList=[]
      this.reset();
      this.handleQuery();
    },
    //刷新页面
    refreshList() {
      this.getList();
    },

    //搜索
    handleQuery() {
      this.queryParams.pageNum = 1;

      this.getList();
    },
    stockInfo(row) {
        this.currentItem = row
        this.modelShow=true
    },
    handleClose(){
        this.modelShow=false
    },
    exportList() {
      exportExcel(checkOutExport,{
                          ...this.queryParams
                      },'出库明细')

    },
    printButton(rowData) {

                exportExcel(checkOutDocumentsExport, {
                    checkOutId: rowData.checkOutId
                },'出库单')
            },

  },
};
</script>

<style lang="scss" scoped>
.tab {
  padding: 0 20px;
  color: #333333;
  span {
    margin: 0 10px;
    cursor: pointer;
  }
}
</style>
