<template>
  <div>
    <el-row :gutter="10" class="mb8">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">

        <el-form-item label="交易时间：">
          <el-date-picker
            v-model="time"
            value-format="yyyy-MM-dd"
            style="width: 240px"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleExportQuot"
            v-show="total > 0"
          >导出</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-row>

    <el-table :data="list" stripe style="width: 100%;margin-top:10px" v-loading="loading" border>
      <el-table-column type="index" label="序号" width="60"></el-table-column>
      <el-table-column prop="orderCode" label="交易流水号" align="center"></el-table-column>
      <el-table-column prop="businessName" label="项目"></el-table-column>
      <el-table-column prop="amountYuan" label="交易金额" align="right" width="100"></el-table-column>
      <el-table-column prop="transactionTime" label="交易时间" align="center"></el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { fundDetails } from "@/api/ffs/superviseLhApi.js";
import { exportQuot } from "@/views/ffs/fundFlowSupervision/components/detailsForm/exportQuot.js";
export default {
  name: "fundDetail",

  props: {
    info: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      queryParams: {
        quotaCode: "",
        pageNum: 1,
        pageSize: 10,
        startTime: "",
        endTime: "",
      },
      time: undefined,
      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
    };
  },
  created() {
    this.queryParams.quotaCode = this.info.contractNo;
    this.getList();
  },
  methods: {
    /** 资金明细记录 */
    getList() {
      fundDetails(this.queryParams).then((res) => {
        this.loading = false;
        if (res.stautscode == 200) {
          const total = Number(res.data.totalRow || 0);
          if (total == 0) {
            this.list = [];
            return;
          }
          this.list = res.data.list || res.data || [];
          this.total = total;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    handleExportQuot() {
        exportQuot(this, 3, this.info.contractNo);
    },
    /** 搜索按钮操作 */
    handleQuery() {
      if (this.time && this.time.length > 0) {
        this.queryParams.startTime = this.time[0];
        this.queryParams.endTime = this.time[1];
      } else {
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
      }
      this.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.time = [];
      this.queryParams.startTime = "";
      this.queryParams.endTime = "";
      this.resetForm("queryForm");
      this.handleQuery();
    },
    //关闭弹框
    close() {
      this.outEnterHoseData.open = false;
      this.$emit("close");
    },
  },
};
</script>

<style lang="scss">
.fieldList {
  .el-dialog__header {
    background-color: #f4f4f4;
  }

  .el-dialog__footer {
    text-align: center;
  }

  .selectWidth {
    width: 100%;
  }
}
</style>
