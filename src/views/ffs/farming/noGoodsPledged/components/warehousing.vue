<template>
  <div class="app-container tabs_box">
    <el-row :gutter="10">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="120px">


        <el-form-item label="" prop="checkInCode">
            <el-input
                v-model="queryParams.checkInCode"
                placeholder="入库申请编号"
            />
        </el-form-item>
        <el-form-item label="" prop="supplierName">
            <el-input
                v-model="queryParams.supplierName"
                placeholder="供货商名称"
            />
        </el-form-item>
        <el-form-item label="" prop="contactPhone">
            <el-input
                v-model="queryParams.contactPhone"
                placeholder="联系电话"
            />
        </el-form-item>
        <el-form-item prop="supplierType">
            <el-select v-model="queryParams.supplierType" placeholder='供货商类型' >
                <el-option
                    v-for="(item,index) in supplierTypes"
                    :label="item.text"
                    :value="item.value"
                    :key="index"
                />
            </el-select>
        </el-form-item>
        <el-form-item label="" prop="materialsNames" >
            <el-input
                v-model="queryParams.materialsNames"
                placeholder="物料信息"
            />
        </el-form-item>




        <el-form-item label="">
          <el-date-picker
            v-model="dateEnter"
            style="width: 240px"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            start-placeholder="操作起始日期"
            end-placeholder="操作截至日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label=" ">
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-row>
    <el-card class="table_box" shadow="never">
      <el-row class="mb20">
        <div style="display: flex;justify-content: end;">
          <el-button
            class="default_btn"
            icon="el-icon-download"
            size="mini"
            @click="exportList"
          >导出数据</el-button>
        </div>
      </el-row>

      <!-- 表格数据 -->
      <el-table :data="tableData" stripe style="width: 100%" v-loading="loading" border>
        <el-table-column type="index" align="center" width="55" label="序号"></el-table-column>
        <el-table-column prop="checkInCode" show-overflow-tooltip min-width="150" align="center" label="入库申请编号" />
        <el-table-column prop="supplierName" show-overflow-tooltip min-width="130" align="center" label="供货商名称" />
        <el-table-column show-overflow-tooltip min-width="130" align="center" label="供货商类型" >
            <template slot-scope="scope">{{ handeltext(supplierTypes, scope.row.supplierType) }}</template>
        </el-table-column>
        <el-table-column prop="contactPhone" show-overflow-tooltip min-width="130" align="center" label="联系电话" />
        <el-table-column show-overflow-tooltip min-width="130" align="center" label="入库类型" >
            <template slot-scope="scope">{{ handeltext(checkInTypes, scope.row.checkInType) }}</template>
        </el-table-column>
        <el-table-column prop="materialsNames" show-overflow-tooltip min-width="130" align="center" label="物料信息" />
        <el-table-column prop="inventoryWeight" show-overflow-tooltip min-width="150" :sort-method="(a, b) => { return a.inventoryWeight - b.inventoryWeight}" align="right" sortable label="净重（Kg）" />
        <el-table-column prop="inventoryAmount" show-overflow-tooltip min-width="150" :sort-method="(a, b) => { return a.inventoryAmount - b.inventoryAmount}" align="right" sortable label="收购金额（元）" />
        <el-table-column prop="createBy" show-overflow-tooltip min-width="130" align="center" label="操作人" />
        <el-table-column prop="createTime" show-overflow-tooltip min-width="180" sortable align="center" label="操作时间" />
        <el-table-column label="操作" align="center" prop="op" fixed="right" width="160">
            <template slot-scope="scope">
                <!-- <el-button icon="el-icon-info" size="mini" class="text_btn" @click="infoDevice(scope.row)" type="text" >详情</el-button> -->
                <el-button icon="el-icon-info" size="mini" class="text_btn" @click="stockInfo(scope.row)" type="text" >详情</el-button>
                <el-button icon="el-icon-download" size="mini" class="text_btn" @click="printButton(scope.row)" type="text" >下载入库单</el-button>
            </template>
        </el-table-column>

        <!-- <el-table-column show-overflow-tooltip align="center" type="index" label="序号" width="50"></el-table-column>
        <el-table-column show-overflow-tooltip align="center" prop="inventoryCode" label="入库单号">
          <template slot-scope="scope">{{ scope.row.inventoryCode }}</template>
        </el-table-column>

        <el-table-column show-overflow-tooltip align="center" prop="warehouseName" label="入库仓库">
          <template slot-scope="scope">{{ scope.row.warehouseName }}</template>
        </el-table-column>
        <el-table-column show-overflow-tooltip align="right" prop="inventoryNum" label="总数量">
          <template slot-scope="scope">{{ scope.row.inventoryNum }}</template>
        </el-table-column>
        <el-table-column show-overflow-tooltip align="right" prop="inventoryWeight" label="总重量（kg）">
          <template slot-scope="scope">{{ scope.row.inventoryWeight }}</template>
        </el-table-column>
        <el-table-column show-overflow-tooltip align="center" prop="createUserName" label="操作人">
          <template slot-scope="scope">{{ scope.row.createUserName }}</template>
        </el-table-column>
        <el-table-column show-overflow-tooltip align="center" prop="createTime" label="入库时间">
          <template slot-scope="scope">{{ scope.row.createTime }}</template>
        </el-table-column>
        <el-table-column show-overflow-tooltip align="center" label="操作" fixed="right">
          <template slot-scope="scope">
            <el-button
              class="text_btn"
              size="mini"
              icon="el-icon-warning-outline"
              @click="stockInfo(scope.row)"
              type="text"
            >查看</el-button>
          </template>
        </el-table-column> -->
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
        class="pagination-box"
      />
    </el-card>
    <el-dialog title="详情" :visible.sync="modelShow" width="1200px" :before-close="handleClose" :append-to-body="true">
        <warehousingInfo v-if='modelShow' ref="model" :dataInfo="currentItem" :tenantId="queryParams.tenantId"></warehousingInfo>
    </el-dialog>
  </div>
</template>

<script>
import { checkInList ,inventoryExport,checkInDocumentsExport,checkInExport } from "@/api/ffs/noGoodsPledged.js";
import { exportExcel } from "@/utils/east";
import warehousingInfo from "./components/warehousingInfo.vue";
export default {
    props:{
        info:{
            type:Object,
            default:{}
        },

    },
  components: {
    warehousingInfo,
  },
  data() {
    return {
        warehouseList:[],
        currentItem:{},
        modelShow:false,
      queryParams: {
        checkInCode:'',
        supplierName:'',
        contactPhone:'',
        supplierType:'',
        materialsNames:'',
        inventoryStatus: '1,2',
        // startTime:'',
        // endTime:'',

        tenantId: "",
        pageNum: 1,
        pageSize: 10,
      },
      tableData: [],
      loading: true,
      dateEnter: [],
      total: 0,
      supplierTypes: [
          { text: '企业', value: 1 },
          { text: '个人', value: 3 }
      ],
      checkInTypes: [
          { text: '抵押', value: 1 },
          { text: '销售', value: 2 },
          { text: '取货', value: 3 }
      ],


    };
  },
  watch:{

  },

  computed: {
          handeltext(){
              return (list,value)=>{
                  let name=''
                  list.forEach(item=>{
                      if(item.value==value){
                          name=item.text
                      }
                  })
                  return name
              }
          }
      },

  created() {
    this.queryParams.tenantId=this.info.tenantId
    console.log(this.queryParams.tenantId,'&&&&&&&&&&');
    this.getList();
  },
  methods: {
    refresh() {
      this.getList();
    },
    //列表查询
    getList() {
      checkInList(this.queryParams).then((res) => {
        if (res.code == 200) {
          this.tableData = res.result.list;
          this.total = Number(res.result.total);
          this.loading = false;
        }
      });
    },
    reset() {
      this.resetForm("queryForm");
    },
    //重置
    resetQuery() {
      this.dateEnter = [];
      this.reset();
      this.handleQuery();
    },
    //刷新页面
    refreshList() {
      this.getList();
    },
    handelData(startTime, endTime, list) {

      if (list?.length > 0) {
        this.queryParams[startTime] = list[0];
        this.queryParams[endTime] = list[1];
      } else {
        delete this.queryParams[startTime];
        delete this.queryParams[endTime];
      }
    },
    //搜索
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.handelData("startTime", "endTime", this.dateEnter);
      this.getList();
    },
    stockInfo(row) {
        this.currentItem = row
      this.modelShow=true
    },
    handleClose(){
        this.modelShow=false
    },
    printButton(rowData) {
        // this.rowData = rowData
        // this.$refs.printInStorage.showModel()
        exportExcel(checkInDocumentsExport, {
            checkInId: rowData.checkInId
        },'入库单')
    },

    exportList() {
      exportExcel(checkInExport,{
                          inventoryStatus: '1,2',
                          ...this.queryParams
                      },'入库 - 单据')

        // exportExcel(inventoryExport,this.queryParams,'入库明细')
    },
  },
};
</script>

<style lang="scss" scoped>
.tab {
  padding: 0 20px;
  color: #333333;

  span {
    margin: 0 10px;
    cursor: pointer;
  }
}
</style>
