<template>
  <div class="app-container">
    <el-card class="mb10 form_box" shadow="never" ref="formBox">
      <el-form
        :model="queryParams"
        ref="queryForm"
        size="small"
        :inline="true"
        label-width="130px"
      >
        <el-row class="form_row">
          <el-col class="form_col">
            <!-- <el-form-item label="区域查询：" prop="areaDataRange">
                <RegCascaderTag v-model="queryParams.areaDataRange"></RegCascaderTag>
            </el-form-item> -->
            <el-form-item label="质押品类型：" prop="mortgageType">
              <el-select
                v-model="queryParams.mortgageType"
                clearable
                class="selectWidth"
              >
                <el-option
                  v-for="dict in dict.type.ffs_mortgage_type"
                  :key="dict.label"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="商品品类：" prop="commodityCategory">
              <el-select
                v-model="queryParams.commodityCategory"
                clearable
                class="selectWidth"
              >
                <el-option
                  v-for="dict in dict.type.ffs_commodity_category"
                  :key="dict.label"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="加工方式：" prop="processWay">
              <el-select
                v-model="queryParams.processWay"
                clearable
                class="selectWidth"
              >
                <el-option
                  v-for="dict in dict.type.ffs_machining_type"
                  :key="dict.label"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="商品类型：" prop="commodityType">
              <el-select
                v-model="queryParams.commodityType"
                clearable
                class="selectWidth"
              >
                <el-option
                  v-for="dict in dict.type.ffs_commodity_type"
                  :key="dict.label"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="商品编码/名称：" prop="searchValue">
              <el-input
                v-model="queryParams.searchValue"
                placeholder="请输入商品编码/名称"
                clearable
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="创建人：" prop="createUserName">
              <el-input
                v-model="queryParams.createUserName"
                placeholder="请输入创建人姓名"
                clearable
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="创建时间：">
              <el-date-picker
                v-model="time"
                style="width: 215px"
                value-format="yyyy-MM-dd HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row style="margin-left: 130px">
          <el-col>
            <el-form-item>
              <el-button
                type="primary"
                icon="el-icon-search"
                size="mini"
                @click="handleQuery"
                >搜索</el-button
              >
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
                >重置</el-button
              >
              <template v-if="toggleSearchDom">
                <el-button type="text" @click="packUp">
                  {{ toggleSearchStatus ? "收起" : "展开" }}
                  <i
                    :class="{
                      'el-icon-arrow-down': !toggleSearchStatus,
                      'el-icon-arrow-up': toggleSearchStatus,
                    }"
                  ></i>
                </el-button>
              </template>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <el-card shadow="never">
      <el-row class="mb8 form_btn">
        <el-col class="form_btn_col">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="addFrom"
            >新增</el-button
          >
        </el-col>
      </el-row>

      <!-- 表格数据 -->
      <el-table
        :data="tableData"
        stripe
        style="width: 100%"
        v-loading="loading"
        :height="tableHeight"
        border
      >
        <el-table-column
          type="index"
          label="序号"
          fixed="left"
          align="center"
          width="50"
        ></el-table-column>
        <el-table-column
          prop="mortgageTypeName"
          label="质押品类型"
          min-width="100"
          align="center"
        />
        <el-table-column
          prop="commodityCategoryName"
          label="商品品类"
        ></el-table-column>
        <el-table-column
          prop="processWayName"
          label="加工方式"
        ></el-table-column>
        <el-table-column
          prop="commodityTypeName"
          label="商品类型"
        ></el-table-column>
        <el-table-column
          prop="commodityName"
          label="商品名称"
          min-width="100px"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column prop="commodityNo" label="商品编码"></el-table-column>
        <el-table-column
          prop="commodityUnitName"
          label="计量单位"
        ></el-table-column>
        <el-table-column
          prop="inventoryUnitName"
          label="库存单位"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="commoditySpecifications"
          label="商品规格"
          :formatter="unitName"
          min-width="120px"
        ></el-table-column>
        <el-table-column
          prop="createUserName"
          label="创建人"
          min-width="100px"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="updateUserName"
          label="修改人"
          min-width="100px"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="createTime"
          label="创建时间"
          min-width="160"
          align="center"
          sortable
          :sort-method="(a, b) => sortBy(a, b, 'createTime')"
        ></el-table-column>
        <el-table-column
          prop="updateTime"
          label="修改时间"
          min-width="160"
          align="center"
          sortable
          :sort-method="(a, b) => sortBy(a, b, 'updateTime')"
        ></el-table-column>
        <el-table-column label="操作" align="center" fixed="right" width="190">
          <template slot-scope="scope">
            <el-button
              @click="goInfo(scope.row.commodityId)"
              type="text"
              icon="el-icon-info"
              size="mini"
              >查看</el-button
            >
            <el-button
              @click="goEdit(scope.row.commodityId)"
              type="text"
              icon="el-icon-edit"
              size="mini"
              class="btn_color_t"
              >编辑</el-button
            >
            <el-button
              @click="deleteInfo(scope.row.commodityId)"
              type="text"
              icon="el-icon-delete"
              size="mini"
              class="btn_color_f"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
    <!-- 新增 -->
    <addModel
      v-if="dialogAdd.open"
      :dialogAdd="dialogAdd"
      @close="close"
      @refresh="refresh"
    ></addModel>
  </div>
</template>
<script>
import {
  commodityList as getCommodityList,
  delCommodity,
} from "@/api/ffs/mortgage/commodityManage.js";
import addModel from "./components/addModel.vue";
import { getDicts } from "@/api/system/dict/data.js";
import { tableUi } from "@/utils/mixin/tableUi.js";
export default {
  dicts: [
    "ffs_mortgage_type",
    "ffs_commodity_category",
    "ffs_machining_type",
    "ffs_commodity_type",
  ],
  components: {
    addModel,
  },
  name: 'farmingManage',
  mixins: [tableUi],
  data() {
    return {
      //新增
      dialogAdd: {
        open: false,
        title: "",
        id: "",
        disabled: false,
      },

      time: undefined,

      queryParams: {
        mortgageType: undefined,
        searchValue: undefined,
        processWay: undefined,
        commodityCategory: undefined,
        commodityType: undefined,
        createUserName: undefined,
        areaDataRange: "",
        pageNum: 1,
        pageSize: 20,
      },
      loading: true,
      total: 0,
      tableData: [],
      pastureNatureList: [],
    };
  },
  computed: {
    unitName() {
      return (row, com, val) => {
        return (
          row.commoditySpecifications +
          row.commodityUnitName +
          "/" +
          row.inventoryUnitName
        );
      };
    },

    handelNatureType() {
      let name = "";
      return (status) => {
        if (!parseInt(status)) {
          return status;
        } else {
          this.pastureNatureList.forEach((item) => {
            if (item.dictValue == status) {
              name = item.dictLabel;
            }
          });
          return name;
        }
      };
    },
  },
  created() {
    this.getList();
    this.getPasture();
  },

  methods: {
    sortBy(a, b, key) {
      let at = a[key];
      let bt = b[key];
      return at - bt;
    },
    //调用数据字典
    getPasture() {
      getDicts("pasture_nature").then((res) => {
        if (res.code == 200) {
          this.pastureNatureList = res.data;
        }
      });
    },
    //列表查询
    getList() {
      getCommodityList(this.queryParams).then((res) => {
        if (res.code == 200) {
          this.tableData = res.result.list;
          this.total = Number(res.result.total);
          this.loading = false;
        }
      });
    },
    reset() {
      this.resetForm("queryForm");
    },
    close() {
      this.dialogAdd.open = false;
      this.dialogAdd.id = "";
      this.dialogAdd.disabled = false;
    },
    //重置
    resetQuery() {
      this.time = [];
      this.reset();
      this.handleQuery();
    },
    //刷新页面
    refresh() {
      this.getList();
    },
    //搜索
    handleQuery() {
      if (this.time && this.time.length > 0) {
        this.queryParams.createStart = this.time[0];
        this.queryParams.createEnd = this.time[1];
      } else {
        this.queryParams.createStart = "";
        this.queryParams.createEnd = "";
      }
      this.queryParams.pageNum = 1;
      this.getList();
    },

    //详情
    goInfo(id) {
      this.dialogAdd.open = true;
      this.dialogAdd.title = "详情";
      this.dialogAdd.id = id;
      this.dialogAdd.disabled = true;
    },
    //创建
    addFrom() {
      this.dialogAdd.open = true;
      this.dialogAdd.title = "新增";
    },
    //编辑
    goEdit(id) {
      this.dialogAdd.open = true;
      this.dialogAdd.title = "编辑";
      this.dialogAdd.id = id;
    },
    //删除
    deleteInfo(id) {
      this.$confirm("确定删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          delCommodity({ commodityId: id }).then((res) => {
            if (res.code == 200) {
              this.getList();
              this.$message({
                type: "success",
                message: "删除成功!",
              });
            }
          });
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss" scoped></style>
