<template>
  <div>
    <el-cascader
      style="width:100%"
      :options="options"
      :props="defaultProps"
      ref="cascader"
      @change="select"
      clearable
      v-model="list"
      popper-class="last-check"
      :disabled='disabled'
    ></el-cascader>
  </div>
</template>
<script>
import { getPostAreaList } from "@/api/system/area";
export default {
  props: {
    value: [String, Object, Array],
    disabled:{
        type:Boolean,
        default:false
    }
  },
  data() {
    return {
      defaultProps: {
        multiple: true,
        checkStrictly: true,
        expandTrigger: "click",
      },
      options: [],
      list: [],
    };
  },
  created() {
    this.getData();
    this.handelValue()
    },

  methods: {
    handelValue() {
        if (this.value) {
            let list=this.value.split(";")
            list.map(item=>{
               this.list.push(item.split(','))
            })
        }else{
            this.list=[]
        }
    },
    select() {
        let nameList=[]
        let cascader=this.$refs.cascader.getCheckedNodes()
        cascader.map(item=>{
            nameList.push(item.pathLabels.toString())
        })
        this.handelInput()
      this.$emit('getName',nameList.join(';'))
    },
    // 处理数据
    handelInput(){
        let listString=[]
        this.list.map(key=>{
            let item=key.toString()
            listString.push(item)
        })
        this.$emit('input',listString.join(';'))
    },
    getData() {
      getPostAreaList({}).then((res) => {
        if (res.code == 200) {
          this.options = res.data;
        }
      });
    },
  },
};
</script>
<style lang="scss" >
.last-check {
  li[aria-haspopup="true"] {
    .el-checkbox {
      display: none;
    }
  }
}
</style>
