<template>
  <div>
    <el-dialog
      :title="auditData.title"
      :visible.sync="auditData.dialogVisible"
      width="700px"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      @close="close"
    >
          <el-form ref="form" :model="form" :rules="rules" label-width="120px" >
        <span>企业信息</span>
        <el-divider></el-divider>
        <el-form-item label="企业名称" prop="companyName">
          <el-input v-model="form.companyName" placeholder="请输入企业名称" :disabled="true"/>
        </el-form-item>
        <el-form-item label="企业类型" prop="companyType">
          <el-select v-model="form.companyType" placeholder="请选择企业类型" style="width:100%" :disabled="true">
            <el-option
              v-for="dict in dict.type.enterprise_type"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-row :span="24">
          <el-col :span="12">
            <el-form-item label="企业证件类型" prop="companyCertType">
              <el-select v-model="form.companyCertType" placeholder="请选择企业证件类型" style="width:100%" :disabled="true">
                <el-option key="1" label="统一社会信用代码" value="1" />
                <el-option key="2" label="营业执照" value="2" />
                <el-option key="3" label="组织机构代码" value="3" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="营业执照号" prop="businessLicenseNo">
              <el-input
                v-model="form.businessLicenseNo"
                placeholder="请输入营业执照号"
                :disabled="true"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="营业执照" prop="businessLicense">
              <image-preview
          v-if="form.businessLicense"
            :src="(form.businessLicense)"
            :width="100"
          ></image-preview>
           <!-- <image-upload
              :disabled="false"
              v-model="form.businessLicense"
              :limit="3"
            ></image-upload> -->
        </el-form-item>

        <el-row :span="24">
          <el-form-item label="企业地址" prop="address">
            <el-cascader
            :options="areaProps"
            style="width: 100%"
            v-model="partnerAddressPath"
            ref="myCascader"
            :disabled="true"
          >
          </el-cascader>
          </el-form-item>
        </el-row>
        <el-row :span="24">
          <el-form-item label="详细地址" prop="detailAddress">
            <el-input v-model="form.detailAddress" placeholder="请输入详细地址" :disabled="true"/>
          </el-form-item>
        </el-row>

        <span>法人信息</span>
        <el-divider></el-divider>

        <el-row :span="24">
          <el-col :span="12">
            <el-form-item label="法人证件类型" prop="corprateIdType">
              <el-select v-model="form.corprateIdType" placeholder="请选择法人证件类型" style="width:100%" :disabled="true">
                <el-option key="0" label="身份证" value="0" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="真实姓名" prop="realName">
              <el-input v-model="form.realName" placeholder="请输入真实姓名" :disabled="true" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :span="24">
          <el-col :span="12">
            <el-form-item label="身份证号" prop="idCard">
              <el-input v-model="form.idCard" placeholder="请输入身份证号" :disabled="true"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号" prop="phoneNumber">
              <el-input v-model="form.phoneNumber" placeholder="请输入手机号" :disabled="true"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="12">
            <el-form-item label="身份证正面照" prop="corprateIdFrontal">
                 <image-preview
          v-if="form.corprateIdFrontal"
            :src="form.corprateIdFrontal"
            :width="100"
          ></image-preview>
              <!-- <image-upload
              :disabled="false"
              v-model="form.corprateIdFrontal"
              :limit="1"
            ></image-upload> -->
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="身份证反面照" prop="corprateIdReverse">
                       <image-preview
          v-if="form.corprateIdReverse"
            :src="form.corprateIdReverse"
            :width="100"
          ></image-preview>
                <!-- <image-upload
              :disabled="false"
              v-model="form.corprateIdReverse"
              :limit="1"
            ></image-upload> -->
            </el-form-item>
          </el-col>
        </el-row>

        <span>企业管理员信息</span>
        <el-divider></el-divider>
        <el-row :span="24">
          <el-col :span="12">
            <el-form-item prop="createBy" label="用户账号">
              <el-select style="width:100%" v-model="form.createBy"
              :disabled="true"
                filterable remote reserve-keyword
                placeholder="请输入用户名/手机号码/昵称关键词"
                :remote-method="searchUserList">
                <el-option v-for="item in userList" :key="item.userId" :label="item.nickName" :value="item.userId" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="corprateName" label="真实姓名">
              <el-input v-model="form.corprateName" placeholder="请输入真实姓名" :disabled="true" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="12">
            <el-form-item prop="corprateIdNo" label="身份证号码">
              <el-input v-model="form.corprateIdNo" placeholder="请输入身份证号码" :disabled="true"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="adminPhonenumber" label="手机号码">
              <el-input v-model="form.adminPhonenumber" :disabled="true" placeholder="请输入手机号码" />
            </el-form-item>
          </el-col>
        </el-row>


      </el-form>
      <span slot="footer" class="dialog-footer" v-show="auditData.disabled">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="Submit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { areaData } from "@/utils/mixin/area.js";
import { listRoleNoPage } from "@/api/system/role";
import { listSimpleUser } from "@/api/system/user";
import { getEnterprise, approveEnterprise } from "@/api/system/enterprise";
export default {
  mixins: [areaData],
  props: {
    // 是否禁用输入，查看详情的时候禁用
    auditData: {
      type: Object,
      default: {},
    },
  },
  dicts: ["enterprise_type", "approve_status"],
  data() {
    return {
      status:true,
      address: "",
      userList: [],
      partnerAddressPath:[],
      form: {

      },
      listRole: {}, //角色列表
      srcList: [],
      // 表单校验
      rules: {
        approveStatus: [
          { required: true, message: "请选择审核结论", trigger: "blur" }
        ],
        approveOption: [
          { required: false, message: "请输入审核意见", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getData();
  },
  methods: {
    searchUserList(query) {
      let searchParams = { phonenumber: query };
      listSimpleUser(searchParams).then((response) => {
        this.userList = response.data;
      });
    },

    changApprove(){
            this.form.approveStatus === 2? (this.rules.approveOption[0].required=true):this.rules.approveOption[0].required=false
             this.$refs.form.clearValidate();
    },
    //查看详情
    getData() {
      getEnterprise(this.auditData.tenantId).then((res) => {
        if (res.code == 200) {
          this.form = res.data;
          this.srcList.push(this.form.businessLicense);
        //   this.address=`${res.data.provinceName}${res.data.cityName}${res.data.countyName}`
          this.partnerAddressPath=[
            res.data.provinceId,
              res.data.cityId,
              res.data.countyId,]
          let userInfo = [{
            "userId": res.data.createBy,
            "phonenumber": res.data.adminPhonenumber,
            "nickName": res.data.nickName
          }];
          this.userList = userInfo;
          if(res.data.approveStatus==0){
              this.status=false
          }
        }
      });
    },
    //角色查询
    listRoleNoPage() {
      listRoleNoPage().then((response) => {
        this.listRole = response.data;
      });
    },
    close(value) {
      this.$emit("close", value);
    },

    //审核
    Submit() {
        if(this.form.approveStatus==0){
            this.form.approveStatus=''
        }
      this.$refs["form"].validate((valid) => {
        if (valid) {
          approveEnterprise({
            tenantId: this.auditData.tenantId,
            approveStatus: this.form.approveStatus,
            approveOption: this.form.approveOption,
          }).then((res) => {
            if (res.code == 200) {
              this.$modal.msgSuccess(res.msg);
            } else {
              this.$modal.msgError(res.msg);
            }
            this.close(1);
            this.$emit("refresh");
          });
        }
      });
    },
  },
};
</script>

<style lang="scss" >
</style>
