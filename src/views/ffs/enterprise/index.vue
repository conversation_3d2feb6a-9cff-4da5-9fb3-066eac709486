<template>
  <div class="app-container">
    <el-card class="mb10 form_box" shadow="never" ref="formBox">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="120px">
    <el-row class="form_row">
     <el-col class="form_col">
      <el-form-item label="企业类型" prop="companyType">
        <el-select v-model="queryParams.companyType" placeholder="请选择企业类型" clearable>
          <template v-if="companyTypeParamsList.length>0">
            <el-option v-for="dict in dict.type.enterprise_type" v-if="companyTypeParamsList.indexOf(dict.value) >= 0" :key="dict.value" :label="dict.label" :value="dict.value"/>
          </template>
          <template v-else>
            <el-option v-for="dict in dict.type.enterprise_type" :key="dict.value" :label="dict.label" :value="dict.value"/>
          </template>
        </el-select>
      </el-form-item>
      <el-form-item label="企业名称" prop="companyName">
        <el-input v-model="queryParams.companyName" placeholder="请输入企业名称" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="真实姓名" prop="realName">
        <el-input v-model="queryParams.realName" placeholder="请输入真实姓名" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="身份证号" prop="idCard">
        <el-input v-model="queryParams.idCard" placeholder="请输入身份证号" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="手机号" prop="phoneNumber">
        <el-input v-model="queryParams.phoneNumber" placeholder="请输入手机号" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="银行等级" prop="level">
        <el-select v-model="queryParams.level" placeholder="选择银行等级进行搜索" clearable>
          <el-option v-for="dict in dict.type.enterprise_level" :key="dict.value" :label="dict.label" :value="dict.value"/>
        </el-select>
      </el-form-item>
      </el-col>
      </el-row>
      <el-row style="margin-left: 120px;">
            <el-col >
            <el-form-item >
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery(0)">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              <template v-if="toggleSearchDom">
                <el-button type="text" @click="packUp" >
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <i
                  :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"
                ></i>
              </el-button>
              </template>

            </el-form-item>
          </el-col>
        </el-row>
    </el-form>
    </el-card>
    <el-card shadow="never" class="list_table">
        <el-row class="mb8 form_btn">
      <el-col  class="form_btn_col">
        <template v-if="companyTypeBtn==11">
            <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['system:enterpriseWatch:add']">新增</el-button>
        </template>
        <template v-else>
            <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['system:enterprise:add']">新增</el-button>
        </template>

        </el-col>
        </el-row>
    <!-- <el-row :gutter="10" class="mb8"> -->

<!--      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['system:enterprise:remove']">删除</el-button>
      </el-col>-->
      <!-- <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row> -->

    <el-table v-loading="loading" :data="enterpriseList" @selection-change="handleSelectionChange" border :height="tableHeight">
      <el-table-column type="selection" width="55" align="center"  fixed="left"/>
      <el-table-column label="企业名称" align="left" prop="companyName" width="180" show-overflow-tooltip  fixed="left"/>
      <el-table-column label="企业类型" align="left" prop="companyType" min-width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.enterprise_type" :value="scope.row.companyType"/>
        </template>
      </el-table-column>
      <el-table-column label="法人真实姓名" align="left" min-width="110" prop="realName" show-overflow-tooltip/>
      <el-table-column label="法人身份证号" align="center"  width="165">
        <template slot-scope="scope">{{showCarId(scope.row.idCard)}}</template>
      </el-table-column>
      <el-table-column label="法人手机号" align="center" prop="phoneNumber" width="110"/>
      <el-table-column label="营业执照号" align="center" prop="businessLicenseNo" width="200"/>
      <el-table-column label="营业执照" align="center" prop="businessLicense">
        <template slot-scope="scope">
          <div @click="onClick(picPath(scope.row.businessLicense.split(',')[0]))" v-if="scope.row.businessLicense">
            <el-image style="width: 50px; height: 50px" :src="picPath(scope.row.businessLicense.split(',')[0])" />
            <el-image-viewer v-if="showViewer" :on-close="() => {showViewer = false;}" :url-list="imgList"/>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="企业管理员昵称" width="120" align="left" prop="nickName"  show-overflow-tooltip/>
      <el-table-column label="企业管理员账号" width="130" align="left" prop="userName" show-overflow-tooltip />
      <el-table-column label="所属省" width="150" align="left" prop="provinceName" />
      <el-table-column label="所属市" width="150" align="left" prop="cityName" />
      <el-table-column label="所属区/县" width="150" align="left" prop="countyName" show-overflow-tooltip/>
      <el-table-column label="详细地址" align="left" prop="detailAddress" width="200" show-overflow-tooltip/>
      <el-table-column label="创建时间" align="center" prop="createTime" width="140">
        <template slot-scope="scope">
          <span v-if="scope.row.createTime">{{ parseTime(scope.row.createTime, "{y}-{m}-{d} {hh}:{mm}") }}</span>
          <span v-if="!scope.row.createTime">--</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="240" fixed="right">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-info" @click="details(scope.row)">查看</el-button>
          <template v-if="companyTypeBtn==11">
            <el-button class="btn_color_t" size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"  v-hasPermi="['system:enterpriseWatch:edit']" >编辑</el-button>
            <el-button   class="btn_color_four" size="mini" type="text" icon="el-icon-user" v-hasPermi="['system:enterpriseWatch:viewMembers']" @click="enterpriseUserList(scope.row)">成员</el-button>
            <el-button size="mini" type="text" icon="el-icon-s-check" v-hasPermi="['system:enterpriseWatch:authorize']" @click="authorize(scope.row)">授权</el-button>
        </template>
        <template v-else>
            <el-button class="btn_color_t" size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"  v-hasPermi="['system:ffs:enterprise:edit']" >编辑</el-button>
            <el-button   class="btn_color_four" size="mini" type="text" icon="el-icon-user" v-hasPermi="['system:ffs:enterprise:viewMembers']" @click="enterpriseUserList(scope.row)">成员</el-button>
            <el-button size="mini" type="text" icon="el-icon-s-check" v-hasPermi="['system:ffs:enterprise:authorize']" @click="authorize(scope.row)">授权</el-button>
        </template>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList"/>
    </el-card>
    <!-- 添加或修改企业认证对话框 -->
    <el-drawer :title="title" :visible.sync="open" v-if="open" size="50%" append-to-body @close="cancel" :close-on-click-modal="false" :wrapperClosable="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" style="padding:0 30px 0 30px">
        <span>企业信息</span>
        <el-divider></el-divider>
        <el-form-item label="企业名称" prop="companyName">
          <el-select
          style="width:100%"
            v-model="form.companyName"
            filterable
            clearable
            allow-create
            remote
            :remote-method="selectCompanyName"
            default-first-option
            placeholder="请输入企业名称">
            <el-option
            v-for="(item,index) in options"
            :key="index"
            :label="item.subBranchName"
            :value="item.subBranchName">
            </el-option>
         </el-select>
        </el-form-item>
        <el-form-item label="企业类型" prop="companyType">
          <el-select v-model="form.companyType" placeholder="请选择企业类型" style="width:100%" :disabled="isEdit">
            <template v-if="companyTypeParamsList.length>0">
              <el-option v-for="dict in dict.type.enterprise_type" v-if="companyTypeParamsList.indexOf(dict.value) >= 0" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
            </template>
            <template v-else>
              <el-option v-for="dict in dict.type.enterprise_type" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
            </template>
          </el-select>
        </el-form-item>
        <el-form-item label="银行等级" prop="level" v-if="form.companyType==3">
          <el-select v-model="form.level" placeholder="请选择企业类型" style="width:100%" @change="selectBank">
            <el-option v-for="dict in dict.type.enterprise_level" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"  ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="上级银行" prop="parentId" v-if="form.level==3&&(companyTypeBtn!=11)">
          <treeselect v-model="form.parentId" :options="deptOptions" :normalizer="normalizer" placeholder="选择上级银行" />
        </el-form-item>

        <el-row :span="24">
          <el-col :span="12">
            <el-form-item label="企业证件类型" prop="companyCertType">
              <el-select v-model="form.companyCertType" placeholder="请选择企业证件类型" style="width:100%" :disabled="isEdit">
                <el-option key="1" label="统一社会信用代码" value="1" />
                <el-option key="2" label="营业执照" value="2" />
                <el-option key="3" label="组织机构代码" value="3" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="营业执照号" prop="businessLicenseNo">
              <el-input v-model="form.businessLicenseNo" placeholder="请输入营业执照号" :disabled="isEdit"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="营业执照" prop="businessLicense">
            <image-upload :disabled="false" v-model="form.businessLicense" :limit="1" />
        </el-form-item>

        <el-row :span="24">
          <el-form-item label="企业地址" prop="address">
            <el-cascader :disabled="isEdit"  :options="areaProps" :clearable="true" style="width:100%" v-model="address" @change="handleAreaChange" ref="myCascader" > </el-cascader>
          </el-form-item>
        </el-row>
        <el-row :span="24">
          <el-form-item label="详细地址" prop="detailAddress">
            <el-input v-model="form.detailAddress" placeholder="请输入详细地址" :disabled="isEdit"/>
          </el-form-item>
        </el-row>
        <el-row :span="24">
          <el-form-item label="经纬度查询：" prop="address">
            <div style="margin-top:-10px">
              <map-control @position="mapPosition" :formData="formData" placeholder="请输入完整地址查询经纬度" />
            </div>
          </el-form-item>
        </el-row>
        <el-row :span="24">
          <el-form-item label="运营区域" prop="operatingAreaId">
            <enterprise-area v-model="form.operatingAreaId" @getName="getName" style="width: 100%;" v-if="open" :disabled="disabledCascader" />
          </el-form-item>
        </el-row>
        <span>法人信息</span>
        <el-divider></el-divider>

        <el-row :span="24">
          <el-col :span="12">
            <el-form-item label="法人证件类型" prop="corprateIdType">
              <el-select v-model="form.corprateIdType" placeholder="请选择法人证件类型" style="width:100%" >
                <el-option key="0" label="身份证" value="0" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="真实姓名" prop="realName">
              <el-input v-model="form.realName" placeholder="请输入真实姓名" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :span="24">
          <el-col :span="12">
            <el-form-item label="身份证号" prop="idCard">
              <el-input v-model="form.idCard" placeholder="请输入身份证号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号" prop="phoneNumber">
              <el-input v-model="form.phoneNumber" placeholder="请输入手机号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="12">
            <el-form-item label="身份证正面照" prop="corprateIdFrontal">
              <image-upload :disabled="false" v-model="form.corprateIdFrontal" :limit="1" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="身份证反面照" prop="corprateIdReverse">
              <image-upload :disabled="false" v-model="form.corprateIdReverse" :limit="1" />
            </el-form-item>
          </el-col>
        </el-row>

        <span>企业管理员信息</span>
        <el-divider></el-divider>
        <el-row :span="24">
          <el-col :span="8">
            <el-form-item prop="createBy" label="用户账号">
              <el-select :disabled="isEdit" style="width:80%" v-model="form.createBy" filterable remote reserve-keyword placeholder="输入账号/手机号码搜索" @change="selectUserInfo" :remote-method="searchUserList" :loading="loading">
                <el-option v-for="item in userList" :key="item.userId" :label="item.nickName" :value="item.userId" />
              </el-select>
              <el-popover v-if="isEdit == false" style="padding-left: 10px" v-model="popover.visible" placement="left" width="400" trigger="click">
                <el-row>
                  <el-form-item label="用户账号" prop="userName"><el-input v-model="userData.userName" placeholder="请输入用户名称" maxlength="30" /></el-form-item>
                  <el-form-item label="联系电话" prop="contactNumber"><el-input v-model="userData.contactNumber" placeholder="联系电话" maxlength="11" /></el-form-item>
                  <el-form-item label="真实姓名" prop="nickName"><el-input v-model="userData.nickName" placeholder="请输入真实姓名" maxlength="60" /></el-form-item>
                  <el-form-item label="用户密码" prop="password"><el-input v-model="userData.password" placeholder="请输入用户密码" type="password" maxlength="20" show-password auto-complete="new-password"/></el-form-item>
                </el-row>
                <el-row style="color: red;text-align: right;margin: -10px 0 10px 0">
                  {{ message.tips }}
                </el-row>
                <el-row style="text-align: right">
                  <el-button type="primary" @click="addEnterpriseUser">新 建</el-button>
                </el-row>
                <el-button type="text" slot="reference">新建</el-button>
              </el-popover>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="corprateName" label="真实姓名">
              <el-input v-model="form.corprateName" placeholder="请输入真实姓名" :disabled="true"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="corprateName" label="电话号码">
              <el-input v-model="form.contactNumber" placeholder="" :disabled="true"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="24">
            <el-form-item prop="corprateIdNo" label="身份证号码">
              <el-input v-model="form.corprateIdNo" placeholder="请输入身份证号码" :disabled="true"/>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item prop="sysUser.phonenumber" label="手机号码">
              <el-input v-model="form.sysUser.phonenumber" :disabled="true" placeholder="请输入手机号码" />
            </el-form-item>
          </el-col> -->
        </el-row>
      </el-form>
      <el-footer style="border-top: 1px solid #DCDFE6">
        <div style="text-align: right;padding-top:10px">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </el-footer>
    </el-drawer>
    <model :auditData="auditData" v-if="auditData.dialogVisible" @close="close" @refresh="refresh"></model>
    <el-drawer v-if="enterpriseUserListDrawer" :title="selectEnterpriseName" :visible.sync="enterpriseUserListDrawer" size="70%">
      <enterprise-user :enterpriseTenant="enterpriseTenant"/>
    </el-drawer>
    <el-drawer :title="roleTitle" v-if="roleOpen" :visible.sync="roleOpen" size="45%" append-to-body :close-on-click-modal="false">
      <enterprise-role @roleCancel="roleCancel" :roleId="roleId" />
    </el-drawer>
  </div>
</template>

<script>
import enterpriseArea  from './components/enterpriseArea.vue'
import { addUser,bankinfo } from "@/api/system/user";
import ElImageViewer from "element-ui/packages/image/src/image-viewer";
import enterpriseUser from '@/views/system/enterprise/components/enterpriseUser';
import {
  listEnterprise,
  listNoPageEnterprise,
  getEnterprise,
  delEnterprise,
  addEnterprise,
  updateEnterprise,
  approveEnterprise,
  ADDsuperviseEnterprise
} from "@/api/system/enterprise";
import { listRoleNoPage } from "@/api/system/role";
import { basicPath2 } from "@/api/base";
import { getToken } from "@/utils/auth";
import Model from "./components/Model";
import { listSimpleUser,getUser } from '@/api/system/user';
import { areaData } from "@/utils/mixin/area.js";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { getDicts } from "@/api/system/dict/data.js";
import { tableUi } from "@/utils/mixin/tableUi.js";
import EnterpriseRole from '@/views/system/enterprise/components/enterpriseRole'
import MapControl from '@/views/ffs/mortgage/supervision/components/addForm/MapControl.vue'
export default {
  mixins:[areaData,tableUi],
  name: "Enterprise",
  dicts: ["enterprise_type", "enterprise_approval_status","enterprise_level"],
  components: {
    MapControl,
    Model,
    enterpriseArea,
    ElImageViewer,Treeselect,enterpriseUser,EnterpriseRole
  },
  data() {
    return {
        disabledCascader:false,
      roleId: undefined,
      type:undefined,
      roleOpen:false,
      roleTitle:'',
      enterpriseUserListDrawer:false,
      enterpriseTenant:null,
      selectEnterpriseName:"企业成员列表",
      platformId:'',
      defaultBank:[],
      isEdit: false,
      deptOptions: [],
      userData:{
        userName:"",
        contactNumber:"",
        nickName:"",
        password:""
      },
      options:[],
      message:{
        tips: ""
      },
      popover: {
        visible: false
      },
      companyTypeParamsList:[],
      showViewer: false,
      imgList:[],
      address:[],//省级id
      addressName:[],//省级名称
      updateAction: process.env.VUE_APP_BASE_API + basicPath2 + "files/obs/fileUpload",
      dialogImageUrl: "",
      dialogVisible: false,
      disabled: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      headers: { Authorization: getToken() },
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 企业认证表格数据
      enterpriseList: [],
      listRole: {}, //角色列表
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      userList:[],//用户名称
      companyTypeBtn:null,
      formData: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        params:{},
        companyType: null,
        companyTypeList: [],
        companyName: null,
        provinceId: null,
        cityId: null,
        countyId: null,
        realName: null,
        idCard: null,
        phoneNumber: null,
        companyAuthStatus: null,
        approveStatus: null,
      },
      //企业审核
      auditData: {
        dialogVisible: false,
        disabled: false,
      },
      // 表单参数
      form: {
        sysUser:{},
        businessLicense:'',
        realName:'',
        latitudeLongitude: ''
      },
      // 表单校验
      rules: {
        companyType: [{ required: true, message: "企业类型不能为空", trigger: "change" }],
        companyName: [{ required: true, message: "企业名称不能为空", trigger: "blur" }],
        businessLicenseNo: [{ required: true, message: "营业执照号不能为空", trigger: "blur" }],
        businessLicense: [{ required: true, message: "营业执照不能为空", trigger: "blur" }],
        detailAddress: [{ required: true, message: "请输入企业的详细地址", trigger: "blur" }],
        realName: [{ required: false, message: "真实姓名不能为空", trigger: "blur" }],
        level: [{ required: true, message: "请选择银行等级", trigger: "blur" }],
        idCard: [
          { required: false, message: "身份证号不能为空", trigger: "blur" },
          {
            pattern: /^(\d{18}|\d{17}X|\d{17}x)$/,
            message: "请输入正确的身份证号码",
            trigger: "blur"
          },
        ],
        phoneNumber: [
          { required: false, message: "手机号不能为空", trigger: "blur" },
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur"
          },
        ],
        companyAuthStatus: [
          { required: true, message: "企业认证状态不能为空", trigger: "blur" },
        ],
        delFlag: [
          { required: true, message: "删除标识不能为空", trigger: "blur" },
        ],
        approveId: [
          { required: true, message: "审核人不能为空", trigger: "blur" },
        ],
        approveTime: [
          { required: true, message: "审核时间不能为空", trigger: "blur" },
        ],
        corprateIdFrontal: [
          { required: false, message: "请上传法人身份证正面", trigger: "blur" },
        ],
        corprateIdReverse: [
          { required: false, message: "请上传法人身份证反面", trigger: "blur" },
        ],
        companyCertType:[
          { required: true, message: "请选择企业证件类型", trigger: "blur" },
        ],
        createBy:[
          { required: true, message: "请选择运营人员", trigger: "blur" },
        ],
        operatingAreaId:[
        { required: true, message: "请选择运营区域", trigger: "blur" },
        ],
        // corprateName:[
        //   { required: true, message: "请输入运营人员真实姓名", trigger: "blur" },
        // ],
        // corprateIdNo:[
        //   { required: true, message: "请输入运营人员身份证号码", trigger: "blur" },
        //   {
        //     pattern: /^(\d{18}|\d{17}X|\d{17}x)$/,
        //     message: "请输入正确的身份证号码",
        //     trigger: "blur"
        //   },
        // ]
      },
    };
  },
  computed:{
    showCarId(){
      return (p)=>{
        return this.carId(p)
      }
    }
  },
  created() {
    this.platformId=this.$store.state.user.user.platformId;
    let companyType  = this.$route.query.companyType;
    this.companyTypeBtn=companyType
    if(companyType && companyType.length>0){
      let strings  = companyType.split(",");
      this.companyTypeParamsList = strings;
      this.queryParams.companyTypeList = this.companyTypeParamsList;
    }
    this.type= this.$route.query.type
    this.getDictsData()
  },
  watch:{
    'form.realName':function(val){
        if(this.form.realName){
            this.rules.corprateIdFrontal[0].required=true
            this.rules.corprateIdReverse[0].required=true
        }else{
            this.rules.corprateIdFrontal[0].required=false
            this.rules.corprateIdReverse[0].required=false
        }
    }
  },
  methods: {
    roleCancel(){
      this.roleOpen = false;
    },
    authorize(row){
      console.log(JSON.stringify(row));
      this.roleTitle = row.companyName;
      this.roleOpen = true;
      this.roleId = row.tenantId;
    },
    enterpriseUserList(data){
      this.selectEnterpriseName = data.companyName;
      this.enterpriseUserListDrawer = true;
      this.enterpriseTenant = data;
    },
    //查询字典
    getDictsData(){
        getDicts('operation_center_default_bank').then(res=>{
            if(res.code==200){
                this.defaultBank=res.data
                this.getList();
            }
        })
    },
    /** 转换银行数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.tenantId,
        label: node.companyName === undefined ? "请选择上级银行": node.companyName,
        children: node.children,
        isDisabled: (node.tenantId === this.form.tenantId)?true:false
      };
    },
    //查询银行名称
    selectCompanyName(name){
        bankinfo({subBranchName:name}).then(res=>{
            this.options=res.result.list
        })
    },
    addEnterpriseUser(){
      let userData = this.userData;
      if(!userData.userName){this.message.tips = "请输入用户账号";return false;}
      if(!userData.nickName){this.message.tips = "请输入真实姓名";return false;}
      if(!userData.password){this.message.tips = "请输入登录密码";return false;}
      addUser(userData).then(response => {
        this.userList.push(response.data);
        this.popover.visible = false;
        this.form.createBy = response.data.userId;//选中默认
        this.$modal.msgSuccess("新增成功");
      });
    },
    onClick(url){
      this.imgList=[]
      let imgUrl=url.split(',')
      this.imgList=[...imgUrl]
      this.showViewer=true
    },
    //选中的地址
    handleAreaChange(){
      let areaCodeData  = this.$refs['myCascader'].getCheckedNodes()[0];
      if(areaCodeData){
            let provinceId = areaCodeData.data.provinceId;
            let areaId = areaCodeData.data.areaId;
            let cityId = areaCodeData.data.cityId;
            this.form.areaCodeSplit = provinceId+"," + areaId + "," + cityId;
            this.addressName = areaCodeData.pathLabels
            this.form.provinceName = this.addressName[0]
            this.form.cityName = this.addressName[1]
            this.form.countyName = this.addressName[2]
      }

    },
    refresh(){
      this.getList()
    },
    /** 查询企业认证列表 */
    getList() {
      this.loading = true;
      this.queryParams.params.excludeEnterprise = this.handelPlatformId()
      this.queryParams.params.isBank = 'yes';
      listEnterprise(this.queryParams).then((response) => {
        this.enterpriseList = response.rows;
        this.total = parseInt(response.total);
        this.loading = false;
      });
    },
    handelPlatformId(){
        let value=''
        this.defaultBank.forEach(item=>{
            if(item.dictLabel==this.platformId){
                value=item.dictValue
            }
        })
        return value
    },
    // 取消按钮
    cancel() {
      this. address=[],//省级id
        this.addressName=[],//省级名称
        this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        tenantId: null,
        companyType: null,
        companyName: null,
        provinceId: null,
        cityId: null,
        countyId: null,
        provinceName: null,
        cityName: null,
        countyName: null,
        detailAddress: null,
        businessLicenseNo: null,
        businessLicense: null,
        realName: null,
        idCard: null,
        phoneNumber: null,
        companyAuthStatus: 0,
        delFlag: null,
        approveId: null,
        approveTime: null,
        approveStatus: 0,
        approveOption: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        password: null,
        corprateIdFrontal:'',
        corprateIdReverse:'',
        address:[],
        level: null,
        companyCertType: '1',
        corprateIdType:'0',
        corprateIdNo: '', //运营人员身份证号码
        corprateName: '', //运营人员的真实姓名
        operatingAreaName:'',
        operatingAreaId:'',
        sysUser:{
          phonenumber:'',  //运营人员电话号码
          corprateIdNo: '', //运营人员身份证号码
          corprateName: '' //运营人员的真实姓名
        }
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery(type) {
      this.queryParams.pageNum = 1;
      if(type !== 1){
        this.queryParams.companyTypeList = [];
      }
      if(this.queryParams.companyType){
        this.queryParams.companyTypeList.push(this.queryParams.companyType);
      }else{
        this.queryParams.companyTypeList = this.companyTypeParamsList;
      }
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.companyTypeList = this.companyTypeParamsList;
      this.handleQuery(1);
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.tenantId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    getName(value){
        this.form.operatingAreaName = value
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.isEdit = false;
      this.reset();
      this.listRoleNoPage();
      this.userList=[]
      this.open = true;
      this.title = "添加企业认证";

      this.rules.address = [{ required: true, message: "请选择企业所属区域", trigger: "blur" }];
        if(this.companyTypeBtn==11){
            const userinfo = JSON.parse(window.localStorage.getItem("USERINFO"));
            let operatingAreaId= userinfo.areaDataRange.split(';')
            let operArry=[]
            operatingAreaId.forEach(item=>{
               let arrItem= item.split(',')
               arrItem.splice(0, 1)
               operArry.push(arrItem)
            })
            this.form.operatingAreaId=operArry.join(';')
            this.disabledCascader=true
        }else{
            this.disabledCascader=false
        }
      if(this.queryParams.companyType){
        this.queryParams.companyTypeList.push(this.queryParams.companyType);
      }else{
        this.queryParams.companyTypeList = this.companyTypeParamsList;
      }

    //   listNoPageEnterprise(this.queryParams).then(response => {
    //     this.deptOptions = this.handleTree(response.data, "tenantId");
    //   });
    },
    //选择银行类型
    selectBank(){
        let obj={
            companyTypeList:this.queryParams.companyTypeList,
        }
        if(this.form.level==1){
            return
        }
        if(this.form.level==2){
            this.form.parentId = this.handelPlatformId()
            return
        }
        if(this.form.level==3){
            obj.level=2
        }
        listNoPageEnterprise(obj).then(response => {
            this.deptOptions = this.handleTree(response.data, "tenantId");
        });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.rules.address = [{ required: false, message: "请选择企业所属区域", trigger: "blur" }];
      this.isEdit = true;
      this.reset();
      this.listRoleNoPage();
      const id = row.tenantId || this.ids;
      getEnterprise(id).then((response) => {
        this.form = response.data;
        this.formData = {
          address: `${response.data.provinceName}${response.data.cityName}${response.data.countyName}`,
          addrPoint: {
            lng: response.data.latitudeLongitude?.split(",")[0],
            lat: response.data.latitudeLongitude?.split(",")[1],
          }
        }
        console.log(this.formData)
        this.userList = [];
        this.userList.push(response.data.sysUser);
        this.address = [];
        this.address = [response.data.provinceId,response.data.cityId,response.data.countyId];
        this.open = true;
        this.title = "修改企业认证";
        this.selectBank()
      });
      if(this.queryParams.companyType){
        this.queryParams.companyTypeList.push(this.queryParams.companyType);
      }else{
        this.queryParams.companyTypeList = this.companyTypeParamsList;
      }
      let obj=JSON.parse(JSON.stringify(this.queryParams))
      if(this.companyTypeBtn==11){
            const userinfo = JSON.parse(window.localStorage.getItem("USERINFO"));
            let operatingAreaId= userinfo.areaDataRange.split(';')
            let operArry=[]
            operatingAreaId.forEach(item=>{
               let arrItem= item.split(',')
               arrItem.splice(0, 1)
               operArry.push(arrItem)
            })
            this.form.operatingAreaId=operArry.join(';')
            this.disabledCascader=true
        }else{
            this.disabledCascader=false
        }
    //   listNoPageEnterprise(obj).then(response => {
    //     this.deptOptions = this.handleTree(response.data, "tenantId");
    //   });
    },
    /**企业审核 */
    handleApprove(data) {
      this.auditData.disabled = true;
      this.auditData.dialogVisible = true;
      this.auditData.title = "企业审核";
      this.auditData.tenantId = data.tenantId;
    },
    /**查看详情 */
    details(data) {
      this.auditData.disabled = false;
      this.auditData.dialogVisible = true;
      this.auditData.title = "详情";
      this.auditData.tenantId = data.tenantId;
    },
    close(value) {
      this.auditData.disabled = false;
      this.auditData.dialogVisible = false;
      //刷新列表
      if (value == 1) {
        this.getList();
      }
    },
    searchUserList(query){
      let searchParams = { "phonenumber":query };
      listSimpleUser(searchParams).then(response => {
        this.userList = response.data;
      });
    },
    /**处理省级地区 */
    handleAddress(){
      this.form.provinceId = this.address[0]
      this.form.cityId = this.address[1]
      this.form.countyId = this.address[2]
      this.form.address = this.address;
    },
    /** 提交按钮 */
    submitForm() {
      this.handleAreaChange()
      this.handleAddress()
      console.log(this.form)
      if (!this.form.latitudeLongitude) {
        this.$message({
          message: "请选择经纬度",
          type: "error",
        });
        return false;
      }
      this.$refs["form"].validate((valid) => {
        this.form.sysUser.corprateIdNo = this.form.corprateIdNo;
        this.form.sysUser.corprateName = this.form.corprateName;
        if (valid) {
          if (this.form.latitudeLongitude == "") {
            this.$message({
              message: "请选择经纬度",
              type: "error",
            });
            return false;
          }
          if (this.form.tenantId != null) {
            updateEnterprise(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
                addEnterprise(this.form).then((response) => {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
                });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除企业认证编号为"' + ids + '"的数据项？')
        .then(function () {
          return delEnterprise(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/enterprise/export",
        {
          ...this.queryParams,
        },
        `enterprise_${new Date().getTime()}.xlsx`
      );
    },
    listRoleNoPage() {
      listRoleNoPage().then((response) => {
        this.listRole = response.data;
      });
    },
    // handleRemove(file) {
    //   console.log(file);
    // },
    // handlePictureCardPreview(file) {
    //   this.dialogImageUrl = file.url;
    //   this.dialogVisible = true;
    // },
    // handleDownload(file) {
    //   console.log(file);
    // },
    // handleImageSuccess(file,type) {
    //   let result  = file.response.result;
    //   if("businessLicense" === type){
    //     this.form.businessLicense = result[0].objectUrl;
    //   }
    //   if("corprateIdFrontal" === type){
    //     this.form.corprateIdFrontal = result[0].objectUrl;
    //   }
    //   if("corprateIdReverse" === type){
    //     this.form.corprateIdReverse = result[0].objectUrl;
    //   }
    // },
    selectUserInfo(data){
      getUser(data).then(response => {
        this.form.sysUser.phonenumber = response.data.phonenumber;
        this.form.sysUser.corprateIdNo = response.data.corprateIdNo;
        this.form.corprateIdNo = response.data.corprateIdNo;
        this.form.sysUser.corprateName = response.data.corprateName;
        this.form.corprateName = response.data.corprateName;
        this.form.contactNumber = response.data.contactNumber;
      });
    },
    mapPosition(po) {
      console.log(po);
      this.form.latitudeLongitude = po.position.addressLoglat
    },
  },
};
</script>
<style scoped>
/deep/ .el-upload--picture-card {
  background-color: #fbfdff;
  border: 1px dashed #c0ccda;
  border-radius: 6px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 80px;
  height: 80px;
  cursor: pointer;
  line-height: 80px;
  vertical-align: top;
}

/deep/ .el-upload-list__item {
  overflow: hidden;
  background-color: #fff;
  border: 1px solid #c0ccda;
  border-radius: 6px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 80px;
  height: 80px;
  margin: 0 8px 8px 0;
  display: inline-block;
}
/deep/ .el-divider--horizontal {
  display: block;
  height: 1px;
  width: 100%;
  margin: 5px 0 20px 0;
}
</style>
