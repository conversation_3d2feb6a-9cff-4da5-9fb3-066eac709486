import Layout from "@/layout";

// 牛超市
export const cowMarket = [
  {
    path: "/cow_market/index",
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/cow_market/operation/storeDetail/:id(\\d+)',
        component: () => import('@/views/xmb/cow_market/detail.vue'),
        name: 'storeDetail',
        meta: { title: '详情' }
      }
    ]
  },
  {
    path: "/cow_market/index",
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/cow_market/operation/editInfo/:id(\\d+)',
        component: () => import('@/views/xmb/cow_market/detail.vue'),
        name: 'editInfo',
        meta: { title: '编辑' }
      }
    ]
  },
  {
    path: "/cow_market/index",
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/cow_market/operation/examine/:id(\\d+)',
        component: () => import('@/views/xmb/cow_market/detail.vue'),
        name: 'examine',
        meta: { title: '审核' }
      }
    ]
  },
  {
    path: "/cow_market/marketAdmin",
    component: Layout,
    hidden: true,
  },


  {
    path: "/pasture/index",
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/pasture/details/animalList/:id(\\d+)',
        component: () => import('@/views/xmb/pasture/details/animalList.vue'),
        name: 'animalListInfo1',
        meta: { title: '活畜信息' }
      },
      {
        path: '/pasture/liveAnimalList',
        component: () => import('@/views/xmb/pasture/liveAnimalList.vue'),
        name: 'animalListItem',
        meta: { title: '活畜信息' }
      },
      {
        path: '/pasture/penList',
        component: () => import('@/views/xmb/pasture/pasturePen.vue'),
        name: 'penList',
        meta: { title: '圈舍管理' }
      },
      {
        path: '/pasture/monitorList',
        component: () => import('@/views/xmb/pasture/monitor.vue'),
        name: 'monitorList',
        meta: { title: '视频监控' }
      },
      {
        path: '/pasture/goods',
        component: () => import('@/views/xmb/pasture/goods.vue'),
        name: 'goodsList',
        meta: { title: '货品信息' }
      },
    ]
  },
  {
    path: "/cow_market/orders",
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/cow_market/orders/detail',
        component: () => import('@/views/xmb/cow_market/orders/detail.vue'),
        name: 'cowMarketOrdersDetail',
        meta: { title: '订单详情' }
      },
      {
        path: '/cow_market/orders/prepare',
        component: () => import('@/views/xmb/cow_market/orders/prepare.vue'),
        name: 'cowMarketOrdersPrepare',
        meta: { title: '准备货品' }
      },
    ]
  },
  {
    path: "/cow_market/batchList",
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/cow_market/batch/shop_allocation',
        component: () => import('@/views/xmb/cow_market/orders/shopAllocation.vue'),
        name: 'shopAllocation',
        meta: { title: '超市分配' }
      },
      {
        path: '/cow_market/batch/shop_allocation_detail',
        component: () => import('@/views/xmb/cow_market/orders/shopAllocationDetail.vue'),
        name: 'shopAllocationDetail',
        meta: { title: '批量订购详情' }
      },
    ]
  },
  {
    path: "/cow_market/longTerm",
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/cow_market/batch/shop_bind',
        component: () => import('@/views/xmb/cow_market/orders/shopBind.vue'),
        name: 'shopBind',
        meta: { title: '超市绑定' }
      },
      {
        path: '/cow_market/batch/shop_bind_detail',
        component: () => import('@/views/xmb/cow_market/orders/shopBindDetail.vue'),
        name: 'shopBindDetail',
        meta: { title: '长期订购详情' }
      },
    ]
  },
];
