import Layout from '@/layout'


// 包邮规则创建
export const rules = [
    {
        path: '/rules/index',
        component: Layout,
        hidden: true,
        children: [
            {
            path: '/rules/index',
            component: () => import('@/views/xmb/feedMall/distributionRules/newRules.vue'),
            name: 'rules',
            meta: { title: '包邮规则', icon: 'user' }
            }
        ]
    },
    {
        path: '/goodsList',
        component: Layout,
        hidden: true,
        children: [
            {
            path: '/goodsList/create',
            component: () => import('@/views/xmb/feedMall/goodsManage/goodsList/create'),
            name: 'GoodsCreate',
            meta: { title: '商品发布', icon: 'user' ,noCache: false    }
            }
        ]
    },

    {
        path: '/goodsList',
        component: Layout,
        hidden: true,
        children: [
            {
            path: '/goodsList/createServer',
            component: () => import('@/views/xmb/feedMall/goodsManage/goodsList/createServer'),
            name: 'ServerCreate',
            meta: { title: '服务发布', icon: 'user' ,noCache: false    }
            }
        ]
    },
    {
        path: '/goodsList',
        component: Layout,
        hidden: true,
        children: [
            {
            path: '/goodsList/Edit',
            component: () => import('@/views/xmb/feedMall/goodsManage/goodsList/create'),
            name: 'GoodsCreate',
            meta: { title: '商品重新上架', icon: 'user' ,noCache: false    }
            }
        ]
    },
    {
        path: '/serversList',
        component: Layout,
        hidden: true,
        children: [
            {
            path: '/serversList/Edit',
            component: () => import('@/views/xmb/feedMall/goodsManage/goodsList/createServer'),
            name: 'ServersCreate',
            meta: { title: '商品重新上架', icon: 'user' ,noCache: false    }
            }
        ]
    },
    {
        path: '/service',
        component: Layout,
        hidden: true,
        children: [
            {
            path: '/service/orderDetail',
            component: () => import('@/views/xmb/feedMall/order/service/orderDetail'),
            name: 'serviceOrderDetail',
            meta: { title: '订单详情页', icon: 'user' ,noCache: true    }
            }
        ]
    },
    {
        path: '/jy_cow',
        component: Layout,
        hidden: true,
        children: [
            {
            path: '/jy_cow/orderDetail',
            component: () => import('@/views/xmb/jy_cow/order/orderManager/orderDetail.vue'),
            name: 'jy_cowOrderDetail',
            meta: { title: '订单详情', icon: 'user' ,noCache: true    }
            }
        ]
    },
    {
        path: '/supplier',
        component: Layout,
        hidden: true,
        children: [
            {
                path: '/supplier/addForm',
                component: () => import('@/views/xmb/supplier/addForm.vue'),
                name: 'capitalAccountAddFrom',
                meta: { title: '添加供应商', icon: 'user' ,noCache: true    }
            }
        ]
    },

]
