import Layout from "@/layout"

export const nlbAdmin = [
  {
    path: "/region/gen-list",
    component: Layout,
    hidden: true,
    permissions: ["region:list:list"]
  }, {
    path: "/market/index",
    component: Layout,
    hidden: true,
    performance: ["market: index"]
  }, {
    path: "/member/index",
    component: Layout,
    hidden: true,
    performance: ["member:list:list"],
    children: [
      {
        path: '/member/detail/:id(\\d+)',
        component: () => import('@/views/nlb/member/list/detail'),
        name: 'memberDetail',
        meta: { title: '会员详情', activeMenu: '/nlb/member' }
      }
    ]
  },
  {
    path: "/index",
    component: Layout,
    hidden: true,
    performance: ["storeManage:list"],
    children: [
      {
        path: '/storeManage/detail/:id(\\d+)',
        component: () => import('@/views/nlb/storeManage/detail'),
        name: 'storeManageDetail',
        meta: { title: '店铺详情', activeMenu: '/nlb/storeManage/storeManage' }
      }
    ]
  },
  {
    path: "/storeManage/storeAudit/index",
    component: Layout,
    hidden: true,
    performance: ["storeManage:storeAudit:list"],
    children: [
      {
        path: '/storeManage/storeAudit/detail/:id(\\d+)',
        component: () => import('@/views/nlb/storeManage/storeAudit/detail'),
        name: 'storeAuditDetail',
        meta: { title: '店铺详情', activeMenu: '/nlb/storeManage/storeAudit' }
      }
    ]
  },
  {
    path: "/storeManage/postManage/index",
    component: Layout,
    hidden: true,
    performance: ["storeManage:postManage:list"],
    children: [
      {
        path: '/storeManage/postManage/detail/:id(\\d+)',
        component: () => import('@/views/nlb/storeManage/postManage/detail'),
        name: 'storeAuditDetail',
        meta: { title: '店铺详情', activeMenu: '/nlb/storeManage/postManage' }
      }
    ]
  },
  {
    path: "/index",
    component: Layout,
    hidden: true,
    performance: ["nlb:commodity:index"],
    children: [
      {
        path: '/commodity/detail/:id(\\d+)',
        component: () => import('@/views/nlb/commodity/detail'),
        name: 'commodityDetail',
        meta: { title: '商品详情', activeMenu: '/nlb/commodity/detail' }
      }
    ]
  },
  {
    path: '/index',
    component: Layout,
    hidden: true,
    performance: ['nlb:order:index'],
    children: [
      {
        path: '/order/detail/:id(\\d+)',
        component: ()=> import('@/views/nlb/order/detail'),
        name: 'orderDetail',
        meta: { title: '订单详情', activeMenu: '/nlb/order/detail' }
      }
    ]
  },{
    path: "/finance",
    component: Layout,
    hidden: true,
    performance: ["finance:collection:index"]
  }, 
]
