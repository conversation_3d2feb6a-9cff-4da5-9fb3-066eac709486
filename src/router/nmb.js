import Layout from "@/layout"

export const nmb = [
    // 需求方成员管理
    {
        path: '/customer/userCustomer',
        component: Layout,
        hidden: true,
        redirect: 'noredirect',
        children: [
            {
                path: '/customer/userCustomer',
                component: () => import('@/views/nmb/userCustomer/index'),
                name: 'userCustomers',
                meta: { title: '成员管理', icon: 'user' }
            }
        ]
    },
    // 需求方成员管理
    {
        path: '/supervisor/userSupervisor',
        component: Layout,
        hidden: true,
        redirect: 'noredirect',
        children: [
            {
                path: '/supervisor/userSupervisor',
                component: () => import('@/views/nmb/userSupervisor/index'),
                name: 'userSupervisor',
                meta: { title: '成员管理', icon: 'user' }
            }
        ]
    },
  // 牛贸易公司成员管理
  {
    path: '/company/userCompany',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: '/company/userCompany',
        component: () => import('@/views/nmb/userCompany/index'),
        name: 'userCompany',
        meta: { title: '成员管理', icon: 'user' }
      }
    ]
  }
]
