import Layout from "@/layout"

// 担保交易导出明细
export const nlbao = [
  {
    path: "/guarantee/index",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "/guarantee/exportRecord",
        component: () => import("@/views/nlbao/guarantee/exportRecord"),
        name: "exportRecord",
        meta: { title: "导出记录", icon: "user" }
      }
    ]
  },
  {
    path: "/stockist/detail",
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/stockist/detail',
        component: () => import("@/views/nlbao/stockist/detail"),
        name: "stockistDetail",
        meta: { title: "详情", icon: "user" }
      }
    ]
  },
  {
    path: "/operate/examine",
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/operate/examine',
        component: () => import("@/views/nlbao/operate/examine"),
        name: "operatExamine",
        meta: { title: "运营角色审核", icon: "user" }
      }
    ]
  },
  {
    path: "/guarantor/examine",
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/guarantor/examine',
        component: () => import("@/views/nlbao/guarantor/examine"),
        name: "guarantorExamine",
        meta: { title: "服务店铺审核", icon: "user" }
      }
    ]
  },
  {
    path: "/bank/recordExamine",
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/bank/recordExamine',
        component: () => import("@/views/nlbao/bank/recordExamine"),
        name: "bankRecordExamine",
        meta: { title: "银行审核", icon: "user" }
      }
    ]
  },
  // 银行导出记录
  {
    path: "/bank/record",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "/bank/orderExport",
        component: () => import("@/views/nlbao/bank/orderExport"),
        name: "bankExportRecord",
        meta: { title: "导出记录", icon: "user" }
      }
    ]
  },
  {
    path: "/enterprise/addList",
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/enterprise/addList',
        component: () => import("@/views/nlbao/enterprise/addLIst"),
        name: "addList",
        meta: { title: "担保设置", icon: "user" ,keepAlive: true},
      }
    ]
  },
  // 运营角色还款/消费记录
  {
    path: "/operate/repaymentInfo",
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/operate/repaymentInfo',
        component: () => import("@/views/nlbao/operate/repaymentInfo"),
        name: "repaymentInfo",
        meta: { title: "还款记录", icon: "user" }
      }
    ]
  },
  {
    path: "/operate/payInfo",
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/operate/payInfo',
        component: () => import("@/views/nlbao/operate/payInfo"),
        name: "payInfo",
        meta: { title: "消费记录", icon: "user" }
      }
    ]
  },

  // 担保方还款/消费记录
  {
    path: "/guarantor/guarmentInfo",
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/guarantor/guarmentInfo',
        component: () => import("@/views/nlbao/guarantor/guarmentInfo"),
        name: "guarmentInfo",
        meta: { title: "还款记录", icon: "user" }
      }
    ]
  },
  {
    path: "/guarantor/guarpayinfo",
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/guarantor/guarpayinfo',
        component: () => import("@/views/nlbao/guarantor/guarpayinfo"),
        name: "guarpayinfo",
        meta: { title: "消费记录", icon: "user" }
      }
    ]
  },

  // 银行还款/消费记录
  {
    path: "/bank/bankrepay",
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/bank/bankrepay',
        component: () => import("@/views/nlbao/bank/bankrepay"),
        name: "bankrepay",
        meta: { title: "还款记录", icon: "user" }
      }
    ]
  },
  {
    path: "/bank/bankpayinfo",
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/bank/bankpayinfo',
        component: () => import("@/views/nlbao/bank/bankpayinfo"),
        name: "bankpayinfo",
        meta: { title: "消费记录", icon: "user" }
      }
    ]
  },
  // 确认还款
  {
    path: "/operate/enterLoan",
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/operate/enterLoan',
        component: () => import("@/views/nlbao/operate/enterLoan"),
        name: "enterLoan",
        meta: { title: "确认放款", icon: "user" }
      }
    ]
  },
  {
    path: "/operate/enterpriseDetail",
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/operate/enterpriseDetail',
        component: () => import("@/views/nlbao/operate/enterpriseDetail"),
        name: "enterpriseDetail",
        meta: { title: "详情", icon: "user" }
      }
    ]
  },
  {
    path: "/operate/cost",
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/operate/costList',
        component: () => import("@/views/nlbao/operate/costList"),
        name: "cost",
        meta: { title: "设置服务费", icon: "user" }
      }
    ]
  },
  {
    path: "/operate/costDetail",
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/operate/costDetail',
        component: () => import("@/views/nlbao/operate/cost"),
        name: "costDetail",
        meta: { title: "详情", icon: "user" }
      }
    ]
  },
]
