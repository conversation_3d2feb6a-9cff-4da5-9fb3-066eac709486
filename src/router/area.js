import Layout from '@/layout'
export const area = [
    {
        path: '/area/detial',
        component: Layout,
        hidden: true,
        redirect: 'noredirect',
        children: [
            {
            path: '/area/detial',
            component: () => import('@/views/offline/column/area/areaDetail.vue'),
            name: 'areaDetail',
            meta: { title: '区域详情场信息', icon: 'user' }
            }
        ]
    }
]
//畜牧师 牧民列表
export const pastor = [
    {
        path: '/pastor/list',
        component: Layout,
        hidden: true,
        children: [
            {
            path: '/pastor/list',
            component: () => import('@/views/system/stockist/herdsman.vue'),
            name: 'pastor',
            meta: { title: '牧民列表', icon: 'user' }
            }
        ]
    },
    {
        path: '/herdsman/add',
        component: Layout,
        hidden: true,
        children: [
            {
            path: '/herdsman/add',
            component: () => import('@/views/system/stockist/herdsmanAdd.vue'),
            name: 'herdsmanAdd',
            meta: { title: '创建', icon: 'user' }
            }
        ]
    }
]
//结算统计
export const settlement = [
    {
        path: '/profitList/list',
        component: Layout,
        hidden: true,
        children: [
            {
            path: '/profitList/list',
            component: () => import('@/views/xmb/settlement/profitList.vue'),
            name: 'pastor',
            meta: { title: '收益明细', icon: 'user' }
            }
        ]
    },
    {
        path: '/withdrawalList/list',
        component: Layout,
        hidden: true,
        children: [
            {
            path: '/withdrawalList/list',
            component: () => import('@/views/xmb/settlement/withdrawalList.vue'),
            name: 'pastor',
            meta: { title: '提现结算明细', icon: 'user' }
            }
        ]
    }
]
//融安保牧场信息中的活畜信息路由
export const pasture = [
    {
        path: '/pasture',
        component: Layout,
        hidden: true,
        children: [
            {
                path: '/pasture/liveAnimal',
                component: () => import('@/views/ffs/supervisionSheet/siteManagement/liveAnimalList.vue'),
                name: 'liveAnimal',
                meta: { title: '活畜信息', icon: 'user', noCache: false }
            },
            {
                path: '/pasture/monitor',
                component: () => import('@/views/ffs/supervisionSheet/siteManagement/monitor.vue'),
                name: 'monitor',
                meta: { title: '视频监控', icon: 'user' }
            },
            {
                path: '/pasture/offLineVideo',
                component: () => import('@/views/ffs/supervisionSheet/siteManagement/offLineVideo.vue'),
                name: 'offLineVideo',
                meta: { title: '离线视频', icon: 'user' }
            },
          {
            path: '/farmingPasture/offLineVideo',
            component: () => import('@/views/ffs/supervisionSheet/siteManagement/farmingOffLineVideo.vue'),
            name: 'offLineVideo',
            meta: { title: '离线视频', icon: 'user' }
          },
            {
                path: '/pasture/pastureNetwork',
                component: () => import('@/views/ffs/supervisionSheet/siteManagement/pastureNetwork.vue'),
                name: 'pastureNetwork',
                meta: { title: '网关管理', icon: 'user', noCache: false }
            },
            {
              path: '/pasture/pen',
              component: () => import('@/views/ffs/supervisionSheet/siteManagement/pasturePen.vue'),
              name: 'pasturePen',
              meta: { title: '圈舍管理', icon: 'user', noCache: false }
            },
            {
              path: '/pasture/bringAdopt',
              component: () => import('@/views/xmb/jy_cow/farm/adopt/index.vue'),
              name: 'bringAdopt',
              meta: { title: '认养管理', icon: 'user', noCache: false }
            }
        ]
    }
]
//获取管理路由地址
export const cargoArea = [
    {
        path: '/cargoArea',
        component: Layout,
        hidden: true,
        children: [
            {
                path: '/cargoArea/cargoAreaList',
                component: () => import('@/views/ffs/mortgage/storeManage/cargoAreaList.vue'),
                name: 'cargoAreaList',
                meta: { title: '货区管理', icon: 'user' }
            },
          {
            path: '/farmingCargoArea/cargoAreaList',
            component: () => import('@/views/ffs/farmingMortgage/storeManage/cargoAreaList.vue'),
            name: 'cargoAreaList',
            meta: { title: '货区管理', icon: 'user' }
          },
            {
                path: '/cargoArea/monitorList',
                component: () => import('@/views/ffs/mortgage/storeManage/monitorList.vue'),
                name: 'monitorList',
                meta: { title: '视频监控', icon: 'user' }
            },
            {
                path: '/farmingCargoArea/monitorList',
                component: () => import('@/views/ffs/farmingMortgage/storeManage/monitorList.vue'),
                name: 'monitorList',
                meta: { title: '视频监控', icon: 'user' }
            },
            {
                path: '/cargoArea/sensorList',
                component: () => import('@/views/ffs/mortgage/storeManage/sensorList.vue'),
                name: 'sensorList',
                meta: { title: '传感器管理', icon: 'user' }
            }
        ]
    }
]
