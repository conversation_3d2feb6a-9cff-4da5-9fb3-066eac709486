<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <script type="text/javascript" src="//api.map.baidu.com/api?type=webgl&v=1.0&ak=HGgAvme7n8KUblKTyyqnZvWStNIRiZFA"></script>
    <script src="<%= BASE_URL %>assets/ezuikit.js"></script>
    <script src="<%= BASE_URL %>assets/base64.min.js"></script>
    <link rel="icon" href="<%= BASE_URL %>favicon.ico">
    <title>
        <%= webpackConfig.name %>
    </title>
    <!--[if lt IE 11]><script>window.location.href='/html/ie.html';</script><![endif]-->
    <style>
        html,
        body,
        #app {
            height: 100%;
            margin: 0px;
            padding: 0px;
        }

        .chromeframe {
            margin: 0.2em 0;
            background: #ccc;
            color: #000;
            padding: 0.2em 0;
        }
        .loading {
            width: 100%;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }

        .loading-box span {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin:0 5px;
            background: #54BFFF;
            animation-name: loading-beat;
            /* 动画完成一个周期所需要的时间 */
            animation-duration: 1.8s;
            /* 定义动画从何时开始（延迟） */
            animation-delay: 0s;
            /* 动画播放次数（无限） */
            animation-iteration-count: infinite;
            opacity: 0.5;
        }

        .loading-box span:nth-child(2) {

            animation-delay: .3s;
        }

        .loading-box span:nth-child(3) {

            animation-delay: .5s;
        }

        .loading-box span:nth-child(4) {
            animation-delay: .8s;

        }
        .loading-box span:nth-child(5) {
            animation-delay: .9s;

        }
        .loading-box span:nth-child(6) {
            animation-delay: 1.1s;

        }


        @keyframes loading-beat {
            0%{
        transform: translateY(0) scale(1);
        }
        25% {
        transform: scale(1.8);
        opacity: 1;
        }
        50%, 75%, 100% {
        transform: translateY(0px) scale(1);
        }
        }

    </style>
</head>

<body>
    <div id="app">
        <div class="loading">
            <div class="loading-box">
                <span></span>
                <span></span>
                <span></span>
                <span></span>
                <span></span>
                <span></span>
            </div>
            <div style="font-size: 14px; margin-top: 20px; color: #BCBCBC;">正在加载中，请耐心等待</div>
        </div>
    </div>
</body>

</html>